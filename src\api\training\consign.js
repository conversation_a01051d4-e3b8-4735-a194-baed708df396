import request from '@/utils/request'

// 查询委培管理列表
export function listWlTrainingDepute(query) {
    return request({
        url: '/railway/wlTrainingDepute/list',
        method: 'get',
        params: query
    })
}

// 查询委培管理详细
export function getWlTrainingDepute(id) {
    return request({
        url: '/railway/wlTrainingDepute/' + id,
        method: 'get'
    })
}

// 新增委培管理
export function addWlTrainingDepute(data) {
    return request({
        url: '/railway/wlTrainingDepute',
        method: 'post',
        data: data
    })
}

// 修改委培管理
export function updateWlTrainingDepute(data) {
    return request({
        url: '/railway/wlTrainingDepute',
        method: 'put',
        data: data
    })
}

// 删除委培管理
export function delWlTrainingDepute(id) {
    return request({
        url: '/railway/wlTrainingDepute/' + id,
        method: 'delete'
    })
}
// 发起委培管理流程
export function postLaunchProcess(data) {
    return request({
        url: '/railway/wlTrainingDepute/launchProcess',
        method: 'post',
        data: data
    })
}
// 下载委培管理审批表
export function getWlTrainingDeputeDownload(id) {
    return request({
        url: '/railway/wlTrainingDepute/download/' + id,
        method: 'get',
        responseType: "blob",
    })
}
