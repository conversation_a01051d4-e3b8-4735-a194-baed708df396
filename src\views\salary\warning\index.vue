<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="项目" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              placeholder="请选择项目"
              clearable
              class="w-[200px]"
              :extraParams="{ parentId: '0' }"
              @change="handleProjectChange"
            />
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <RemoteSelect
              v-model="queryParams.deptId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              placeholder="请选择部门"
              clearable
              class="w-[200px]"
              :extraParams="{ parentId: 9999 }"
            />
          </el-form-item>
          <el-form-item label="时间" prop="yearMonth">
            <el-date-picker
              v-model="monthrangeDate"
              type="monthrange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              class="w-[200px]"
              clearable
            />
          </el-form-item>
          <el-form-item label="上传状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="上传状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in salary_upload_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="salaryList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间" align="center" prop="yearMonth" />
          <el-table-column label="单位名称" align="center" prop="projectName" />
          <el-table-column label="部门名称" align="center" prop="deptName" />
          <el-table-column label="上传状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="salary_upload_status"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column label="发放金额" align="center" prop="totalAmount">
            <template #default="scope">
              <span>{{ scope.row.totalAmount || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="是否发放工资"
            align="center"
            prop="isPaySalary"
            width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.status === '2' ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工资支付书" align="center" prop="payBook">
            <template #default="scope">
              <span
                v-if="
                  !scope.row.annexList1 || scope.row.annexList1.length === 0
                "
                >未上传</span
              >
              <attachment-display v-else :attachments="scope.row.annexList1" />
            </template>
          </el-table-column>
          <el-table-column
            label="工资凭证"
            align="center"
            prop="payVoucher"
            width="200"
          >
            <template #default="scope">
              <div
                v-if="scope.row.annexList2 && scope.row.annexList2.length > 0"
              >
                <attachment-display :attachments="scope.row.annexList2" />
              </div>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="120"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleDetail(scope.row)"
                >查看</el-button
              >
              <el-button
                v-if="scope.row.status === '1'"
                type="text"
                @click="handleUpload(scope.row)"
                >上传工资凭证</el-button
              >
              <el-button
                v-if="scope.row.status === '0'"
                type="text"
                @click="handleProcess(scope.row)"
                >处理</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <el-dialog
      :title="'处理薪酬'"
      v-model="processDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="processFormRef"
        :model="processForm"
        label-width="120px"
        :rules="processFormRules"
      >
        <!-- <el-form-item label="是否发放工资" prop="isPaySalary">
          <el-radio-group v-model="processForm.isPaySalary">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="工资支付书" prop="payBook">
          <file-upload
            drag
            v-model="processForm.payBook"
            :limit="1"
            :isShowTip="true"
            :fileSize="60"
            :fileType="['xls', 'xlsx']"
          >
            <div class="el-upload__tip custom-upload-tip">
              <el-icon size="30px"><upload-filled /></el-icon>
              <span>仅允许导入xls、xlsx格式文件。</span>
              <el-link
                type="primary"
                :underline="false"
                style="font-size: 12px; vertical-align: baseline"
                @click="handleDownloadTemplate"
                >下载模板</el-link
              >
            </div>
          </file-upload>
        </el-form-item>
        <!-- <el-form-item
          label="工资凭证"
          prop="payVoucher"
          v-if="processForm.isPaySalary === '1'"
        >
          <file-upload
            v-model="processForm.payVoucher"
            :limit="1"
            :fileType="['jpg', 'png', 'jpeg']"
            :fileSize="30"
          />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processDialogVisible = false" class="cancel-btn"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submitProcess"
            :loading="submitLoading"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 上传工资凭证弹窗 -->
    <el-dialog
      title="上传工资凭证"
      v-model="uploadVoucherDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="voucherFormRef"
        :model="voucherForm"
        label-width="120px"
        :rules="voucherFormRules"
      >
        <el-form-item label="工资凭证" prop="payVoucher">
          <file-upload
            v-model="voucherForm.payVoucher"
            :limit="1"
            :fileType="['jpg', 'png', 'jpeg']"
            :fileSize="30"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="uploadVoucherDialogVisible = false"
            class="cancel-btn"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submitVoucher"
            :loading="submitVoucherLoading"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import {
  listProjectOrgSalaryWarn,
  processSalary,
  saveVoucher,
} from '@/api/salary/projectOrgSalary';
import FileUpload from '@/components/FileUpload';
import { useRouter } from 'vue-router';
import AttachmentDisplay from '@/components/AttachmentDisplay/index.vue';
import RemoteSelect from '@/components/RemoteSelect';
const { proxy } = getCurrentInstance();

// 字典数据
const { salary_upload_status } = proxy.useDict('salary_upload_status');

// 遮罩层
const loading = ref(false);
// 总条数
const total = ref(0);
// 薪酬表格数据
const salaryList = ref([]);
// 显示搜索条件
const showSearch = ref(true);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  yearMonth: undefined,
  status: undefined,
  projectId: '',
  deptId: '',
});

const processFormRules = ref({
  isPaySalary: [{ required: true, message: '请选择是否发放工资' }],
  payBook: [{ required: true, message: '请上传工资支付书' }],
  payVoucher: [{ required: true, message: '请上传工资凭证' }],
});

const processDialogVisible = ref(false);
const processFormRef = ref(null);
const processForm = ref({
  isPaySalary: '1',
  payBook: [],
  payVoucher: [],
});
let currentRow = null;

// 上传工资凭证弹窗
const uploadVoucherDialogVisible = ref(false);
const voucherFormRef = ref(null);
const voucherForm = ref({
  payVoucher: [],
  salaryId: null,
});
const voucherFormRules = ref({
  payVoucher: [{ required: true, message: '请上传工资凭证' }],
});
const submitLoading = ref(false);
const submitVoucherLoading = ref(false);
const monthrangeDate = ref([]);
const router = useRouter();

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  monthrangeDate.value = [];
  handleQuery();
}
/** 查询薪酬列表 */
function getList() {
  loading.value = true;
  const [beginMonth, endMonth] = monthrangeDate.value;
  listProjectOrgSalaryWarn({ ...queryParams.value, beginMonth, endMonth }).then(
    (response) => {
      response.rows.map((item) => {
        item.annexList1 = item.annexList?.filter((i) => i.type === '0');
        item.annexList2 = item.annexList?.filter((i) => i.type === '1');
      });
      salaryList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlProjectOrgSalary/warn/export',
    {
      ...queryParams.value,
    },
    `薪酬预警数据_${new Date().getTime()}.xlsx`
  );
}

function handleProcess(row) {
  currentRow = row;
  processForm.value = {
    isPaySalary: row.isPaySalary || '1',
    payBook: [],
    payVoucher: [],
    salaryId: row.id,
  };
  processDialogVisible.value = true;
}

function submitProcess() {
  processFormRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      const payBooks = processForm.value.payBook?.map((item) => ({
        ...item,
        type: 0,
      }));
      const payVoucher = processForm.value.payVoucher?.map((item) => ({
        ...item,
        type: 1,
      }));
      const data = {
        salaryId: processForm.value.salaryId,
        annexList: [...payBooks, ...payVoucher],
      };
      processSalary(data)
        .then((response) => {
          proxy.$modal.msgSuccess('处理成功');
          processDialogVisible.value = false;
          getList();
        })
        .finally(() => {
          submitLoading.value = false;
        });
    }
  });
}
// 详情

function handleDetail(row) {
  localStorage.setItem('salaryBaseInfo', JSON.stringify(row));
  router.push({
    path: '/salary/projectOrgSalary/details',
    query: { id: row.id },
    meta: { activeMenu: location.pathname },
  });
}

// 上传工资凭证
function handleUpload(row) {
  currentRow = row;
  voucherForm.value = {
    payVoucher: [],
    salaryId: row.id,
  };
  uploadVoucherDialogVisible.value = true;
}

// 提交工资凭证
function submitVoucher() {
  voucherFormRef.value.validate((valid) => {
    if (valid) {
      submitVoucherLoading.value = true;
      const payVoucher = voucherForm.value.payVoucher?.map((item) => ({
        ...item,
        type: 1,
        objectId: voucherForm.value.salaryId,
      }));
      const data = {
        salaryId: voucherForm.value.salaryId,
        ...payVoucher[0],
      };
      saveVoucher(data)
        .then((response) => {
          proxy.$modal.msgSuccess('上传成功');
          uploadVoucherDialogVisible.value = false;
          getList();
        })
        .finally(() => {
          submitVoucherLoading.value = false;
        });
    }
  });
}
// 项目选择后触发

function handleProjectChange(project) {
  if (project && project.deptId) {
    queryParams.value.deptId = undefined;
  } else {
    queryParams.value.projectId = undefined;
    queryParams.value.deptId = undefined;
  }
}

/**
 * 下载模板操作
 */
function handleDownloadTemplate() {
  proxy.download(
    'railway/salaryDetails/importTemplate',
    {},
    `${'工资支付书'}_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
<style scoped>
.custom-upload-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
