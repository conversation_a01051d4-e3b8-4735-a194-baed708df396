<template>
  <div class="certificate-info">
    <!-- <div class="flex justify-between mb-4">
      <div class="font-bold text-xl">证书信息</div>
    </div> -->

    <el-form class="mb20" :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="证书编号" prop="certificateCode">
        <el-input
          v-model="queryParams.certificateCode"
          placeholder="请输入证书编号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <!-- <el-form-item label="证书类型" prop="certificateType">
          <el-select
            v-model="queryParams.certificateType"
            placeholder="请选择证书类型"
            clearable
            style="width: 200px"
          >
            <el-option  v-for="dict in certificate_type"
                :key="dict.value"
                :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb4">
      <!-- 只保留导出按钮 -->
      <el-col :span="1.5">
        <el-button class="custom-btn" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>

    <el-table :data="certificateList" style="width: 100%" border>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="certificateCode" label="证书编号" align="center" />
      <!-- <el-table-column
        prop="certificateType"
        label="证书类型"
        align="center"
      >
            <template #default="scope"> 
              <dict-tag
                :options="certificate_type"
                :value="scope.row.certificateType"
              />
            </template>
      </el-table-column> -->
      <el-table-column prop="createTime" label="发证时间" align="center" />
      <el-table-column prop="issueOrgan" label="发证机关" align="center" />
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)"
            >查看</el-button
          >
          <!-- <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getWlStaffInfoCertificate } from "@/api/personnelFiles/incumbentEmployee";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const queryForm = ref(null);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  certificateCode: null,
  certificateType: null,
});

const total = ref(0);
const certificateList = ref([]);

/** 查询证书信息 */
function getList() {
  if (route.query.id) {
    getWlStaffInfoCertificate(queryParams.value).then((response) => {
      if (response.code === 200) {
        certificateList.value = response.rows || [];
        total.value = response.total;
      }
    });
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  queryParams.value.staffId = route.query.id;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryForm.value?.resetFields();
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  // TODO: 实现导出功能
  if (route.query.id) {
    proxy.download(
      `/railway/wlStaffInfo/certificate/export`,
      {
        ...queryParams.value,
      },
      `个人证书信息_${new Date().getTime()}.xlsx`
    );
  }
}

/** 查看按钮操作 */
function handleView(row) {
  if (row.certificateType == "2") {
    router.push({
      path: "/certificate/specialAudit/detail",
      query: {
        id: row.id,
        view: true,
      },
      meta: { activeMenu: location.pathname },
    });
  } else {
    router.push({
      path: "/certificate/certificateEnroll/form",
      query: {
        id: row.id,
        view: true,
      },
    });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  ElMessageBox.confirm("是否确认删除该证书信息？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // TODO: 实现删除功能
      ElMessage.success("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  queryParams.value.staffId = route.query.id;
  getList();
});
</script>

<style scoped lang="scss">
.certificate-info {
  padding: 20px;
}

.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}

.search-box {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
