<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="人员名称" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.staffName"
              placeholder="请输入人员名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="单位名称" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择单位"
            ></RemoteSelect>
          </el-form-item>
          <el-form-item label="岗位" prop="postName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.postName"
              placeholder="请输入岗位名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="奖惩类型" prop="type">
            <el-select
              class="w-[200px]"
              v-model="queryParams.type"
              placeholder="请选择奖惩类型"
              clearable
            >
              <el-option
                v-for="dict in reward_punishment_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" type="primary" @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="rewardPunishmentList">
          <el-table-column label="姓名" align="center" prop="staffName" />
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="所在单位" align="center" prop="projectName" />
          <el-table-column label="岗位" align="center" prop="postName" />
          <el-table-column label="奖惩类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag
                :options="reward_punishment_type"
                :value="scope.row.type"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="描述"
            align="center"
            prop="remark"
            show-overflow-tooltip
          />
          <el-table-column
            label="附件"
            align="center"
            prop="wlAnnexes"
            width="120"
          >
            <template #default="scope">
              <attachment-display
                :attachments="scope.row.wlAnnexes"
                mode="compact"
                @preview="handleAttachmentPreview"
                @download="handleAttachmentDownload"
              />
            </template>
          </el-table-column>

          <el-table-column
            label="执行时间"
            align="center"
            prop="executeDate"
            width="180"
          />
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="180"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或修改奖惩处罚对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="700px"
      append-to-body
      destroy-on-close
    >
      <el-form
        ref="rewardPunishmentForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="奖惩类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择奖惩类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in reward_punishment_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="执行时间" prop="executeDate">
          <el-date-picker
            v-model="form.executeDate"
            type="date"
            placeholder="请选择执行时间"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="奖惩人员" prop="staffIds" trigger="change">
          <user-select v-model="form.staffIds" multiple style="width: 100%" />
        </el-form-item>
        <el-form-item label="附件" prop="wlAnnexes">
          <file-upload v-model="form.wlAnnexes" :limit="10" />
        </el-form-item>
        <el-form-item label="奖惩描述" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入奖惩描述"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" class="cancel-btn">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看奖惩处罚对话框 -->
    <el-dialog
      title="查看奖惩处罚"
      v-model="viewOpen"
      width="700px"
      append-to-body
      destroy-on-close
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">{{
          viewForm.staffName
        }}</el-descriptions-item>
        <el-descriptions-item label="性别">
          <dict-tag :options="sys_user_sex" :value="viewForm.gender" />
        </el-descriptions-item>
        <el-descriptions-item label="所在单位">{{
          viewForm.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="岗位">{{
          viewForm.postName
        }}</el-descriptions-item>
        <el-descriptions-item label="奖惩类型">
          <dict-tag :options="reward_punishment_type" :value="viewForm.type" />
        </el-descriptions-item>
        <el-descriptions-item label="执行时间">{{
          viewForm.executeDate
        }}</el-descriptions-item>
        <el-descriptions-item label="附件" :span="2">
          <attachment-display :attachments="viewForm.wlAnnexes" />
        </el-descriptions-item>
        <el-descriptions-item label="奖惩描述" :span="2">
          {{ viewForm.remark }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="viewOpen = false"
            >关 闭</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  listRewardPunishment,
  getRewardPunishment,
  addRewardPunishment,
  updateRewardPunishment,
  delRewardPunishment,
} from "@/api/personnelFiles/rewardPunishment";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
const { proxy } = getCurrentInstance();

// 字典数据
const { sys_user_sex, reward_punishment_type } = proxy.useDict(
  "sys_user_sex",
  "reward_punishment_type"
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 奖惩处罚表格数据
const rewardPunishmentList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示查看弹出层
const viewOpen = ref(false);
// 查看表单
const viewForm = ref({});
// 表单参数
const form = ref({
  id: undefined,
  staffIds: [],
  type: undefined,
  executeDate: undefined,
  remark: undefined,
  wlAnnexes: [],
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: undefined,
  projectId: undefined,
  postId: undefined,
  type: undefined,
});

// 表单校验规则
const rules = ref({
  wlAnnexes: [{ required: true, message: "附件不能为空", trigger: "change" }],
  type: [{ required: true, message: "奖惩类型不能为空", trigger: "change" }],
  staffIds: [
    { required: true, message: "奖惩人员不能为空", trigger: "change" },
  ],
  executeDate: [
    { required: true, message: "执行时间不能为空", trigger: "change" },
  ],
  remark: [{ required: true, message: "奖惩描述不能为空", trigger: "change" }],
});

/** 查询奖惩处罚列表 */
function getList() {
  loading.value = true;
  listRewardPunishment(queryParams.value).then((response) => {
    rewardPunishmentList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  resetForm();
}

/** 表单重置 */
function resetForm() {
  form.value = {
    id: undefined,
    staffIds: undefined,
    type: undefined,
    executeDate: undefined,
    remark: undefined,
    wlAnnexes: [],
  };
  proxy.resetForm("rewardPunishmentForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  resetForm();
  open.value = true;
  title.value = "添加奖惩处罚";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["rewardPunishmentForm"].validate((valid) => {
    if (valid) {
      const data = {
        ...form.value,

        staffs: [],
      };
      form.value.staffIds.forEach((e) => {
        let val = {
          staffId: e,
        };
        data.staffs.push(val);
      });
      delete data.staffIds;
      if (form.value.id != undefined) {
        updateRewardPunishment(data).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRewardPunishment(data).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 查看按钮操作 */
function handleView(row) {
  getRewardPunishment(row.id).then((response) => {
    viewForm.value = response.data;
    viewForm.value = { ...viewForm.value, ...response.data.staffs?.[0] };
    viewOpen.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  resetForm();
  const id = row.id || row.params.id;
  getRewardPunishment(id).then((response) => {
    response.data.staffIds = response.data.staffs?.map((e) => e.staffId);
    form.value = response.data;
    open.value = true;
    title.value = "修改奖惩处罚";
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm("是否确认删除奖惩处罚记录?")
    .then(function () {
      return delRewardPunishment(row.id, row.staffId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "railway/wlBonusPenalty/export",
    {
      ...queryParams.value,
    },
    `奖惩处罚_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
