<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div>
          <span>{{ title }}</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        :disabled="isView"
      >
        <!-- 合同信息 -->
        <div class="form-section">
          <h3>合同信息</h3>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
              <el-form-item label="合同编号" prop="contractCode">
                <el-input
                  v-model="form.contractCode"
                  placeholder="请输入合同编号"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
              <el-form-item label="合同种类" prop="contractTypeId">
                <RemoteSelect
                  v-model="form.contractTypeId"
                  url="railway/wlType/list"
                  labelKey="typeName"
                  valueKey="id"
                  placeholder="请选择合同种类"
                  :extraParams="{
                    type: 'HT',
                  }"
                  @change="handleContractTypeChange"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
              <el-form-item label="合同类型" prop="contractType">
                <el-select
                  v-model="form.contractType"
                  placeholder="请选择合同类型"
                  clearable
                >
                  <el-option
                    v-for="dict in contract_type"
                    :label="dict.label"
                    :value="dict.value"
                    :key="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              :xl="6"
              v-if="isFixedTerm"
            >
              <el-form-item label="合同年限" prop="duration">
                <el-select v-model="form.duration" placeholder="请选择合同年限">
                  <el-option
                    v-for="dict in contract_duration"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
              <el-form-item label="开始时间" prop="beginTime">
                <el-date-picker
                  v-model="form.beginTime"
                  type="date"
                  placeholder="请选择开始时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  @change="calculateContractEndTime()"
                />
              </el-form-item>
            </el-col>

            <el-col
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              :xl="6"
              v-if="isFixedTerm"
            >
              <el-form-item label="结束日期" prop="endTime">
                <el-date-picker
                  v-model="form.endTime"
                  type="date"
                  placeholder="自动计算"
                  format="YYYY-MM-DD"
                  disabled
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              :xl="6"
              v-if="form.contractType === '3'"
            >
              <el-form-item label="工作任务" prop="jobTask">
                <el-input
                  v-model="form.jobTask"
                  placeholder="请输入工作任务"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
              <el-form-item label="是否有试用期" prop="isTryOut">
                <el-radio-group v-model="form.isTryOut">
                  <el-radio :label="0">否</el-radio>
                  <el-radio :label="1">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <template v-if="form.isTryOut === 1">
              <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                <el-form-item label="试用期月数" prop="tryOutMonth">
                  <el-input-number
                    :controls="false"
                    :min="1"
                    step-strictly
                    v-model="form.tryOutMonth"
                    placeholder="请输入试用期月数"
                    style="width: 100%"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                <el-form-item label="试用开始时间" prop="tryOutBeginTime">
                  <el-date-picker
                    v-model="form.tryOutBeginTime"
                    type="date"
                    placeholder="请选择试用开始时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    @change="calculateTryOutEndDate"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">
                <el-form-item label="试用结束日期" prop="tryOutEndTime">
                  <el-date-picker
                    v-model="form.tryOutEndTime"
                    type="date"
                    placeholder="自动计算"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </div>

        <!-- 根据合同种类显示不同的组件 -->
        <labor-contract
          v-if="isLaborContract"
          :form="form"
          :disabled="isView"
          :personnel_type="personnel_type"
          :sys_user_sex="sys_user_sex"
          @openUserSelect="openUserSelect"
        />

        <labor-dispatch-contract
          v-else
          :form="form"
          :disabled="isView"
          :personnel_type="personnel_type"
          :sys_user_sex="sys_user_sex"
          :work_type="work_type"
          @openUserSelect="openUserSelect"
        />
      </el-form>
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button @click="goBack" class="cancel-btn">取消</el-button>
            <el-button type="primary" @click="submitForm" v-if="!isView"
              >保存</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getContract, addContract, updateContract } from "@/api/contract/index";
import { getCurrentInstance } from "vue";
import RemoteSelect from "@/components/RemoteSelect/index";
import LaborContract from "./components/LaborContract.vue";
import LaborDispatchContract from "./components/LaborDispatchContract.vue";
const { proxy } = getCurrentInstance();
import { parseTime } from "@/utils/welink";
const route = useRoute();
const router = useRouter();

// 字典数据
const {
  contract_duration,
  personnel_type,
  contract_type,
  sys_user_sex,
  work_type,
} = proxy.useDict(
  "contract_duration",
  "personnel_type",
  "contract_type",
  "sys_user_sex",
  "work_type"
);

// 是否为查看模式
const isView = computed(() => {
  return route.query.view === "true";
});

// 标题
const title = computed(() => {
  let _title = "";
  if (isView.value) {
    _title = "查看合同";
  } else if (route.query.id) {
    _title = pageType.value === "renew" ? "续签合同" : "编辑合同";
  } else {
    _title = "新增合同";
  }
  return _title;
});

// 合同ID
const contractId = ref(undefined);
// 类型判断
const pageType = ref(null);
// 表单引用
const formRef = ref(null);
// 人员选择对话框可见性
const userSelectVisible = ref(false);

// 表单数据
const form = ref({
  id: undefined,
  contractCode: "",
  contractTypeId: "10002",
  contractType: undefined,
  duration: undefined,
  beginTime: undefined,
  endTime: undefined,
  projectId: undefined,
  legalPerson: "",
  legalPhone: "",
  address: "",
  staffId: undefined,
  name: "",
  idNumber: "",
  phone: "",
  birthDate: undefined,
  joinCrscDate: undefined,
  staffType: undefined,
  currentAddress: "",
  annexes: [],
  description: "",
  remark: "",
  serviceFeeType: "A",
  normalDayFee: undefined,
  restDayFee: undefined,
  serviceFee: "",
  benefitType: "A",
  handprintFile: "",
  declarationFile: "",
  benefit: "",
  expirationStatus: "0", // 默认为有效
  wlAnnexes1: [],
  wlAnnexes2: [],
  wlAnnexes3: [],
  isTryOut: 0,
  tryOutMonth: undefined,
  tryOutBeginTime: undefined,
  tryOutEndTime: undefined,
  jobWay: undefined,
  jobDayHour: undefined,
  jobWeekDay: undefined,
  jobContent: "",
  jobAddress: "",
  labourRewardWay: undefined,
  labourReward: undefined,
});

// 表单校验规则
const rules = reactive({
  description: [{ required: true, message: "请输入", trigger: "blur" }],
  contractCode: [
    { required: true, message: "请输入合同编号", trigger: "blur" },
  ],
  jobDayHour: [
    {
      required: true,
      message: "请输入",
      trigger: "blur",
    },
  ],
  contacts: [
    {
      required: true,
      message: "请输入",
      trigger: "blur",
    },
  ],
  firstPostalCode: [
    {
      required: true,
      message: "请输入邮政编码",
      trigger: "blur",
    },
  ],
  // 工作内容和工作地点
  jobContent: [{ required: true, message: "请输入工作内容", trigger: "blur" }],
  jobAddress: [{ required: true, message: "请输入工作地点", trigger: "blur" }],

  // 工作时间和休息休假
  jobWay: [{ required: true, message: "请选择工作标准", trigger: "change" }],
  jobDayHour: [
    { required: true, message: "请输入每日工作时长", trigger: "blur" },
  ],
  jobWeekDay: [
    { required: true, message: "请输入每周工作天数", trigger: "blur" },
  ],

  // 劳动报酬
  labourRewardWay: [
    { required: true, message: "请选择执行方式", trigger: "change" },
  ],
  labourReward: [
    { required: true, message: "请输入试用期金额", trigger: "blur" },
  ],
  contractTypeId: [
    { required: true, message: "请选择合同种类", trigger: "change" },
  ],
  contractType: [
    { required: true, message: "请选择合同类别", trigger: "change" },
  ],
  isTryOut: [{ required: true, message: "请选择", trigger: "blur" }],
  jobTask: [{ required: true, message: "请输入工作任务", trigger: "blur" }],
  // 固定期限合同必填项
  duration: [{ required: true, message: "请选择合同年限", trigger: "change" }],
  beginTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endTime: [
    { required: true, message: "请选择结束日期", trigger: "change" },
    { validator: validateEndTime, trigger: "change" },
  ],
  // 试用期相关必填项
  tryOutMonth: [
    { required: true, message: "请输入试用期月数", trigger: "blur" },
  ],
  tryOutBeginTime: [
    { required: true, message: "请选择试用开始时间", trigger: "change" },
  ],
  // 甲方信息必填项
  projectId: [{ required: true, message: "请选择单位", trigger: "change" }],
  legalPerson: [
    { required: true, message: "请输入法定代表人", trigger: "blur" },
  ],
  legalPhone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
  address: [{ required: true, message: "请输入公司地址", trigger: "blur" }],
  // 乙方信息必填项
  staffId: [{ required: true, message: "请选择人员", trigger: "change" }],
  currentAddress: [
    { required: true, message: "请输入现居住地", trigger: "blur" },
  ],
  censusRegister: [
    {
      required: true,
      message: "请输入户籍所在地",
      trigger: "blur",
    },
  ],
  lastPostalCode: [
    {
      required: true,
      message: "请输入邮政编码",
      trigger: "blur",
    },
  ],
  // 劳务派遣合同特有必填项
  serviceFeeType: [
    { required: true, message: "请选择劳动报酬类型", trigger: "change" },
  ],
  serviceFee: [
    { required: true, message: "请输入劳务报酬详情", trigger: "blur" },
  ],
  benefitType: [
    { required: true, message: "请选择福利待遇类型", trigger: "change" },
  ],
  wlAnnexes1: [{ required: true, message: "请上传手印", trigger: "blur" }],
  wlAnnexes2: [{ required: true, message: "请上传声明", trigger: "blur" }],
  wlAnnexes3: [{ required: true, message: "请上传附件", trigger: "blur" }],
  benefit: [
    {
      required: true,
      message: "请输入福利待遇及社会保险详情",
      trigger: "blur",
    },
  ],
});

// 是否为固定期限合同
const isFixedTerm = computed(() => {
  return form.value.contractType === "1"; // 1代表固定期限合同
});

// 是否劳动合同书(天诚佳业)
const isLaborContract = computed(() => {
  return form.value.contractTypeId === "10002";
});
// 验证结束时间必须大于开始时间
function validateEndTime(rule, value, callback) {
  if (form.value.beginTime && value) {
    if (new Date(value) <= new Date(form.value.beginTime)) {
      callback(new Error("结束时间必须大于开始时间"));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
// 根据试用开始时间和试用期月数自动计算试用结束日期
const calculateTryOutEndDate = () => {
  if (form.value.tryOutBeginTime && form.value.tryOutMonth) {
    try {
      const beginDate = new Date(form.value.tryOutBeginTime);
      const endDate = new Date(beginDate);
      // 增加指定的月数
      endDate.setMonth(endDate.getMonth() + parseInt(form.value.tryOutMonth));
      // 减去一天，因为试用期到这一天结束
      endDate.setDate(endDate.getDate() - 1);
      // 格式化日期为 YYYY-MM-DD
      const year = endDate.getFullYear();
      const month = String(endDate.getMonth() + 1).padStart(2, "0");
      const day = String(endDate.getDate()).padStart(2, "0");
      form.value.tryOutEndTime = `${year}-${month}-${day}`;
    } catch (error) {
      console.error("计算试用期结束日期出错:", error);
    }
  }
};
// 根据合同开始时间、合同年限自动计算合同结束日期
const calculateContractEndTime = () => {
  if (form.value.beginTime && form.value.duration) {
    try {
      const beginDate = new Date(form.value.beginTime);
      const endDate = new Date(beginDate);
      // 增加指定的年数
      endDate.setFullYear(
        endDate.getFullYear() + parseInt(form.value.duration)
      );
      // 减去一天，因为合同到这一天结束
      endDate.setDate(endDate.getDate() - 1);
      // 格式化日期为 YYYY-MM-DD
      form.value.endTime = parseTime(endDate, "{y}-{m}-{d}");
    } catch (error) {
      console.log(error);
    }
  }
};

// 获取合同详情
const getContractInfo = () => {
  getContract(contractId.value).then((response) => {
    response.data.wlAnnexes1 =
      response.data.wlAnnexes?.filter((i) => i.type === "0") || [];
    response.data.wlAnnexes2 =
      response.data.wlAnnexes?.filter((i) => i.type === "1") || [];
    response.data.wlAnnexes3 =
      response.data.wlAnnexes?.filter((i) => i.type === "2") || [];
    Object.assign(form.value, response.data);
  });
};

// 处理合同种类变更
const handleContractTypeChange = (val) => {
  // 重置相关字段
  if (val && val.id) {
    form.value.contractTypeId = val.id;
  } else {
    form.value.contractTypeId = undefined;
  }
};

// 打开人员选择对话框
const openUserSelect = () => {
  userSelectVisible.value = true;
};

// 返回
const goBack = () => {
  router.go(-1);
};

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      console.log(form.value);

      const submitData = {
        ...form.value,
        wlAnnexes: [
          ...form.value.wlAnnexes1?.map((i) => ({
            ...i,
            type: "0",
          })),
          ...form.value.wlAnnexes2?.map((i) => ({
            ...i,
            type: "1",
          })),
          ...form.value.wlAnnexes3?.map((i) => ({
            ...i,
            type: "2",
          })),
        ],
      };

      if (pageType.value === "renew") {
        // 续签类型 走新增操作
        delete submitData.id;
      }
      if (submitData.id) {
        updateContract(submitData).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          goBack();
        });
      } else {
        addContract(submitData).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          goBack();
        });
      }
    }
  });
};

// 监听试用期月数变化，重新计算结束日期
watch(
  () => form.value.tryOutMonth,
  () => {
    if (form.value.tryOutBeginTime) {
      calculateTryOutEndDate();
    }
  }
);
// 监听合同年限变化，重新计算结束日期
watch(
  () => form.value.duration,
  () => {
    if (form.value.beginTime) {
      calculateContractEndTime();
    }
  }
);
// 在组件挂载后合并子组件的校验规则
onMounted(() => {
  contractId.value = route.query.id;
  pageType.value = route.query.type;

  if (contractId.value) {
    getContractInfo();
  }
});
</script>

<style scoped>
div /deep/ .el-input-number .el-input__inner {
  text-align: left !important;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-section {
  margin-bottom: 20px;
  padding-top: 20px;
}

.form-section h3 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}

.button-group {
  display: flex;
  gap: 10px;
}
</style>
