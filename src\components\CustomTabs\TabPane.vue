<template>
  <div
    v-if="isActive"
    :class="['tab-pane', { 'is-changing': isChanging }]"
    :key="props.name"
  >
    <slot></slot>
  </div>
</template>

<script setup>
import { inject, onMounted, onUnmounted, computed } from "vue";

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  name: {
    type: [String, Number],
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const customTabs = inject("customTabs");

const isActive = computed(() => {
  return customTabs.activeTab.value === props.name;
});

const isChanging = computed(() => {
  return customTabs.isChanging?.value || false;
});

onMounted(() => {
  customTabs.registerPane({
    name: props.name,
    label: props.label,
    disabled: props.disabled,
  });
});

onUnmounted(() => {
  customTabs.unregisterPane({
    name: props.name,
  });
});
</script>

<style scoped>
.tab-pane {
  padding: 32px 24px;
  background: #ffffff;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-pane.is-changing {
  opacity: 0;
  transform: translateY(8px);
}

/* 为内容区域添加现代化的基础样式 */
.tab-pane:deep(.page-header) {
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f3f4;
}

.tab-pane:deep(.page-title) {
  color: #202124;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.tab-pane:deep(.page-desc) {
  color: #5f6368;
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .tab-pane {
    background: #202124;
  }

  .tab-pane:deep(.page-header) {
    border-bottom-color: #3c4043;
  }

  .tab-pane:deep(.page-title) {
    color: #e8eaed;
  }

  .tab-pane:deep(.page-desc) {
    color: #bdc1c6;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-pane {
    padding: 24px 16px;
    min-height: 400px;
  }

  .tab-pane:deep(.page-title) {
    font-size: 20px;
  }

  .tab-pane:deep(.page-desc) {
    font-size: 14px;
  }
}
</style>
