<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @downloadTemplate="handleDownloadTemplate"
    />
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="合同编号" prop="contractCode">
            <el-input
              class="w-[200px]"
              v-model="queryParams.contractCode"
              placeholder="请输入合同编号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              class="w-[200px]"
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="合同类型" prop="contractType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.contractType"
              placeholder="请选择合同类型"
              clearable
            >
              <el-option
                v-for="dict in contract_type"
                :label="dict.label"
                :value="dict.value"
                :key="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="合同种类" prop="contractTypeId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.contractTypeId"
              url="railway/wlType/list"
              labelKey="typeName"
              valueKey="id"
              :extraParams="{
                type: 'HT',
              }"
              placeholder="请选择合同种类"
              @change="handleContractTypeChange"
            />
          </el-form-item>
          <el-form-item label="合同状态" prop="expirationStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.expirationStatus"
              placeholder="请选择合同状态"
              clearable
            >
              <el-option
                v-for="dict in contract_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="合同年限" prop="duration">
            <el-select
              class="w-[200px]"
              v-model="queryParams.duration"
              placeholder="请选择合同年限"
              clearable
            >
              <el-option
                v-for="dict in contract_duration"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="终止时间" prop="endTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.endTime"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="选择终止时间"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="contractList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="合同编号"
            align="center"
            prop="contractCode"
          />
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="学历" align="center" prop="highestEducation">
            <template #default="scope">
              <dict-tag
                :options="education_type"
                :value="scope.row.highestEducation"
              />
            </template>
          </el-table-column>
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="出生日期"
            align="center"
            prop="birthDate"
            width="100"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.birthDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>

          <el-table-column label="合同种类" align="center" prop="typeName">
          </el-table-column>
          <el-table-column label="合同年限" align="center" prop="duration">
            <template #default="scope">
              <dict-tag
                :options="contract_duration"
                :value="scope.row.duration"
              />
            </template>
          </el-table-column>
          <el-table-column label="合同类型" align="center" prop="contractType">
            <template #default="scope">
              <dict-tag
                :options="contract_type"
                :value="scope.row.contractType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="开始时间"
            align="center"
            prop="beginTime"
            width="100"
          >
            <template #default="scope">
              <span v-if="scope.row.beginTime">{{
                parseTime(scope.row.beginTime, "{y}-{m}-{d}")
              }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            label="结束时间"
            align="center"
            prop="endTime"
            width="100"
          >
            <template #default="scope">
              <span v-if="scope.row.endTime">{{
                parseTime(scope.row.endTime, "{y}-{m}-{d}")
              }}</span>

              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="合同状态" align="center" prop="status" />
          <el-table-column
            label="合同变更、终止、解除情况"
            align="center"
            width="180"
            prop="description"
          >
            <template #default="scope">
              {{ scope.row.description || "--" }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="合同附件" align="center" prop="wlAnnexes">
            <template #default="scope">
              <div v-if="scope.row.wlAnnexes && scope.row.wlAnnexes.length > 0">
                <el-button
                  type="text"
                  v-for="(annex, index) in scope.row.wlAnnexes"
                  :key="index"
                  @click="handleDownload(annex)"
                >
                  {{ annex.name }}
                </el-button>
              </div>
              <span v-else>无</span>
            </template>
          </el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="160"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleRenew(scope.row)"
                >续签</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import { parseTime } from "@/utils/welink";
import { listContract, delContract } from "@/api/contract/index";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const {
  sys_user_sex,
  education_type,
  personnel_type,
  contract_status,
  contract_duration,
  contract_type,
} = proxy.useDict(
  "sys_user_sex",
  "education_type",
  "personnel_type",
  "contract_status",
  "contract_duration",
  "contract_type"
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 合同表格数据
const contractList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  contractCode: undefined,
  name: undefined,
  endTime: undefined,
  contractType: undefined,
  contractTypeId: undefined,
  expirationStatus: undefined,
  duration: undefined,
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref("导入合同数据");
const importUrl = "/railway/wlContract/importData";
const templateUrl = "/railway/wlContract/importTemplate";

/** 查询合同列表 */
function getList() {
  loading.value = true;
  listContract(queryParams.value).then((response) => {
    contractList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  router.push("/contract/index/addOrEdit");
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: "/contract/index/addOrEdit",
    query: { id: row.id, view: true },
    meta: { activeMenu: location.pathname },
  });
}

/** 续签按钮操作 */
function handleRenew(row) {
  // TODO: 实现续签功能
  router.push({
    path: "/contract/index/addOrEdit",
    query: { id: row.id, type: "renew" },
  });
}

/** 下载附件 */
function handleDownload(annex) {
  // TODO: 实现附件下载功能
  proxy.$modal.msgSuccess("附件下载功能待实现");
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "railway/wlContract/export",
    {
      ...queryParams.value,
    },
    `合同信息${new Date().getTime()}.zip`
  );
}
/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true;
}

/** 下载模板操作 */
function handleDownloadTemplate() {
  proxy.download(
    "system/wlContract/importTemplate",
    {},
    `contract_template_${new Date().getTime()}.xlsx`
  );
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  getList();
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.download-link {
  color: #409eff;
  cursor: pointer;
  margin-right: 8px;
  text-decoration: underline;
}
</style>
