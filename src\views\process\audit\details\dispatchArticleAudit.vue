<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="font-bold text-lg">{{ title }}</span>
        </div>
      </template>
      <el-form
        ref="dispatchArticleForm"
        :model="form"
        label-width="120px"
        v-loading="loading"
        disabled
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发文标题" prop="title">
              <span>{{ form.title }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件号" prop="code">
              <span>{{ form.code }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发文抄录类型" prop="type">
              <dict-tag :options="dispatch_article_type" :value="form.type" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.projectNames">
            <el-form-item label="抄送单位" prop="projectId">
              <span>{{ form.projectNames }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="附件" prop="wlAnnexes" v-if="form.wlAnnexes">
          <attachment-display :attachments="form.wlAnnexes" />
        </el-form-item>

        <el-form-item label="消息内容" prop="content">
          <div v-html="form.content" class="rich-text-content"></div>
        </el-form-item>
      </el-form>
      <div class="px-5">
        <div class="flex items-center justify-start">
          <span class="font-bold text-lg mr-2">人员信息</span>
        </div>
        <el-table :data="staffInfo" border class="mt-2">
          <el-table-column
            label="姓名"
            prop="name"
            width="100"
            align="center"
          />
          <el-table-column
            label="身份证号"
            prop="idNumber"
            width="250"
            align="center"
          />
          <el-table-column label="性别" prop="gender" align="center">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column
            label="项目部名称"
            prop="projectName"
            width="200"
            align="center"
          />

          <!-- 根据不同类型显示不同的列 -->
          <JLColumn
            v-if="form.type === 'JL'"
            v-slot="scope"
            :view-mode="true"
          />
          <JOColumn
            v-if="form.type === 'JO'"
            v-slot="scope"
            :view-mode="true"
          />
          <REColumn
            v-if="form.type === 'RE'"
            v-slot="scope"
            :view-mode="true"
          />
          <STColumn
            v-if="form.type === 'ST'"
            v-slot="scope"
            :view-mode="true"
          />
        </el-table>

        <!-- 审核组件 -->
        <ProcessComponent :nodeInfo="nodeInfo" class="mt-4" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getDispatchArticle } from "@/api/documentCenter/dispatchArticle";
import { getBusinessIdByInstanceId } from "@/api/process/audit";
import ProcessComponent from "@/components/ProcessComponent";
import {
  JOColumn,
  JLColumn,
  REColumn,
  STColumn,
} from "@/views/documentCenter/dispatchArticle/components";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
const { proxy } = getCurrentInstance();
const { work_type, professional_title, sys_user_sex } = proxy.useDict(
  "work_type",
  "professional_title",
  "sys_user_sex"
);
// 节点信息
const nodeInfo = ref({});
const optins = {
  JO: "jobList",
  JL: "postList",
  RE: "transferList",
  ST: "skillList",
};
const router = useRouter();
const route = useRoute();

// 信息
const staffInfo = ref([]);

// 字典数据
const { dispatch_article_type } = proxy.useDict("dispatch_article_type");

// 遮罩层
const loading = ref(false);
// 文章ID
const articleId = ref(route.query.id);

// 标题
const title = computed(() => {
  return "发文抄录详情";
});

// 表单参数
const form = ref({
  id: undefined,
  title: undefined,
  code: undefined,
  type: "JL",
  projectId: undefined,
  fileUrl: "",
  content: undefined,
});

/** 获取发文抄录详情 */
function getDetail(id) {
  if (id) {
    loading.value = true;
    getDispatchArticle(id).then((response) => {
      form.value = response.data;
      form.value.fileUrl = response.data?.wlAnnexes[0]?.path;
      staffInfo.value = form.value[optins[form.value.type]];
      staffInfo.value.map((i) => {
        i.name = i.staffName;
      });
      form.value.projectNames = staffInfo.value
        .map((i) => i.projectName)
        ?.join(",");
      loading.value = false;
    });
  }
}

onMounted(async () => {
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  let res = {};
  if (instanceId) {
    res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
    nodeInfo.value = res.data;
  }
  if (id) {
    getDetail(id);
  }
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rich-text-content {
  width: 100%;
  min-height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
}
</style>
