<template>
  <div class="certificate-info">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" ref="queryForm" :model="queryParams" class="mb20">
        <el-form-item label="文件名称:" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入文件名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <el-row v-if="isEdit" :gutter="10" class="mb4">
      <el-col :span="1.5">
        <el-button class="custom-btn" @click="handleAdd">新增</el-button>
      </el-col>
    </el-row>

    <div v-if="fileList.length === 0" class="info-content mt20">
      <Empty text="暂无档案附件信息" />
    </div>
    <!-- 文件列表 -->
    <div v-else class="file-list">
      <el-row :gutter="20">
        <el-col
          v-for="(file, index) in fileList"
          :key="index"
          :xs="24"
          :sm="12"
          :md="12"
          :lg="6"
        >
          <div class="file-card">
            <div class="file-icon">
              <img src="@/assets/images/fileImg.png" alt="" />
            </div>
            <div>
              <div class="file-name truncate" :title="file.name">
                {{ file.name || "暂无" }}
              </div>
              <div class="file-actions">
                <preview-file :url="file.path">
                  <el-button
                    type="primary"
                    size="small"
                    style="margin-right: 12px"
                  >
                    查看
                  </el-button>
                </preview-file>

                <el-button
                  type="primary"
                  size="small"
                  @click="downloadFile(file.path, file.name)"
                >
                  下载
                </el-button>
                <el-button
                  v-if="isEdit"
                  type="warning"
                  class="delBtn"
                  size="small"
                  @click="deleteFile(file)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增文件弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="附件新增"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        :rules="addFormRules"
        ref="addFormRef"
        label-width="110px"
      >
        <el-form-item label="文件名称:" prop="name">
          <el-input
            v-model="addForm.name"
            placeholder="请输入文件名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="文件上传:" prop="path">
          <file-upload
            v-model="addForm.path"
            :drag="true"
            :limit="1"
            :fileSize="60"
            :fileType="fileType"
          ></file-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="cancel-btn" @click="addDialogVisible = false"
            >取 消</el-button
          >
          <el-button type="primary" :loading="btnLoading" @click="confirmAdd"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import {
  listWlAnnexListByStaff,
  addWlAnnex,
  delWlAnnex,
  listWlAnnexGetObjectIds,
} from "@/api/personnelFiles/incumbentEmployee";
import { downloadFile } from "@/utils/welink";
// 搜索表单
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: "",
  type: "",
  objectIds: [],
});

const total = ref(0);
const queryForm = ref(null);
const route = useRoute();
const { proxy } = getCurrentInstance();

// 文件类型选项
const types = [
  { label: "图片", value: "image" },
  { label: "文档", value: "document" },
  { label: "压缩包", value: "archive" },
  { label: "其他", value: "other" },
];
const fileType = ["png", "jpg", "docx", "pptx", "pdf", "xlsx", "rar", "zip"];

// 文件列表数据
const fileList = ref([]);
const btnLoading = ref(false);
const isEdit = route.query.type === "check" ? false : true;
// 搜索功能
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

//
function getList() {
  if (route.query.id) {
    listWlAnnexListByStaff(queryParams.value).then((response) => {
      if (response.code === 200) {
        fileList.value = response.rows || [];
        total.value = response.total;
      }
    });
  }
}

// 重置搜索
const resetQuery = () => {
  queryForm.value?.resetFields();
  handleQuery();
};

// 文件操作
const viewFile = (file) => {
  console.log("查看文件:", file);
};

const deleteFile = (file) => {
  let name = file.name || "暂无";
  let ids = [file.id, file.objectId, route.query.id];
  // 这里应该调用API删除文件
  proxy.$modal
    .confirm('是否确认删除档案附件名称为"' + name + '"的数据项？')
    .then(function () {
      return delWlAnnex(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
};

// 新增文件弹窗相关
const addDialogVisible = ref(false);
const addFormRef = ref(null);
const addForm = ref({
  name: "",
  path: "",
});

const addFormRules = ref({
  name: [{ required: true, message: "请输入文件名称", trigger: "blur" }],
  path: [{ required: true, message: "请上传文件", trigger: "change" }],
});

const handleAdd = () => {
  addDialogVisible.value = true;
  addFormRef.value?.resetFields();
};

const handleFileChange = (file) => {
  addForm.value.file = file.raw;
};

function confirmAdd() {
  addFormRef.value.validate((valid) => {
    if (valid) {
      addForm.value.objectId = route.query.id;
      if (route.query.id) {
        btnLoading.value = true;
        addWlAnnex(addForm.value)
          .then((res) => {
            console.log("res", res);
            btnLoading.value = false;
            proxy.$modal.msgSuccess("新增成功");
            addDialogVisible.value = false;
            getList();
          })
          .catch((err) => {
            btnLoading.value = false;
          });
      }
      // 这里应该调用API上传文件
      // 上传成功后关闭弹窗并刷新列表
    }
  });
}
onMounted(async () => {
  if (route.query.id) {
    let { data } = await listWlAnnexGetObjectIds(route.query.id);
    queryParams.value.objectIds = data;
    getList();
  }
});
</script>

<style scoped lang="scss">
.certificate-info {
  padding: 20px;
}

.file-list {
  padding: 20px 0;
}

.file-card {
  background: #f7faff;
  border-radius: 10px;
  margin-bottom: 30px;
  display: flex;
  padding: 15px 20px;
  align-items: flex-end;
}

.file-icon {
  display: block;
  width: 70px;
  height: 70px;
  margin-right: 15px;
  img {
    width: 70px;
    height: 70px;
  }
}

.file-name {
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  margin-bottom: 14px;
  width: 50%;
}

.file-actions {
  display: flex;

  .el-button {
    width: 63px;
    height: 32px;
    background: #dbe4f5;
    border-radius: 16px;
    font-weight: 400;
    font-size: 16px;
    color: #0b388d;
    border-width: 0;
  }
  .delBtn {
    background: #f8e4de;

    color: #eb4815;
  }
}

.upload-area {
  width: 100%;
}

.el-upload__tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}
</style>
