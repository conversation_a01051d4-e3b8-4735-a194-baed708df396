<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :inline="true"
          :disabled="disabled"
        >
          <div class="w-full">
            <p class="relative font-bold">基础信息</p>
          </div>
          <el-form-item v-if="!disabled">
            <user-select
              class="w-[200px]"
              v-model="form.staffId"
              placeholder="请选择人员"
              @change="handleUserChange"
            />
          </el-form-item>
          <el-form-item label="员工姓名" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="form.staffName"
              placeholder="请输入员工姓名"
              disabled
            />
          </el-form-item>
          <el-form-item label="部门">
            <el-input
              class="w-[200px]"
              v-model="form.deptName"
              placeholder="请输入部门名称"
              disabled
            />
          </el-form-item>
          <el-form-item label="岗位">
            <el-input
              class="w-[200px]"
              v-model="form.postName"
              placeholder="请输入岗位名称"
              disabled
            />
          </el-form-item>
          <el-form-item label="人员类型">
            <el-select
              v-model="form.staffType"
              placeholder="请选择人员类型"
              class="w-[200px]"
              disabled
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属单位">
            <el-input
              v-model="form.projectName"
              placeholder="请输入所属单位"
              disabled
            />
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input
              class="w-[200px]"
              v-model="form.idNumber"
              placeholder="请输入身份证号"
              disabled
            />
          </el-form-item>

          <div class="w-full">
            <p class="relative font-bold">证书信息</p>
          </div>
          <el-form-item label="证书编号" prop="certificateCode">
            <el-input
              v-model="form.certificateCode"
              class="w-[200px]"
              placeholder="请输入证书编号"
              :disabled="isExtend"
            />
          </el-form-item>
          <el-form-item label="证书名称" prop="certificateName">
            <el-input
              v-model="form.certificateName"
              class="w-[200px]"
              placeholder="请输入证书名称"
              :disabled="isExtend"
            />
          </el-form-item>
          <!-- <el-form-item label="证书类型" prop="certificateType">
            <el-radio-group v-model="form.certificateType">
              <el-radio
                v-for="dict in certificate_type"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="证书类别" prop="certificateCategory">
            <el-select
              v-model="form.certificateCategory"
              placeholder="请选择证书类别"
              class="w-[200px]"
              :disabled="isExtend"
            >
              <el-option
                v-for="dict in certificate_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="主专业" prop="certificateMajor">
            <el-input
              class="w-[200px]"
              v-model="form.certificateMajor"
              placeholder="请输入主专业"
              :disabled="isExtend"
            />
          </el-form-item>
          <el-form-item label="签发日期" prop="signDate">
            <el-date-picker
              v-model="form.signDate"
              type="date"
              placeholder="选择签发日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-[200px]"
              :disabled="isExtend"
            />
          </el-form-item>
          <el-form-item label="主专业有效期至" prop="effectiveDate">
            <el-date-picker
              v-model="form.effectiveDate"
              type="date"
              placeholder="选择主专业有效期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item
            label="主专业延期注册有效期至"
            prop="extensionDate"
            v-if="isExtend"
          >
            <el-date-picker
              v-model="form.extensionDate"
              type="date"
              placeholder="选择延期注册有效期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="注册号码" prop="registerCode">
            <el-input
              class="w-[200px]"
              v-model="form.registerCode"
              placeholder="请输入注册号码"
              :disabled="isExtend"
            />
          </el-form-item>
          <el-form-item label="注册单位" prop="registerUnit">
            <el-input
              class="w-[200px]"
              v-model="form.registerUnit"
              placeholder="请输入注册单位"
              :disabled="isExtend"
            />
          </el-form-item>
          <el-form-item label="发证机关" prop="issueOrgan">
            <el-input
              class="w-[200px]"
              v-model="form.issueOrgan"
              placeholder="请输入发证机关"
              :disabled="isExtend"
            />
          </el-form-item>

          <el-form-item label="主专业是否参加继续教育" prop="isContinue">
            <el-radio-group v-model="form.isContinue" :disabled="isExtend">
              <el-radio
                v-for="dict in sys_yes_no"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="证书保管处" prop="depositary">
            <el-input
              class="w-[200px]"
              v-model="form.depositary"
              placeholder="请输入证书保管处"
              :disabled="isExtend"
            />
          </el-form-item>
          <div class="w-full">
            <el-form-item
              label="附件上传"
              prop="wlAnnexes"
              class="w-full"
              v-if="!disabled"
            >
              <file-upload
                :disabled="isExtend"
                v-model="form.wlAnnexes"
                :limit="10"
                :fileSize="60"
                :fileType="['doc', 'docx', 'pdf']"
              ></file-upload>
            </el-form-item>
            <el-form-item
              label="附件上传"
              prop="wlAnnexes"
              class="w-full flex items-center"
              v-else
            >
              <attachment-display :attachments="form.wlAnnexes" />
            </el-form-item>
          </div>
          <div class="w-full">
            <el-form-item label="备注" class="w-full">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入备注"
                class="w-full"
                rows="4"
                :disabled="isExtend"
              ></el-input>
            </el-form-item>
          </div>
          <div class="w-full" v-if="!isExtend">
            <el-table
              v-if="form.adds && form.adds.length > 0"
              :data="form.adds"
              style="width: 100%"
              border
            >
              <el-table-column
                type="index"
                label="序号"
                width="60"
                align="center"
              ></el-table-column>
              <el-table-column
                label="增项专业"
                prop="addMajor"
              ></el-table-column>
              <el-table-column label="专业有效期至">
                <template #default="scope">
                  <span>{{
                    parseTime(scope.row.addEffectiveDate, "{y}-{m}-{d}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="延期注册有效期至">
                <template #default="scope">
                  <span>{{
                    parseTime(scope.row.addExtensionDate, "{y}-{m}-{d}")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="是否参加继续教育">
                <template #default="scope">
                  <dict-tag
                    :options="sys_yes_no"
                    :value="scope.row.isContinue"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" v-if="!disabled">
                <template #default="scope">
                  <el-button type="text" @click="handleDeleteAdd(scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <div class="mt-3" v-if="!disabled">
              <el-form
                :model="addForm"
                :rules="addRules"
                ref="addFormRef"
                :inline="true"
              >
                <el-form-item label="增项专业" prop="addMajor">
                  <el-input
                    class="w-[200px]"
                    v-model="addForm.addMajor"
                    placeholder="请输入增项专业"
                  />
                </el-form-item>
                <el-form-item label="是否参加继续教育" prop="isContinue">
                  <el-radio-group v-model="addForm.isContinue">
                    <el-radio
                      v-for="dict in sys_yes_no"
                      :key="dict.value"
                      :label="dict.value"
                      >{{ dict.label }}</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="专业有效期至" prop="addEffectiveDate">
                  <el-date-picker
                    v-model="addForm.addEffectiveDate"
                    type="date"
                    placeholder="选择主专业有效期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-[200px]"
                  />
                </el-form-item>
                <el-form-item label="延期注册有效期至" prop="addExtensionDate">
                  <el-date-picker
                    v-model="addForm.addExtensionDate"
                    type="date"
                    placeholder="选择延期注册有效期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-[200px]"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="handleAddItem"
                    >添加</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-form>

        <!-- 将操作按钮移出表单，确保不受表单禁用状态影响 -->
        <div class="mt-10">
          <el-button
            type="primary"
            :loading="loading"
            @click="submitForm"
            v-if="!disabled"
            >{{ isExtend ? "保存延期" : "保 存" }}</el-button
          >
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { parseTime } from "@/utils/welink";
import {
  getCertificateEnroll,
  addCertificateEnroll,
  updateCertificateEnroll,
} from "@/api/certificate/enroll";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 加载状态
const loading = ref(false);
// 是否禁用表单
const disabled = ref(false);
// 是否为延期模式
const isExtend = computed(() => {
  return route.query.extend === "true";
});

// 字典数据
const {
  personnel_type,
  certificate_type,
  certificate_category,
  certificate_status,
  sys_yes_no,
} = proxy.useDict(
  "personnel_type",
  "certificate_type",
  "certificate_category",
  "certificate_status",
  "sys_yes_no"
);

// 表单参数
const formRef = ref();
const form = ref({
  id: undefined,
  staffId: undefined,
  staffName: undefined,
  staffStatus: "0", // 默认在职
  idNumber: undefined,
  staffType: undefined,
  projectName: undefined,
  postName: undefined,
  deptName: undefined,
  certificateCode: undefined,
  certificateType: undefined,
  certificateCategory: undefined,
  certificateMajor: undefined,
  signDate: undefined,
  effectiveDate: undefined,
  extensionDate: undefined,
  registerCode: undefined,
  registerUnit: undefined,
  issueOrgan: undefined,
  isContinue: "Y", // 默认是
  depositary: undefined,
  expirationStatus: "0", // 默认有效
  warnLevel: undefined,
  adds: [],
  wlAnnexes: [],
});

// 表单校验规则
const rules = ref({
  registerCode: [
    { required: true, message: "注册号码不能为空", trigger: "blur" },
  ],
  registerUnit: [
    { required: true, message: "注册单位不能为空", trigger: "blur" },
  ],
  issueOrgan: [
    { required: true, message: "发证机关不能为空", trigger: "blur" },
  ],
  isContinue: [{ required: true, message: "请选择", trigger: "blur" }],

  certificateMajor: [
    { required: true, message: "主专业不能为空", trigger: "blur" },
  ],

  wlAnnexes: [{ required: true, message: "附件不能为空", trigger: "blur" }],
  staffName: [{ required: true, message: "员工姓名不能为空", trigger: "blur" }],
  idNumber: [
    { required: true, message: "身份证号不能为空", trigger: "blur" },
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: "请输入正确的身份证号码",
      trigger: "blur",
    },
  ],
  staffType: [
    { required: true, message: "人员类型不能为空", trigger: "change" },
  ],
  projectName: [
    { required: true, message: "所属单位不能为空", trigger: "blur" },
  ],
  postName: [{ required: true, message: "岗位名称不能为空", trigger: "blur" }],
  certificateCode: [
    { required: true, message: "证书编号不能为空", trigger: "blur" },
  ],
  certificateName: [
    { required: true, message: "证书名称不能为空", trigger: "blur" },
  ],
  certificateType: [{ required: true, message: "请选择", trigger: "change" }],
  certificateCategory: [
    { required: true, message: "证书类别不能为空", trigger: "change" },
  ],
  certificateMajor: [
    { required: true, message: "主专业不能为空", trigger: "blur" },
  ],
  signDate: [
    { required: true, message: "签发日期不能为空", trigger: "change" },
  ],
  effectiveDate: [
    { required: true, message: "主专业有效期不能为空", trigger: "change" },
  ],
  extensionDate: [
    {
      required: isExtend.value,
      message: "延期注册有效期不能为空",
      trigger: "change",
    },
  ],
});

// 增项对话框参数
const addFormRef = ref();
const addForm = ref({
  addMajor: undefined,
  addEffectiveDate: undefined,
  addExtensionDate: undefined,
  isContinue: "0",
});
const addRules = ref({
  addMajor: [{ required: true, message: "专业不能为空", trigger: "blur" }],
  addEffectiveDate: [
    { required: true, message: "有效期不能为空", trigger: "change" },
  ],
  addExtensionDate: [
    { required: true, message: "延期注册有效期不能为空", trigger: "change" },
  ],
  isContinue: [
    { required: true, message: "是否参加继续教育不能为空", trigger: "change" },
  ],
});

/** 取消按钮 */
function cancel() {
  router.go(-1);
}

/** 表单提交 */
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (isExtend.value) {
        // 延期提交
        updateCertificateEnroll(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("延期成功");
            loading.value = false;
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      } else if (form.value.id) {
        // 编辑提交
        updateCertificateEnroll(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            loading.value = false;
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        // 新增提交
        addCertificateEnroll(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            loading.value = false;
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}
// 人员选择
function handleUserChange(val) {
  form.value.staffName = val.name;
  form.value.gender = val.gender;
  form.value.staffType = val.staffType;
  form.value.unit = val.projectName;
  form.value.idNumber = val.idNumber;
  form.value.age = val.age;
  form.value.workYears = val.workYears;
  form.value.projectName = val.projectName;
  form.value.deptName = val.deptName;
  form.value.postName = val.postName;
}
/** 添加增项 */
function handleAddItem() {
  addFormRef.value.validate((valid) => {
    if (valid) {
      if (!form.value.adds) {
        form.value.adds = [];
      }
      form.value.adds.push({ ...addForm.value });

      // 重置表单
      addForm.value = {
        addMajor: undefined,
        addEffectiveDate: undefined,
        addExtensionDate: undefined,
        isContinue: "0",
      };
      // 重置表单校验结果
      addFormRef.value.resetFields();
    }
  });
}

/** 删除增项 */
function handleDeleteAdd(index) {
  form.value.adds.splice(index, 1);
}

/** 初始化表单数据 */
function initFormData() {
  const id = route.query.id;
  const viewMode = route.query.view === "true";
  const extendMode = route.query.extend === "true";

  if (id) {
    // 查看、编辑或延期模式
    disabled.value = viewMode;
    getCertificateEnroll(id).then((response) => {
      if (route.query.extend === "true") {
        response.data.effectiveDate =
          response.data.extensionDate || response.data.effectiveDate;
        form.value.extensionDate = null;
      }
      Object.assign(form.value, response.data);
    });
  } else {
    // 新增模式
    disabled.value = false;
  }
}

onMounted(() => {
  initFormData();
});
</script>

<style scoped>
div :deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}

.add-form-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}
</style>
