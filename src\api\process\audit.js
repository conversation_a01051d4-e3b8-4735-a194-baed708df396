import request from "@/utils/request";
// 查询我的待审流程列表
export function listMyPendingProcess(query) {
  return request({
    url: "/flow/instance/my/pending/list",
    method: "get",
    params: query,
  });
}

// 审核流程
export function auditProcess(data) {
  return request({
    url: "/flow/instance/audit",
    method: "put",
    data: data,
  });
}

// 查询我的已处理流程列表
export function listMyReviewedProcess(query) {
  return request({
    url: "/flow/instance/my/reviewed/list",
    method: "get",
    params: query,
  });
}
// 根据模版ID业务ID查询实例ID
export function getInstanceIdByTemplateIdAndBizId(templateId, bizId) {
  return request({
    url: `/flow/instance/${templateId}/${bizId}`,
    method: "get",
  });
}
// 根据实例ID查询业务ID
export function getBusinessIdByInstanceId(instanceId) {
  return new Promise((resolve, reject) => {
    request({
      url: `/flow/instance/${instanceId}`,
      method: "get",
    })
      .then((res) => {
        // 重组节点列表数据
        res.data.processInstanceNodeList = [
          {
            nodeName: "创建申请",
            updateTime: res.data.createTime,
            createBy: res.data.applyUserName,
            status: "2",
          },
          ...res.data.processInstanceNodeList,
          {
            nodeName: "完成",
            status: "1",
          },
        ];
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
