<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryParams.projectName"
              placeholder="请输入项目名称"
              clearable
              class="w-[200px]"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="项目状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="projectList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column
            label="负责人"
            align="center"
            prop="leaderUserName"
          />
          <el-table-column
            label="负责人联系方式"
            align="center"
            prop="phone"
            width="160"
          />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="230"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleOrg(scope.row)"
                >机构</el-button
              >
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <!-- <el-button
                type="text"
                @click="handleDelete(scope.row)"
                
                >删除</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑项目对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="projectForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="项目负责人" prop="leaderUserId">
          <RemoteSelect
            v-model="form.leaderUserId"
            url="/system/user/list"
            labelKey="nickName"
            valueKey="userId"
            placeholder="请选择项目"
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";

import {
  listProject,
  getProject,
  addProject,
  updateProject,
  delProject,
} from "@/api/basic/project";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 项目表格数据
const projectList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectName: undefined,
  status: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  projectName: undefined,
  orderNum: 0,
  leaderUserId: undefined,
  leaderName: undefined,
  phone: undefined,
  status: "0",
});

// 表单校验
const rules = ref({
  projectName: [
    { required: true, message: "项目名称不能为空", trigger: "blur" },
  ],
  orderNum: [{ required: true, message: "排序不能为空", trigger: "blur" }],
  leaderUserId: [
    { required: true, message: "项目负责人不能为空", trigger: "blur" },
  ],
  phone: [
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
});

/** 查询项目列表 */
function getList() {
  loading.value = true;
  listProject(queryParams.value).then((response) => {
    projectList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    projectName: undefined,
    orderNum: 0,
    leaderUserId: undefined,
    leaderName: undefined,
    phone: undefined,
    status: "0",
  };
  proxy.resetForm("projectForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 用户选择事件 */
function handleUserChange(user) {
  if (user) {
    form.value.leaderName = user.nickName;
  } else {
    form.value.leaderName = "";
  }
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加项目";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getProject(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑项目";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["projectForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateProject(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addProject(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id;
  proxy.$modal
    .confirm('是否确认删除项目名称为"' + row.projectName + '"的数据项?')
    .then(function () {
      return delProject(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 机构按钮操作 */
function handleOrg(row) {
  router.push({
    path: "/basic/project/org",
    query: {
      projectId: row.id,
      projectName: row.projectName,
    },
  });
}

onMounted(() => {
  getList();
});
</script>
