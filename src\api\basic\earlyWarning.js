import request from "@/utils/request";

// 查询预警规则配置列表
export function listEarlyWarning(query) {
  return request({
    url: "/railway/wlEarlyWarning/list",
    method: "get",
    params: query,
  });
}

// 查询预警规则配置详细
export function getEarlyWarning(id) {
  return request({
    url: "/railway/wlEarlyWarning/" + id,
    method: "get",
  });
}

// 新增预警规则配置
export function addEarlyWarning(data) {
  return request({
    url: "/railway/wlEarlyWarning",
    method: "post",
    data: data,
  });
}

// 修改预警规则配置
export function updateEarlyWarning(data) {
  return request({
    url: "/railway/wlEarlyWarning",
    method: "put",
    data: data,
  });
}

// 删除预警规则配置
export function delEarlyWarning(id) {
  return request({
    url: "/railway/wlEarlyWarning/" + id,
    method: "delete",
  });
}
