<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <!-- 标签切换 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="审核中的证书" name="auditing"></el-tab-pane>
          <el-tab-pane label="核销中的证书" name="verifying"></el-tab-pane>
          <el-tab-pane label="全部证书" name="all"></el-tab-pane>
        </el-tabs>

        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="姓名" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.staffName"
              placeholder="请输入姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="岗位" prop="postId">
            <RemoteSelect
              v-model="queryParams.postId"
              url="/system/post/list"
              labelKey="postName"
              valueKey="postId"
              placeholder="请选择岗位"
              class="w-[200px]"
            ></RemoteSelect>
          </el-form-item>
          <el-form-item label="人员类型" prop="staffType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.staffType"
              placeholder="请选择人员类型"
              clearable
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="证书编号" prop="certificateCode">
            <el-input
              class="w-[200px]"
              v-model="queryParams.certificateCode"
              placeholder="请输入证书编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择单位"
            ></RemoteSelect>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="certificateList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column label="姓名" align="center" prop="staffName" />
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column label="岗位" align="center" prop="postName" />
          <el-table-column label="年龄" align="center" prop="age" />
          <el-table-column
            label="身份证号码"
            align="center"
            prop="idNumber"
            width="160"
          />
          <el-table-column label="工作年限" align="center" prop="workYears" />
          <el-table-column
            label="操作合规证书编号"
            width="160"
            align="center"
            prop="certificateCode"
          />
          <el-table-column label="工种" align="center" prop="workType">
            <template #default="scope">
              <dict-tag
                :options="identify_job_types"
                :value="scope.row.workType"
              />
            </template>
          </el-table-column>
          <el-table-column label="发证机关" align="center" prop="issueOrgan" />
          <el-table-column
            label="证书有效期"
            align="center"
            prop="effectiveDate"
            width="150"
          >
            <template #default="scope">
              <span>{{ scope.row.effectiveDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="进场时间" align="center" prop="approachDate">
            <template #default="scope">
              <span>{{ scope.row.approachDate }}</span>
            </template>
          </el-table-column>

          <el-table-column label="审核状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="audit_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column
            label="核销状态"
            align="center"
            prop="status"
            v-if="activeTab !== 'auditing'"
          >
            <template #default="scope">
              <dict-tag :options="audit_status" :value="scope.row.takeStatus" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                type="text"
                @click="handleSubmitAudit(scope.row)"
                v-if="scope.row.status === '0'"
                >提审</el-button
              >
              <el-button
                type="text"
                @click="handleUpdate(scope.row)"
                v-if="scope.row.status === '0'"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-if="scope.row.status === '0'"
                >删除</el-button
              >
              <el-button
                type="text"
                @click="handleVerify(scope.row)"
                v-if="scope.row.status === '2' && !scope.row.takeStatus"
                >核销</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  listSpecialCertificateDetail,
  launchProcess,
  delSpecialCertificateDetail,
} from '@/api/certificate/special';

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

// 字典数据
const { sys_user_sex, personnel_type, audit_status, identify_job_types } =
  proxy.useDict(
    'sys_user_sex',
    'personnel_type',
    'audit_status',
    'identify_job_types'
  );

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 特种作业证书人员明细表格数据
const certificateList = ref([]);

// 当前活动的标签页
const activeTab = ref('auditing');

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: undefined,
  postName: undefined,
  staffType: undefined,
  certificateCode: undefined,
  projectId: undefined,
  status: '1', // 默认查询审核中的证书
});
/** 查询特种作业证书人员明细列表 */
function getList() {
  loading.value = true;
  listSpecialCertificateDetail(queryParams.value).then((response) => {
    certificateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 标签页切换 */
function handleTabClick(e) {
  activeTab.value = e.props.name;
  // 根据选中的标签页设置不同的状态查询条件
  if (activeTab.value === 'auditing') {
    queryParams.value.status = '1'; // 审核中的状态值
    queryParams.value.takeStatus = undefined;
  } else if (activeTab.value === 'verifying') {
    queryParams.value.status = undefined; // 核销中的状态值
    queryParams.value.takeStatus = 1;
  } else {
    queryParams.value.status = undefined; // 全部证书不限制状态
    queryParams.value.takeStatus = undefined;
  }
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  // 保持当前标签页的状态过滤条件
  if (activeTab.value === 'auditing') {
    queryParams.value.status = '1'; // 审核中的状态值
    queryParams.value.takeStatus = undefined;
  } else if (activeTab.value === 'verifying') {
    queryParams.value.status = undefined; // 核销中的状态值
    queryParams.value.takeStatus = 1;
  } else {
    queryParams.value.status = undefined; // 全部证书不限制状态
    queryParams.value.takeStatus = undefined;
  }
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 查看按钮操作 */
function handleView(row) {
  const templateId = row.takeStatus ? '10003' : '10002';
  proxy.getProcessRouterPath(row, templateId, row.id);
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  router.push({
    path: '/certificate/certificateSpecial/form',
    query: { id: row.id, projectId: route.query.id },
  });
}

/** 提审按钮操作 */
function handleSubmitAudit(row) {
  proxy.$modal
    .confirm(`确认要提交审核"${row.staffName}"的证书吗？`)
    .then(function () {
      return launchProcess({
        bizId: row.id,
        templateId: '10002',
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('提交审核成功');
    })
    .catch(() => {});
}

/** 核销按钮操作 */
function handleVerify(row) {
  proxy.$modal
    .confirm(`确认要核销"${row.staffName}"的证书吗？`)
    .then(function () {
      return launchProcess({
        bizId: row.id,
        templateId: '10003',
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('核销成功');
    })
    .catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(`确认要删除"${row.staffName}"的证书吗？`)
    .then(function () {
      return delSpecialCertificateDetail(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlCertificateSpecial/export',
    {
      ...queryParams.value,
    },
    `special_certificate_audit_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.font-bold {
  font-weight: 700;
}
.w-full {
  width: 100%;
}
</style>
