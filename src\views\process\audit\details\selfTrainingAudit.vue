<template>
  <div class="app-container">
    <el-card>
      <div class="basic-info">
        <div class="flex justify-between mb-4">
          <p class="section-title mt-5 mb-3">自培班次基本信息</p>
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">单位名称：</span>
                <span>{{ info.projectName || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训班名：</span>
                <span>{{ info.className || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训形式：</span>
                <dict-tag :options="training_form" :value="info.trainingMode" />
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训开始时间：</span>
                <span>{{
                  parseTime(info.beginTime, "{y}-{m}-{d}") || "--"
                }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训结束时间：</span>
                <span>{{
                  parseTime(info.endTime, "{y}-{m}-{d}") || "--"
                }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">岗前培训课时：</span>
                <span>{{ info.beforeHour || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">安全培训课时：</span>
                <span>{{ info.secureHour || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">技术培训课时：</span>
                <span>{{ info.artHour || "--" }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">其他培训课时：</span>
                <span>{{ info.otherHour || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">学习课时：</span>
                <span>{{
                  info.beforeHour +
                    info.secureHour +
                    info.artHour +
                    info.otherHour || "--"
                }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">管理人员：</span>
                <span
                  >已选{{
                    info.trainingStaffs?.filter((item) => item.type == 0)
                      .length || "0"
                  }}人</span
                >
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">技术人员：</span>
                <span
                  >已选{{
                    info.trainingStaffs?.filter((item) => item.type == 1)
                      .length || "0"
                  }}人</span
                >
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">工人：</span>
                <span
                  >已选{{
                    info.trainingStaffs?.filter((item) => item.type == 2)
                      .length || "0"
                  }}人</span
                >
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">劳务人员：</span>
                <span
                  >已选{{
                    info.trainingStaffs?.filter((item) => item.type == 3)
                      .length || "0"
                  }}人</span
                >
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">入学人数：</span>
                <span>{{ info.trainingStaffs?.length || "--" }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">结业人数：</span>
                <span>{{ info.graduaNumber || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item">
                <span class="label">考核方式：</span>
                <span>{{ formatAssessWay(info.assessWay) || "--" }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 关联培训计划 -->
        <div class="section-title mt-5 mb-3">关联培训计划</div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="12">
              <div class="info-item">
                <span class="label">培训计划：</span>
                <span>{{ info.planName || "--" }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 兼职教师授课津贴报批 -->
        <div class="section-title mt-5 mb-3">兼职教师授课津贴报批</div>
        <el-table :data="teachers" border style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="培训类型" align="center" prop="trainingType">
            <template #default="scope">
              <dict-tag
                :options="training_type"
                :value="scope.row.trainingType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="授课教师姓名"
            align="center"
            prop="staffName"
          />
          <el-table-column label="职务" align="center" prop="position">
            <template #default="scope">
              <dict-tag
                :options="professional_title"
                :value="scope.row.position"
              />
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center" prop="jobTitle">
            <template #default="scope">
              <dict-tag :options="title_category" :value="scope.row.jobTitle" />
            </template>
          </el-table-column>
          <el-table-column
            label="授课内容"
            align="center"
            prop="lectureContent"
          />
          <el-table-column label="参考资料" align="center" prop="reference" />
          <el-table-column label="资料是否有自编" align="center" prop="isSelf">
            <template #default="scope">
              <span>{{ scope.row.isSelf === "1" ? "是" : "否" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="课时数" align="center" prop="classHour" />
          <el-table-column label="津贴标准" align="center" prop="artSubsidy" />
          <el-table-column label="金额" align="center" prop="artSubsidyTotal" />
        </el-table>

        <div class="total-row">
          <span>合计：{{ totalAmount }} 元</span>
        </div>

        <!-- 附件列表 -->
        <div class="flex items-center">
          <div class="section-title mt-5 mb-3 mr-3">附件</div>
          <div>
            <attachment-display :attachments="info.wlAnnexes" />
          </div>
        </div>
        <!-- 审核组件 -->
        <ProcessComponent :nodeInfo="nodeInfo" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getWlTrainingSelf } from "@/api/training/selfTraining";
import { useRoute, useRouter } from "vue-router";
import { getCurrentInstance } from "vue";
import ProcessComponent from "@/components/ProcessComponent";
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
import { getBusinessIdByInstanceId } from "@/api/process/audit";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
// 节点信息
const nodeInfo = ref({});
// 字典数据
const {
  training_form,
  training_type,
  professional_title,
  title_category,
  assessment_method,
} = proxy.useDict(
  "training_form",
  "training_type",
  "professional_title",
  "title_category",
  "assessment_method"
);

const info = ref({});
// 教师列表
const teachers = ref([]);

// 计算总金额
const totalAmount = computed(() => {
  return teachers.value
    .reduce((sum, item) => {
      return sum + (Number(item.artSubsidyTotal) || 0);
    }, 0)
    .toFixed(2);
});

// 格式化考核方式
function formatAssessWay(assessWay) {
  if (!assessWay) return "";

  const wayArray =
    typeof assessWay === "string" ? assessWay.split(",") : assessWay;
  return wayArray
    .map((way) => {
      const item = assessment_method.value.find((m) => m.value === way);
      return item ? item.label : way;
    })
    .join("、");
}

// 下载附件
function downloadFile(path) {
  if (path) {
    window.open(path);
  }
}

/** 查询自培详细 */
function getInfo(id) {
  getWlTrainingSelf(id).then((response) => {
    info.value = response.data;

    // 处理教师列表
    if (response.data.teachers) {
      teachers.value = response.data.teachers;
    } else {
      teachers.value = [];
    }
  });
}

/** 返回按钮 */
function goBack() {
  router.go(-1);
}

onMounted(async () => {
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  if (instanceId) {
    const res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
    nodeInfo.value = res.data;
  }
  if (id) {
    getInfo(id);
  }
});
</script>

<style scoped lang="scss">
.info-content {
  background-color: #f3f7fc;
  padding: 30px 5% 2px 5%;
}

.el-row {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 140px;
    text-align: left;
    color: #606266;
    padding-right: 10px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
}

.total-row {
  text-align: right;
  padding: 10px 0;
  font-weight: 400;
  font-size: 16px;
  background-color: #f5f6fb;
  padding-right: 50px;
}

.empty-annex {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}
.elLink {
  cursor: pointer;
}
.elLink:hover {
  color: #409eff;
}

@media screen and (max-width: 768px) {
  .info-item .label {
    min-width: 110px;
  }
}
</style>
