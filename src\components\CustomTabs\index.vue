<template>
  <div class="custom-tabs">
    <div class="tabs-header">
      <div
        v-for="(pane, index) in panes"
        :key="pane.name || index"
        :class="[
          'tab-item',
          { 'is-active': activeTab === (pane.name || index) },
        ]"
        @click="handleTabClick(pane, index)"
      >
        <span class="tab-label">{{ pane.label }}</span>
      </div>
    </div>
    <div class="tabs-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, provide, onMounted, watch, nextTick } from "vue";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: "",
  },
});

const emit = defineEmits(["update:modelValue", "tab-click"]);

const activeTab = ref(props.modelValue);
const panes = ref([]);
const isChanging = ref(false);

// 注册tab-pane
const registerPane = (pane) => {
  panes.value.push(pane);
};

// 注销tab-pane
const unregisterPane = (pane) => {
  const index = panes.value.findIndex((p) => p.name === pane.name);
  if (index > -1) {
    panes.value.splice(index, 1);
  }
};

provide("customTabs", {
  activeTab,
  registerPane,
  unregisterPane,
  isChanging,
});

const handleTabClick = async (pane, index) => {
  const tabName = pane.name || index;
  if (tabName === activeTab.value) return;

  isChanging.value = true;

  // 延迟更新activeTab，避免闪烁
  await nextTick();
  activeTab.value = tabName;
  emit("update:modelValue", tabName);
  emit("tab-click", pane, { name: tabName, index });

  // 重置切换状态
  setTimeout(() => {
    isChanging.value = false;
  }, 300);
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal !== activeTab.value) {
      isChanging.value = true;
      await nextTick();
      activeTab.value = newVal;
      setTimeout(() => {
        isChanging.value = false;
      }, 300);
    }
  }
);

onMounted(() => {
  // 如果没有设置默认值，设置第一个tab为活跃
  if (!activeTab.value && panes.value.length > 0) {
    activeTab.value = panes.value[0].name || 0;
    emit("update:modelValue", activeTab.value);
  }
});
</script>

<style scoped>
.custom-tabs {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #f1f3f4;
}

.tabs-header {
  display: flex;
  background: #ffffff;
  padding: 6px;
  border-bottom: 1px solid #f1f3f4;
  gap: 4px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-header::-webkit-scrollbar {
  display: none;
}

.tab-item {
  position: relative;
  padding: 14px 20px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  background: transparent;
  border: none;
  white-space: nowrap;
  min-width: fit-content;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.tab-item:hover:not(.is-active) {
  background: #f8f9fa;
  transform: translateY(-1px);
}

.tab-item.is-active {
  background: #1a73e8;
  color: white;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
  transform: translateY(-1px);
}

.tab-label {
  font-size: 14px;
  font-weight: 500;
  color: #5f6368;
  transition: all 0.2s ease;
  line-height: 1.2;
}

.tab-item.is-active .tab-label {
  color: white;
  font-weight: 600;
}

.tab-item:hover:not(.is-active) .tab-label {
  color: #202124;
}

.tabs-content {
  background: #ffffff;
  position: relative;
}

/* 现代简约的滚动条样式 */
.tabs-header {
  scrollbar-width: thin;
  scrollbar-color: #dadce0 transparent;
}

.tabs-header::-webkit-scrollbar {
  height: 4px;
  display: block;
}

.tabs-header::-webkit-scrollbar-track {
  background: transparent;
}

.tabs-header::-webkit-scrollbar-thumb {
  background: #dadce0;
  border-radius: 2px;
}

.tabs-header::-webkit-scrollbar-thumb:hover {
  background: #bdc1c6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-header {
    padding: 4px;
    gap: 2px;
  }

  .tab-item {
    padding: 12px 16px;
    border-radius: 10px;
  }

  .tab-label {
    font-size: 13px;
  }
}

/* 移除可能引起闪烁的动画 */

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .custom-tabs {
    background: #202124;
    border-color: #3c4043;
  }

  .tabs-header {
    background: #202124;
    border-bottom-color: #3c4043;
  }

  .tab-item:hover:not(.is-active) {
    background: #2d2e30;
  }

  .tab-label {
    color: #bdc1c6;
  }

  .tab-item:hover:not(.is-active) .tab-label {
    color: #e8eaed;
  }

  .tabs-content {
    background: #202124;
  }
}
</style>
