import request from "@/utils/request";

// 查询合同列表
export function listContract(query) {
  return request({
    url: "/railway/wlContract/list",
    method: "get",
    params: query,
  });
}

// 查询合同详细
export function getContract(id) {
  return request({
    url: "/railway/wlContract/" + id,
    method: "get",
  });
}

// 新增合同
export function addContract(data) {
  return request({
    url: "/railway/wlContract",
    method: "post",
    data: data,
  });
}

// 编辑合同
export function updateContract(data) {
  return request({
    url: "/railway/wlContract",
    method: "put",
    data: data,
  });
}

// 删除合同
export function delContract(id) {
  return request({
    url: "/railway/wlContract/" + id,
    method: "delete",
  });
}

// 续签合同
export function renewContract(data) {
  return request({
    url: "/railway/wlContract/renew",
    method: "post",
    data: data,
  });
}
