<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
      <div style="display: flex; align-items: center">
        <div>年月时间：</div>
        <el-date-picker
          v-model="valuetime"
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          placeholder="请选择年月时间"
          @change="handleChange"
        />
      </div>
    </div>
    <div ref="chartRef" class="chart"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  UniversalTransition,
  CanvasRenderer,
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(['change']);
const chartRef = ref(null);
const valuetime = ref('');
let chart = null;

const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const data = params[0];
        // 获取完整的 x 轴标签文字
        const fullXAxisLabel = props.chartData.xAxis[data.dataIndex];
        return `${fullXAxisLabel}<br/>
                劳动生产力：${
                  props.chartData.dict?.[data.name]?.avgAmount
                }元/人<br/>
                发放工资总额：${
                  props.chartData.dict?.[data.name]?.totalAmount
                }元<br/>
                发放工资人数：${
                  props.chartData.dict?.[data.name]?.totalPerson
                }人`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: props.chartData.xAxis,
      axisLabel: {
        interval: 0,
        rotate: 45,
        color: '#333',
        fontSize: 12,
        // 限制文字长度，超过显示省略号
        formatter: function (value) {
          const maxLength = 8; // 最大显示字符数
          if (value.length > maxLength) {
            return value.substring(0, maxLength) + '...';
          }
          return value;
        },
        // 鼠标悬停显示完整文字
        showMaxLabel: true,
      },
      axisTick: {
        alignWithLabel: true,
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED',
        },
      },
      // 添加 triggerEvent 以支持鼠标事件
      triggerEvent: true,
    },
    yAxis: {
      type: 'value',
      name: '劳动生产力(元/人)',
      nameTextStyle: {
        color: '#333',
        fontSize: 12,
        align: 'left',
      },

      axisLabel: {
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E4E7ED',
        },
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: 16,
        itemStyle: {
          color: '#67E0E3',
          // borderRadius: [8, 8, 0, 0]
        },
        data: props.chartData.data,
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
};

watch(
  () => props.chartData,
  () => {
    chart && chart.dispose();
    initChart();
  },
  { deep: true }
);
function handleChange(val) {
  emit('change', val);
}
onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.chart {
  width: 100%;
  height: 360px;
}
</style>
