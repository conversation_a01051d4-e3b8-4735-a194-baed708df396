<template>
  <el-table-column label="定职岗位" prop="postId" align="center" width="200">
    <template #default="slotScope">
      <RemoteSelect
        v-if="!viewMode"
        v-model="slotScope.row.postId"
        v-model:modelName="slotScope.row.postName"
        url="/system/post/list"
        labelKey="postName"
        valueKey="postId"
        placeholder="请选择岗位"
        class="w-full"
      />
      <span v-else>{{ slotScope.row.postName }}</span>
    </template>
  </el-table-column>
  <el-table-column label="专业技术职务" prop="artId" width="200" align="center">
    <template #default="slotScope">
      <RemoteSelect
        v-if="!viewMode"
        v-model="slotScope.row.artId"
        v-model:modelName="slotScope.row.artName"
        url="/railway/wlArtSubsidy/list"
        responsePath="rows"
        labelKey="artName"
        valueKey="id"
        placeholder="请选择职务"
      />
      <span v-else>{{ slotScope.row.artName }}</span>
    </template>
  </el-table-column>
  <el-table-column label="职称分类" prop="postType" align="center" width="200">
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.postType"
        placeholder="请选择职称分类"
      >
        <el-option
          v-for="dict in title_category"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag
        v-else
        :options="title_category"
        :value="slotScope.row.postType"
      />
    </template>
  </el-table-column>
  <el-table-column
    label="聘任时间"
    prop="appointmentDate"
    width="170"
    align="center"
  >
    <template #default="slotScope">
      <el-date-picker
        v-if="!viewMode"
        v-model="slotScope.row.appointmentDate"
        type="date"
        style="width: 100%"
        placeholder="请选择聘任时间"
        value-format="YYYY-MM-DD"
      />
      <span v-else>{{ slotScope.row.appointmentDate }}</span>
    </template>
  </el-table-column>
  <el-table-column label="备注" prop="remark" align="center" width="200">
    <template #default="slotScope">
      <el-input v-if="!viewMode" v-model="slotScope.row.remark" />
      <span v-else>{{ slotScope.row.remark }}</span>
    </template>
  </el-table-column>
</template>

<script setup>
import { getCurrentInstance } from "vue";

defineProps({
  viewMode: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();
const { title_category } = proxy.useDict("title_category");
</script>
