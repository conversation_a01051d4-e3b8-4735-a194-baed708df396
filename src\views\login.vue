<template>
  <AdaptiveContainer>
    <div class="login-container">
      <img
        src="@/assets/images/login.png"
        class="fixed left-0 top-0 w-full h-full"
      />
      <h1
        class="title fixed left-[14%] text-[50px] top-[20%] w-[35%] text-center text-white"
      >
        三处人力资源智能化信息系统
      </h1>
      <img
        class="title w-[460px] h-[76px] fixed left-[18%] bottom-[13%]"
        src="@/assets/images/login-logo.png"
        alt=""
      />
    </div>
    <div class="login-content">
      <div class="login-right">
        <div class="login-form-container">
          <h2 class="welcome-text">欢迎回来</h2>
          <p class="login-desc">请登录您的账号以继续</p>

          <div class="form-group">
            <div class="input-wrapper">
              <img src="@/assets/images/user.png" class="w-4 el-icon-user" />
              <input
                v-model="loginForm.username"
                type="text"
                placeholder="用户名"
                @keyup.enter="handleLogin"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="input-wrapper">
              <img src="@/assets/images/pwd.png" class="w-4 el-icon-user" />
              <input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                @keyup.enter="handleLogin"
              />
            </div>
          </div>

          <div class="form-group" v-if="captchaEnabled">
            <div class="captcha-wrapper">
              <div class="input-wrapper">
                <i class="el-icon-key"></i>
                <input
                  class="w-full"
                  v-model="loginForm.code"
                  type="text"
                  placeholder="验证码"
                  @keyup.enter="handleLogin"
                />
              </div>
              <img
                :src="codeUrl"
                @click="getCode()"
                class="captcha-img"
                title="点击更换验证码"
              />
            </div>
          </div>

          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" v-model="loginForm.rememberMe" />
              <span>记住密码</span>
            </label>
          </div>

          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            {{ loading ? "登录中" : "登录" }}
          </el-button>
        </div>
      </div>
    </div>
  </AdaptiveContainer>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import AdaptiveContainer from "@/components/AdaptiveContainer/index.vue";
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

function handleLogin() {
  if (!loginForm.value.username) {
    proxy.$modal.msgError("请输入用户名");
    return;
  }
  if (!loginForm.value.password) {
    proxy.$modal.msgError("请输入密码");
    return;
  }
  if (captchaEnabled.value && !loginForm.value.code) {
    proxy.$modal.msgError("请输入验证码");
    return;
  }
  if (loading.value) return;
  loading.value = true;
  // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
  if (loginForm.value.rememberMe) {
    Cookies.set("username", loginForm.value.username, { expires: 30 });
    Cookies.set("password", encrypt(loginForm.value.password), {
      expires: 30,
    });
    Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
  } else {
    // 否则移除
    Cookies.remove("username");
    Cookies.remove("password");
    Cookies.remove("rememberMe");
  }
  // 调用action的登录方法
  userStore
    .login(loginForm.value)
    .then(() => {
      router.push({ path: redirect.value || "/" });
    })
    .catch(() => {
      loading.value = false;
      // 重新获取验证码
      if (captchaEnabled.value) {
        getCode();
      }
    });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: url("@/assets/images/login.png") no-repeat center center; */
  background-size: contain;
}

.login-content {
  width: 480px;
  background: #fff;
  backdrop-filter: blur(10px);
  border-radius: 40px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  overflow: hidden;
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  right: 20%;
}

.login-left {
  flex: 1;
  background: rgba(255, 255, 255, 0.5);
  padding: 60px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.brand-section {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.brand-image {
  position: fixed;
  left: 13%;
  top: 50%;
  width: 30%;
  transform: translateY(-50%);
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(0, 122, 255, 0.1) 0%,
    rgba(0, 122, 255, 0.05) 100%
  );
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.circle-2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -50px;
  background: linear-gradient(
    135deg,
    rgba(0, 122, 255, 0.08) 0%,
    rgba(0, 122, 255, 0.03) 100%
  );
}

.circle-3 {
  width: 150px;
  height: 150px;
  bottom: 10%;
  right: 10%;
  background: linear-gradient(
    135deg,
    rgba(0, 122, 255, 0.06) 0%,
    rgba(0, 122, 255, 0.02) 100%
  );
}

.login-right {
  flex: 1;
  padding: 60px;
  display: flex;
  align-items: center;
}

.login-form-container {
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
}

.welcome-text {
  font-size: 32px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.login-desc {
  font-size: 16px;
  color: #86868b;
  margin-bottom: 40px;
}

.form-group {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f0f3f8;
  border-radius: 12px;
  padding: 0 16px;
  height: 50px;
  transition: all 0.3s ease;
  input {
    padding-left: 20px;
  }
}

.input-wrapper:focus-within {
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.input-wrapper i {
  font-size: 20px;
  color: #86868b;
  margin-right: 12px;
}

.input-wrapper input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #1d1d1f;
  outline: none;
}

.input-wrapper input::placeholder {
  color: #86868b;
}

.captcha-wrapper {
  display: flex;
  gap: 12px;
}

.captcha-wrapper .input-wrapper {
  flex: 1;
}

.captcha-img {
  height: 50px;
  cursor: pointer;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #86868b;
  cursor: pointer;
}

.remember-me input[type="checkbox"] {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #86868b;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  position: relative;
}

.remember-me input[type="checkbox"]:checked {
  background-color: #2674fe;
  border-color: #2674fe;
}

.remember-me input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  background: #2674fe;
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: #0066cc;
  transform: translateY(-1px);
}
</style>
