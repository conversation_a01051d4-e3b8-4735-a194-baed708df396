<template>
  <div class="login-container">
    <el-row class="h-full">
      <el-col :xs="0" :sm="14" :md="18" :lg="18">
        <img
          src="@/assets/images/login-bg.jpg"
          class="w-full h-[100vh] object-cover"
        />
      </el-col>
      <el-col :xs="24" :sm="10" :md="6" :lg="6">
        <div class="p-[10%] w-full h-full flex flex-col items-center">
          <img src="@/assets/images/logo.png" class="w-[122px] h-[94px]" />
          <p class="text-[22px] font-bold text-center">
            中铁隧道局集团
            <br />
            三处人力资源智能化信息系统
          </p>
          <div class="w-full">
            <p class="text-[32px] font-bold mt-[60px] mb-0">用户登录</p>
            <span class="text-[#888] m-0"> USER LOGIN </span>
            <div class="flex flex-col mt-[10px]">
              <div class="flex items-center input-item">
                <img src="@/assets/images/user.png" class="w-[24px] h-[24px]" />
                <div
                  class="w-[2px] h-[6px] bg-[#888] ml-[20px] mr-[20px]"
                ></div>
                <input
                  class="input"
                  v-model="loginForm.username"
                  type="text"
                  placeholder="请输入需要登录的账号"
                  @keyup.enter="handleLogin"
                />
              </div>
              <div class="flex items-center input-item">
                <img src="@/assets/images/pwd.png" class="w-[24px] h-[24px]" />
                <div
                  class="w-[2px] h-[6px] bg-[#888] ml-[20px] mr-[20px]"
                ></div>
                <input
                  class="input"
                  v-model="loginForm.password"
                  type="password"
                  placeholder="请输入密码"
                  @keyup.enter="handleLogin"
                />
              </div>
              <div class="flex items-center input-item" v-if="captchaEnabled">
                <img src="@/assets/images/code.png" class="w-[24px] h-[24px]" />
                <div
                  class="w-[2px] h-[6px] bg-[#888] ml-[20px] mr-[20px]"
                ></div>
                <input
                  class="input"
                  v-model="loginForm.code"
                  type="text"
                  placeholder="请输入验证码"
                  @keyup.enter="handleLogin"
                />
                <img
                  :src="codeUrl"
                  @click="getCode()"
                  class="captcha-img"
                  title="点击更换验证码"
                />
              </div>
              <div class="flex items-center input-item">
                <el-button
                  :loading="loading"
                  type="primary"
                  class="login-button"
                  @click="handleLogin"
                >
                  {{ loading ? '登录中' : '登录' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { getCodeImg } from '@/api/login';
import Cookies from 'js-cookie';
import { encrypt, decrypt } from '@/utils/jsencrypt';
import useUserStore from '@/store/modules/user';
import { useRoute, useRouter } from 'vue-router';
import AdaptiveContainer from '@/components/AdaptiveContainer/index.vue';
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: '',
});

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

function handleLogin() {
  if (!loginForm.value.username) {
    proxy.$modal.msgError('请输入用户名');
    return;
  }
  if (!loginForm.value.password) {
    proxy.$modal.msgError('请输入密码');
    return;
  }
  if (captchaEnabled.value && !loginForm.value.code) {
    proxy.$modal.msgError('请输入验证码');
    return;
  }
  if (loading.value) return;
  loading.value = true;
  // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
  if (loginForm.value.rememberMe) {
    Cookies.set('username', loginForm.value.username, { expires: 30 });
    Cookies.set('password', encrypt(loginForm.value.password), {
      expires: 30,
    });
    Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 });
  } else {
    // 否则移除
    Cookies.remove('username');
    Cookies.remove('password');
    Cookies.remove('rememberMe');
  }
  // 调用action的登录方法
  userStore
    .login(loginForm.value)
    .then(() => {
      router.push({ path: redirect.value || '/' });
    })
    .catch(() => {
      loading.value = false;
      // 重新获取验证码
      if (captchaEnabled.value) {
        getCode();
      }
    });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = 'data:image/gif;base64,' + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get('username');
  const password = Cookies.get('password');
  const rememberMe = Cookies.get('rememberMe');
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();
</script>

<style scoped>
.login-container {
  height: 100vh;
  overflow: hidden;
}
.input-item {
  margin-top: 15px;
  padding-left: 25px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #eee;
}
.input {
  outline: none;
  padding: 0 10px;
  height: 34px;
  line-height: 34px;
  border-color: transparent;
  font-size: 14px;
  width: 100%;
}
.captcha-img {
  height: 34px;
  cursor: pointer;
}
.login-button {
  width: 100%;
  background: linear-gradient(to right, #00acff, #006be9);
  height: 42px;
  line-height: 42px;
  text-align: center;
  cursor: pointer;
  transition: opacity 0.2s;
  border: unset;
  margin-top: 30px;
}

.login-button:hover {
  opacity: 0.8;
}
</style>
