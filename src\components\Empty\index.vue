<template>
  <div class="empty-container">
    <div class="empty-image">
      <img src="@/assets/images/empty.png" alt="暂无数据" />
    </div>
    <div class="empty-text">
      <slot>{{ emptyText }}</slot>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  // 空状态文本
  text: {
    type: String,
    default: "暂无数据",
  },
});

const emptyText = ref(props.text);
</script>

<style scoped>
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.empty-image {
  margin-bottom: 10px;
}

.empty-image img {
  width: 120px;
  height: auto;
}

.empty-text {
  color: #909399;
  font-size: 14px;
}
</style>
