<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-tabs
        class="px-[20px]"
        v-model="activeName"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="待处理" name="pending">
          <to-pending-process
            ref="pendingRef"
            v-if="activeName === 'pending'"
          />
        </el-tab-pane>
        <el-tab-pane label="已处理" name="reviewed">
          <reviewed-process
            ref="reviewedRef"
            v-if="activeName === 'reviewed'"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import ToPendingProcess from "./components/ToPendingProcess.vue";
import ReviewedProcess from "./components/ReviewedProcess.vue";

const activeName = ref("pending");
const pendingRef = ref(null);
const reviewedRef = ref(null);

/** 标签页点击事件 */
function handleTabClick() {
  // 可以在这里执行其他操作，目前不需要
}

onMounted(() => {
  // 初始化完成
});
</script>
