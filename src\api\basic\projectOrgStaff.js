import request from "@/utils/request";

// 查询定员设置列表
export function listProjectOrgStaff(query) {
  return request({
    url: "/railway/wlProjectOrgStaff/list",
    method: "get",
    params: query,
  });
}

// 查询定员设置详细
export function getProjectOrgStaff(id) {
  return request({
    url: "/railway/wlProjectOrgStaff/" + id,
    method: "get",
  });
}

// 新增定员设置
export function addProjectOrgStaff(data) {
  return request({
    url: "/railway/wlProjectOrgStaff",
    method: "post",
    data: data,
  });
}

// 修改定员设置
export function updateProjectOrgStaff(data) {
  return request({
    url: "/railway/wlProjectOrgStaff",
    method: "put",
    data: data,
  });
}

// 删除定员设置
export function delProjectOrgStaff(id) {
  return request({
    url: "/railway/wlProjectOrgStaff/" + id,
    method: "delete",
  });
}

// 导出定员设置
export function exportProjectOrgStaff(query) {
  return request({
    url: "/railway/wlProjectOrgStaff/export",
    method: "get",
    params: query,
  });
}

// 获取导入模板
export function importTemplate() {
  return request({
    url: "/railway/wlProjectOrgStaff/importTemplate",
    method: "get",
  });
}

// 导入数据
export function importData(data) {
  return request({
    url: "/railway/wlProjectOrgStaff/import",
    method: "post",
    data: data,
  });
}
