<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      :extraParams="{ projectId: form.projectId }"
      @success="handleImportSuccess"
    />
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="项目名称" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择项目"
            ></RemoteSelect>
          </el-form-item>

          <el-form-item label="时间" prop="serviceTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.serviceTime"
              type="year"
              placeholder="选择年"
              format="YYYY"
              value-format="YYYY"
              clearable
            />
          </el-form-item>

          <el-form-item label="上传状态" prop="status">
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择计划状态"
              clearable
            >
              <el-option
                v-for="dict in planStatusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="planProjectList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间" align="center" prop="serviceTime" />
          <el-table-column label="项目名称" align="center" prop="projectName" />

          <el-table-column label="计划数量" align="center" prop="num">
            <template #default="scope">
              <el-button
                v-if="scope.row.num > 0"
                @click="handleExport(scope.row)"
                text
                type="primary"
              >
                {{ scope.row.num }}
              </el-button>
              <span v-else>{{ scope.row.num }}</span>
            </template>
          </el-table-column>

          <el-table-column label="上传状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="planStatusOptions"
                :value="scope.row.newStatus"
              />
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="200"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.status == 'HAVE_ALREADY_UPLOADED'"
                type="text"
                @click="handleView(scope.row)"
                >查看</el-button
              >

              <el-button type="text" @click="handleUpload(scope.row)"
                >上传</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { listProject } from '@/api/training/plan';

const router = useRouter();
// 获取计划上传状态字典
const planStatusOptions = [
  { label: '未上传', value: 'HAVEN_T_UPLOADED', elTagType: 'danger' },
  { label: '已上传', value: 'HAVE_ALREADY_UPLOADED' },
];
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 培训计划表格数据
const planProjectList = ref([]);
// 项目选项
const projectOptions = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  serviceTime: undefined,
  projectId: undefined,
  status: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  serviceTime: undefined,
  trainingContent: undefined,
  planDays: 1,
  participantCount: 0,
  trainingLocation: undefined,
  personInCharge: undefined,
  status: 'NOT_STARTED',
  remark: undefined,
});

// 表单校验
const rules = ref({
  projectId: [{ required: true, message: '项目不能为空', trigger: 'change' }],
  serviceTime: [
    { required: true, message: '计划时间不能为空', trigger: 'blur' },
  ],
  trainingContent: [
    { required: true, message: '培训内容不能为空', trigger: 'blur' },
  ],
  planDays: [{ required: true, message: '计划天数不能为空', trigger: 'blur' }],
  participantCount: [
    { required: true, message: '参与人数不能为空', trigger: 'blur' },
  ],
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入培训计划');
const importUrl = '/railway/wlTrainingPlan/importData';
const templateUrl = '/railway/wlTrainingPlan/importTemplate';

/** 查询培训计划列表 */
function getList() {
  // 开启加载状态
  loading.value = true;
  queryParams.value.serviceTime = queryParams.value.serviceTime
    ? queryParams.value.serviceTime + '-01-01'
    : null;

  // 调用列表函数并处理响应
  listProject(queryParams.value)
    .then((response) => {
      // 更新计划项目列表
      planProjectList.value = response.rows;
      // 更新总数
      total.value = response.total;
      // 关闭加载状态
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 上传按钮操作 */
function handleUpload(row) {
  form.value.projectId = row.projectId;
  importOpen.value = true;
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: '/training/trainingPlan/details',
    query: {
      projectId: row.projectId,
      projectName: row.projectName,
      serviceTime: row.serviceTime,
      status: row.status,
      num: row.num,
    },
    meta: { activeMenu: location.pathname },
  });
}

/** 导出按钮操作 */
function handleExport(row) {
  const projectId = row.projectId || null;
  const projectName = row.projectName + '_' || '';
  proxy.download(
    'railway/wlTrainingPlan/warn/export',
    {
      ...queryParams.value,
      projectId: projectId,
    },
    `${projectName}培训计划_${new Date().getTime()}.xlsx`
  );
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  getList();
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.plan-preview {
  margin-top: 10px;
}
</style>
