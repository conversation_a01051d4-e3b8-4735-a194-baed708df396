<template>
  <div class="app-container">
    <el-card>
      <div class="basic-info">
        <div class="flex justify-between mb-4">
          <p class="font-bold text-xl">人员信息</p>
          <PageSearch top="90px" container="#searchContainer"></PageSearch>
        </div>

        <!-- 修改描述 -->
        <div
          v-if="changeDescription"
          class="change-description mb-4 p-4 bg-gray-100 rounded"
        >
          <p class="font-bold mb-2">变更说明：</p>
          <p>{{ changeDescription }}</p>
        </div>

        <div class="info-content" id="searchContainer">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="8">
              <div class="info-item">
                <span class="label">照片：</span>
                <image-upload
                  v-model="info.photo"
                  size="70px"
                  :isShowTip="false"
                  :limit="1"
                  disabled
                />
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('name') ? 'label changed-label' : 'label'
                  "
                  >姓名：</span
                >
                <div class="value-container">
                  <span :class="isFieldChanged('name') ? 'new-value' : ''">{{
                    info.name || "--"
                  }}</span>
                  <div v-if="isFieldChanged('name')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.name || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('staffCode')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >员工编号：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('staffCode') ? 'new-value' : ''"
                    >{{ info.staffCode || "--" }}</span
                  >
                  <div v-if="isFieldChanged('staffCode')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.staffCode || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('formerName')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >曾用名：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('formerName') ? 'new-value' : ''"
                    >{{ info.formerName || "--" }}</span
                  >
                  <div v-if="isFieldChanged('formerName')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.formerName || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('birthDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >出生日期：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('birthDate') ? 'new-value' : ''"
                    >{{ info.birthDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('birthDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.birthDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('birthPlace')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >出生地：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('birthPlace') ? 'new-value' : ''"
                    >{{ info.birthPlace || "--" }}</span
                  >
                  <div v-if="isFieldChanged('birthPlace')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.birthPlace || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('ethnicity')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >民族：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('ethnicity') ? 'new-value' : ''"
                    >{{ info.ethnicity || "--" }}</span
                  >
                  <div v-if="isFieldChanged('ethnicity')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.ethnicity || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('gender') ? 'label changed-label' : 'label'
                  "
                  >性别：</span
                >
                <div class="value-container">
                  <span :class="isFieldChanged('gender') ? 'new-value' : ''">{{
                    getDictLabel(sys_user_sex, info.gender) || "--"
                  }}</span>
                  <div v-if="isFieldChanged('gender')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(sys_user_sex, oldInfo.gender) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('idNumber') ? 'label changed-label' : 'label'
                  "
                  >身份证号：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('idNumber') ? 'new-value' : ''"
                    >{{ info.idNumber || "--" }}</span
                  >
                  <div v-if="isFieldChanged('idNumber')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.idNumber || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('maritalStatus')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >婚姻状况：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('maritalStatus') ? 'new-value' : ''"
                    >{{
                      getDictLabel(marital_status, info.maritalStatus) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('maritalStatus')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(marital_status, oldInfo.maritalStatus) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('healthStatus')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >健康状况：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('healthStatus') ? 'new-value' : ''"
                    >{{
                      getDictLabel(health_status, info.healthStatus) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('healthStatus')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(health_status, oldInfo.healthStatus) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('countryRegion')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >国家地区：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('countryRegion') ? 'new-value' : ''"
                    >{{ info.countryRegion || "--" }}</span
                  >
                  <div v-if="isFieldChanged('countryRegion')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.countryRegion || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('hometown') ? 'label changed-label' : 'label'
                  "
                  >籍贯：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('hometown') ? 'new-value' : ''"
                    >{{ info.hometown || "--" }}</span
                  >
                  <div v-if="isFieldChanged('hometown')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.hometown || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('householdType')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >户口性质：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('householdType') ? 'new-value' : ''"
                    >{{
                      getDictLabel(household_type, info.householdType) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('householdType')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(household_type, oldInfo.householdType) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('highestEducation')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >最高学历：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('highestEducation') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(education_type, info.highestEducation) ||
                      "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('highestEducation')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(education_type, oldInfo.highestEducation) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('politicalStatus')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >政治面貌：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('politicalStatus') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(political_status, info.politicalStatus) ||
                      "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('politicalStatus')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(political_status, oldInfo.politicalStatus) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('householdLocation')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >户口所在地：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('householdLocation') ? 'new-value' : ''
                    "
                    >{{ info.householdLocation || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('householdLocation')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.householdLocation || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('partyJoinDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >入党(入团)日期：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('partyJoinDate') ? 'new-value' : ''"
                    >{{ info.partyJoinDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('partyJoinDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.partyJoinDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('staffType')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >人员类型：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('staffType') ? 'new-value' : ''"
                    >{{
                      getDictLabel(personnel_type, info.staffType) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('staffType')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(personnel_type, oldInfo.staffType) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('joinCrscDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >进入中铁时间：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('joinCrscDate') ? 'new-value' : ''"
                    >{{ info.joinCrscDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('joinCrscDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.joinCrscDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('age') ? 'label changed-label' : 'label'
                  "
                  >年龄：</span
                >
                <div class="value-container">
                  <span :class="isFieldChanged('age') ? 'new-value' : ''">{{
                    info.age || "--"
                  }}</span>
                  <div v-if="isFieldChanged('age')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.age || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('workYears')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >工龄：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('workYears') ? 'new-value' : ''"
                    >{{ info.workYears || "--" }}</span
                  >
                  <div v-if="isFieldChanged('workYears')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.workYears || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('adjustedWorkYears')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >调整工龄：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('adjustedWorkYears') ? 'new-value' : ''
                    "
                    >{{ info.adjustedWorkYears || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('adjustedWorkYears')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.adjustedWorkYears || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('phone') ? 'label changed-label' : 'label'
                  "
                  >联系电话：</span
                >
                <div class="value-container">
                  <span :class="isFieldChanged('phone') ? 'new-value' : ''">{{
                    info.phone || "--"
                  }}</span>
                  <div v-if="isFieldChanged('phone')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.phone || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('homePhone')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >家庭电话：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('homePhone') ? 'new-value' : ''"
                    >{{ info.homePhone || "--" }}</span
                  >
                  <div v-if="isFieldChanged('homePhone')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.homePhone || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('homeAddress')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >家庭住址：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('homeAddress') ? 'new-value' : ''"
                    >{{ info.homeAddress || "--" }}</span
                  >
                  <div v-if="isFieldChanged('homeAddress')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.homeAddress || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('currentAddress')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >现在居住地址：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('currentAddress') ? 'new-value' : ''"
                    >{{ info.currentAddress || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('currentAddress')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.currentAddress || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('cadreDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >转干时间：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('cadreDate') ? 'new-value' : ''"
                    >{{ info.cadreDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('cadreDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.cadreDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('department')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >所在单位：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('department') ? 'new-value' : ''"
                    >{{
                      getDictLabel(department_type, info.department) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('department')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(department_type, oldInfo.department) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('currentProfession')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >现从事专业：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('currentProfession') ? 'new-value' : ''
                    "
                    >{{ info.currentProfession || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('currentProfession')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.currentProfession || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('extractDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >提职日期：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('extractDate') ? 'new-value' : ''"
                    >{{ info.extractDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('extractDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.extractDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('joinUnitDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >到本单位时间：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('joinUnitDate') ? 'new-value' : ''"
                    >{{ info.joinUnitDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('joinUnitDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.joinUnitDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('schoolingYears')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >学龄：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('schoolingYears') ? 'new-value' : ''"
                    >{{ info.schoolingYears || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('schoolingYears')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.schoolingYears || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('professionalTitleDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >职称聘任时间：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('professionalTitleDate') ? 'new-value' : ''
                    "
                    >{{ info.professionalTitleDate || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('professionalTitleDate')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.professionalTitleDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('skillIdentification')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >技能鉴定工种：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('skillIdentification') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(
                        identify_job_types,
                        info.skillIdentification
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('skillIdentification')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        identify_job_types,
                        oldInfo.skillIdentification
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('socialSecurityStatus')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >社保身份：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('socialSecurityStatus') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(
                        social_security_status,
                        info.socialSecurityStatus
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('socialSecurityStatus')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        social_security_status,
                        oldInfo.socialSecurityStatus
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('position') ? 'label changed-label' : 'label'
                  "
                  >岗位：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('position') ? 'new-value' : ''"
                    >{{ info.position || "--" }}</span
                  >
                  <div v-if="isFieldChanged('position')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.position || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('jobContent')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >从事工作：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('jobContent') ? 'new-value' : ''"
                    >{{
                      getDictLabel(work_type, info.jobContent) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('jobContent')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(work_type, oldInfo.jobContent) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('professionalTitle')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >专业技术职务：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('professionalTitle') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(
                        professional_title,
                        info.professionalTitle
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('professionalTitle')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        professional_title,
                        oldInfo.professionalTitle
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('workerSkillLevel')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >工人技术等级：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('workerSkillLevel') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(worker_skill_level, info.workerSkillLevel) ||
                      "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('workerSkillLevel')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        worker_skill_level,
                        oldInfo.workerSkillLevel
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('positionLevel')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >职务职别：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('positionLevel') ? 'new-value' : ''"
                    >{{
                      getDictLabel(position_level, info.positionLevel) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('positionLevel')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(position_level, oldInfo.positionLevel) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('entrySource')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >进入来源：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('entrySource') ? 'new-value' : ''"
                    >{{
                      getDictLabel(entry_source, info.entrySource) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('entrySource')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(entry_source, oldInfo.entrySource) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('positionStatus')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >岗位状态：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('positionStatus') ? 'new-value' : ''"
                    >{{
                      getDictLabel(position_status, info.positionStatus) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('positionStatus')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(position_status, oldInfo.positionStatus) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('deptName') ? 'label changed-label' : 'label'
                  "
                  >二级机构：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('deptName') ? 'new-value' : ''"
                    >{{ info.deptName || "--" }}</span
                  >
                  <div v-if="isFieldChanged('deptName')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.deptName || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('titleCategory')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >职称分类：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('titleCategory') ? 'new-value' : ''"
                    >{{
                      getDictLabel(title_category, info.titleCategory) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('titleCategory')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(title_category, oldInfo.titleCategory) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('personality')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >个人性格：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('personality') ? 'new-value' : ''"
                    >{{
                      getDictLabel(personality_type, info.personality) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('personality')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(personality_type, oldInfo.personality) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('professionalPersonality')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >职业性格：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('professionalPersonality')
                        ? 'new-value'
                        : ''
                    "
                    >{{
                      getDictLabel(
                        professional_personality,
                        info.professionalPersonality
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('professionalPersonality')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        professional_personality,
                        oldInfo.professionalPersonality
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('businessAbilityEvaluation')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >业务能力评价：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('businessAbilityEvaluation')
                        ? 'new-value'
                        : ''
                    "
                    >{{
                      getDictLabel(
                        business_ability_evaluation,
                        info.businessAbilityEvaluation
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('businessAbilityEvaluation')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        business_ability_evaluation,
                        oldInfo.businessAbilityEvaluation
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('workStatusEvaluation')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >工作状态评价：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('workStatusEvaluation') ? 'new-value' : ''
                    "
                    >{{
                      getDictLabel(
                        work_status_evaluation,
                        info.workStatusEvaluation
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('workStatusEvaluation')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        work_status_evaluation,
                        oldInfo.workStatusEvaluation
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('archived') ? 'label changed-label' : 'label'
                  "
                  >档案是否在册：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('archived') ? 'new-value' : ''"
                    >{{ getDictLabel(sys_yes_no, info.archived) || "--" }}</span
                  >
                  <div v-if="isFieldChanged('archived')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(sys_yes_no, oldInfo.archived) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('archiveNumber')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >档案号：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('archiveNumber') ? 'new-value' : ''"
                    >{{ info.archiveNumber || "--" }}</span
                  >
                  <div v-if="isFieldChanged('archiveNumber')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.archiveNumber || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('archiveAge')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >档案年龄：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('archiveAge') ? 'new-value' : ''"
                    >{{ info.archiveAge || "--" }}</span
                  >
                  <div v-if="isFieldChanged('archiveAge')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.archiveAge || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('archiveCategory')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >档案类别：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('archiveCategory') ? 'new-value' : ''
                    "
                    >{{ info.archiveCategory || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('archiveCategory')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.archiveCategory || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('archiveLocation')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >档案所在地：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('archiveLocation') ? 'new-value' : ''
                    "
                    >{{ info.archiveLocation || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('archiveLocation')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.archiveLocation || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('archiveManagementUnit')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >档案管理单位：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('archiveManagementUnit') ? 'new-value' : ''
                    "
                    >{{ info.archiveManagementUnit || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('archiveManagementUnit')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.archiveManagementUnit || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('honggouPlan')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >是否列入鸿鹄计划：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('honggouPlan') ? 'new-value' : ''"
                    >{{
                      getDictLabel(sys_yes_no, info.honggouPlan) || "--"
                    }}</span
                  >
                  <div v-if="isFieldChanged('honggouPlan')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(sys_yes_no, oldInfo.honggouPlan) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('offlineDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >非在岗时间：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('offlineDate') ? 'new-value' : ''"
                    >{{ info.offlineDate || "--" }}</span
                  >
                  <div v-if="isFieldChanged('offlineDate')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.offlineDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('offlineReason')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >非在岗原因：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('offlineReason') ? 'new-value' : ''"
                    >{{ info.offlineReason || "--" }}</span
                  >
                  <div v-if="isFieldChanged('offlineReason')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.offlineReason || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('firstEducation')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >第一学历：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('firstEducation') ? 'new-value' : ''"
                    >{{
                      getDictLabel(education_type, info.firstEducation) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('firstEducation')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(education_type, oldInfo.firstEducation) ||
                      "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('firstEducationSchool')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >第一学历毕业院校：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('firstEducationSchool') ? 'new-value' : ''
                    "
                    >{{ info.firstEducationSchool || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('firstEducationSchool')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.firstEducationSchool || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('firstEducationMajor')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >第一学历毕业专业：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('firstEducationMajor') ? 'new-value' : ''
                    "
                    >{{ info.firstEducationMajor || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('firstEducationMajor')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.firstEducationMajor || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('firstEducationGraduationDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >第一学历毕业时间：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('firstEducationGraduationDate')
                        ? 'new-value'
                        : ''
                    "
                    >{{ info.firstEducationGraduationDate || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('firstEducationGraduationDate')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.firstEducationGraduationDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('firstEducationSchoolType')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >院校类别：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('firstEducationSchoolType')
                        ? 'new-value'
                        : ''
                    "
                    >{{
                      getDictLabel(
                        school_type,
                        info.firstEducationSchoolType
                      ) || "--"
                    }}</span
                  >
                  <div
                    v-if="isFieldChanged('firstEducationSchoolType')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(
                        school_type,
                        oldInfo.firstEducationSchoolType
                      ) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('highestEducationSchool')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >最高学历毕业学校：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('highestEducationSchool')
                        ? 'new-value'
                        : ''
                    "
                    >{{ info.highestEducationSchool || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('highestEducationSchool')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.highestEducationSchool || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('highestEducationMajor')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >最高学历毕业专业：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('highestEducationMajor') ? 'new-value' : ''
                    "
                    >{{ info.highestEducationMajor || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('highestEducationMajor')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.highestEducationMajor || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('highestEducationGraduationDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >最高学历毕业时间：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('highestEducationGraduationDate')
                        ? 'new-value'
                        : ''
                    "
                    >{{ info.highestEducationGraduationDate || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('highestEducationGraduationDate')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.highestEducationGraduationDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('isExpert') ? 'label changed-label' : 'label'
                  "
                  >是否为专家：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('isExpert') ? 'new-value' : ''"
                    >{{ getDictLabel(sys_yes_no, info.isExpert) || "--" }}</span
                  >
                  <div v-if="isFieldChanged('isExpert')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      getDictLabel(sys_yes_no, oldInfo.isExpert) || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('expertTitle')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >专家名称：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('expertTitle') ? 'new-value' : ''"
                    >{{ info.expertTitle || "--" }}</span
                  >
                  <div v-if="isFieldChanged('expertTitle')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.expertTitle || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('expertLevel')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >专家级别：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('expertLevel') ? 'new-value' : ''"
                    >{{ info.expertLevel || "--" }}</span
                  >
                  <div v-if="isFieldChanged('expertLevel')" class="old-value">
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.expertLevel || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('expertCategory')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >专家类别：</span
                >
                <div class="value-container">
                  <span
                    :class="isFieldChanged('expertCategory') ? 'new-value' : ''"
                    >{{ info.expertCategory || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('expertCategory')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.expertCategory || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('currentPositionDate')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >现职时间：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('currentPositionDate') ? 'new-value' : ''
                    "
                    >{{ info.currentPositionDate || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('currentPositionDate')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.currentPositionDate || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span
                  :class="
                    isFieldChanged('positionPromotionYears')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >提现/职级年限：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('positionPromotionYears')
                        ? 'new-value'
                        : ''
                    "
                    >{{ info.positionPromotionYears || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('positionPromotionYears')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.positionPromotionYears || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item full-width">
                <span
                  :class="
                    isFieldChanged('detailedDescription')
                      ? 'label changed-label'
                      : 'label'
                  "
                  >详细描述：</span
                >
                <div class="value-container">
                  <span
                    :class="
                      isFieldChanged('detailedDescription') ? 'new-value' : ''
                    "
                    >{{ info.detailedDescription || "--" }}</span
                  >
                  <div
                    v-if="isFieldChanged('detailedDescription')"
                    class="old-value"
                  >
                    <span class="arrow-icon">↑</span>
                    <span class="old-value-label">旧值:</span>
                    <span class="old-value-content">{{
                      oldInfo.detailedDescription || "--"
                    }}</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <!-- 审核组件 -->
        <ProcessComponent :nodeInfo="nodeInfo" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getStaffAudit } from "@/api/personnelFiles/personnelUpdateAudit";

import { useRoute, useRouter } from "vue-router";
import { getCurrentInstance } from "vue";
import ProcessComponent from "@/components/ProcessComponent";
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
import { getBusinessIdByInstanceId } from "@/api/process/audit";
import PageSearch from "@/components/PageSearch/index.vue";
// 节点信息
const nodeInfo = ref();
// 字典数据
const {
  sys_user_sex,
  social_security_status,
  personnel_type,
  education_type,
  sys_yes_no,
  work_type,
  marital_status,
  health_status,
  household_type,
  political_status,
  department_type,
  identify_job_types,
  professional_title,
  worker_skill_level,
  position_level,
  entry_source,
  position_status,
  secondary_organization,
  title_category,
  personality_type,
  professional_personality,
  business_ability_evaluation,
  work_status_evaluation,
  school_type,
} = proxy.useDict(
  "sys_user_sex",
  "social_security_status",
  "personnel_type",
  "education_type",
  "sys_yes_no",
  "work_type",
  "marital_status",
  "health_status",
  "household_type",
  "political_status",
  "department_type",
  "identify_job_types",
  "professional_title",
  "worker_skill_level",
  "position_level",
  "entry_source",
  "position_status",
  "secondary_organization",
  "title_category",
  "personality_type",
  "professional_personality",
  "business_ability_evaluation",
  "work_status_evaluation",
  "school_type"
);

const info = ref({});
const oldInfo = ref({});
const changedFields = ref([]);
const changeDescription = ref("");

// 判断字段是否已修改
function isFieldChanged(field) {
  return changedFields.value.includes(field);
}

// 获取字典标签
function getDictLabel(dict, value) {
  const item = dict.find((d) => d.value === value);
  return item ? item.label : "";
}

// 分析变更的字段
function analyzeChanges() {
  if (!info.value || !oldInfo.value) return;

  changedFields.value = [];
  const fields = Object.keys(info.value);

  fields.forEach((field) => {
    if (
      JSON.stringify(info.value[field]) !==
        JSON.stringify(oldInfo.value[field]) &&
      info.value[field] !== undefined &&
      oldInfo.value[field] !== undefined
    ) {
      changedFields.value.push(field);
    }
  });
}

/** 查询员工详细 */
function getInfo(id) {
  getStaffAudit(id).then((response) => {
    info.value = response.data.newStaffVO || {};
    oldInfo.value = response.data.oldStaffVO || {};
    changeDescription.value = response.data.description || "";

    // 分析变更字段
    analyzeChanges();
  });
}

/** 返回按钮 */
function goBack() {
  router.go(-1);
}

onMounted(async () => {
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  if (instanceId) {
    const res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
    nodeInfo.value = res.data;
  }
  if (id) {
    getInfo(id);
  }
});
</script>

<style scoped lang="scss">
.info-content {
  background-color: #f9fafc;
  padding: 30px 5%;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05) inset;
}

.el-row {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
  position: relative;

  .label {
    min-width: 140px;
    text-align: right;
    color: #606266;
    padding-right: 10px;
  }

  .changed-label {
    font-weight: bold;
    color: #409eff;
    position: relative;

    &::before {
      content: "* ";
      color: #f56c6c;
    }
  }

  .value-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;

    .new-value {
      font-weight: 600;
      color: #67c23a;
      background-color: rgba(103, 194, 58, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
      display: inline-block;
      transition: all 0.3s;
      border-left: 3px solid #67c23a;
    }

    .old-value {
      margin-top: 8px;
      padding: 6px 12px;
      background-color: #f8f8f8;
      border-radius: 6px;
      border-left: 3px solid #f56c6c;
      display: flex;
      align-items: center;
      position: relative;
      transition: all 0.3s;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .arrow-icon {
        color: #f56c6c;
        margin-right: 6px;
        font-size: 14px;
        font-weight: bold;
      }

      .old-value-label {
        color: #909399;
        font-size: 13px;
        margin-right: 6px;
        font-weight: 500;
      }

      .old-value-content {
        color: #f56c6c;
        font-weight: 500;
      }
    }
  }
}

.full-width {
  width: 100%;
}

.change-description {
  border-left: 4px solid #409eff;
  background-color: rgba(64, 158, 255, 0.1) !important;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

@media screen and (max-width: 768px) {
  .info-item .label {
    min-width: 110px;
  }
}
</style>
