export default {
  photo: "照片",
  name: "姓名",
  staffCode: "员工编号",
  formerName: "曾用名",
  birthDate: "出生日期",
  birthPlace: "出生地",
  ethnicity: "民族",
  gender: "性别",
  idNumber: "身份证号",
  maritalStatus: "婚姻状况",
  healthStatus: "健康状况",
  countryRegion: "国家地区",
  hometown: "籍贯",
  householdType: "户口性质",
  highestEducation: "最高学历",
  politicalStatus: "政治面貌",
  householdLocation: "户口所在地",
  partyJoinDate: "入党(入团)日期",
  staffType: "人员类型",
  joinCrscDate: "进入中铁时间",
  age: "年龄",
  workYears: "工龄",
  adjustedWorkYears: "调整工龄",
  phone: "联系电话",
  homePhone: "家庭电话",
  homeAddress: "家庭住址",
  currentAddress: "现在居住地址",
  cadreDate: "转干时间",
  projectId: "所在单位",
  currentProfession: "现从事专业",
  extractDate: "提职日期",
  joinUnitDate: "到本单位时间",
  schoolingYears: "学龄",
  professionalTitleDate: "职称聘任时间",
  skillIdentification: "技能鉴定工种",
  socialSecurityStatus: "社保身份",
  postId: "岗位",
  jobContent: "从事工作",
  professionalTitle: "专业技术职务",
  workerSkillLevel: "工人技术等级",
  positionLevel: "职务职别",
  entrySource: "进入来源",
  positionStatus: "岗位状态",
  deptName: "二级机构",
  titleCategory: "职称分类",
  personality: "个人性格",
  professionalPersonality: "职业性格",
  businessAbilityEvaluation: "业务能力评价",
  workStatusEvaluation: "工作状态评价",
  archived: "档案是否在册",
  archiveNumber: "档案号",
  archiveAge: "档案年龄",
  archiveCategory: "档案类别",
  archiveLocation: "档案所在地",
  archiveManagementUnit: "档案管理单位",
  honggouPlan: "是否列入鸿鹄计划",
  offlineDate: "非在岗时间",
  offlineReason: "非在岗原因",
  firstEducation: "第一学历",
  firstEducationSchool: "第一学历毕业院校",
  firstEducationMajor: "第一学历毕业专业",
  firstEducationGraduationDate: "第一学历毕业时间",
  firstEducationSchoolType: "院校类别",
  highestEducationSchool: "最高学历毕业学校",
  highestEducationMajor: "最高学历毕业专业",
  highestEducationGraduationDate: "最高学历毕业时间",
  isExpert: "是否为专家",
  expertTitle: "专家名称",
  expertLevel: "专家级别",
  expertCategory: "专家类别",
  currentPositionDate: "现职时间",
  positionPromotionYears: "提现/职级年限",
  detailedDescription: "详细描述",
};
