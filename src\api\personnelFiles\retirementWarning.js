import request from '@/utils/request';

// 查询退休预警人员列表
export function listRetirementWarning(params) {
  return request({
    url: '/railway/wlStaffInfo/retire',
    method: 'get',
    params,
  });
}

// 获取退休预警人员详细信息
export function getRetirementWarning(id) {
  return request({
    url: '/personnel/retirement/warning/' + id,
    method: 'get',
  });
}

// 处理退休
export function handleRetirement(employeeId) {
  const data = {
    employeeId,
    status: 'R',
  };
  return request({
    url: '/railway/wlStaffInfo',
    method: 'put',
    data: data,
  });
}

// 导出退休预警数据
export function exportRetirementWarning(params) {
  return request({
    url: '/railway/wlStaffInfo/retire/export',
    method: 'get',
    params,
  });
}
