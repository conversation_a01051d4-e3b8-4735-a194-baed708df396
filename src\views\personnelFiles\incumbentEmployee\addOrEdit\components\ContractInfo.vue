<template>
  <div class="contract-info">
    <!-- <div class="flex justify-between mb-4">
      <div class="font-bold text-xl">劳动合同信息</div>
    </div> -->

    <el-form class="mb20" :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="合同编号" prop="contractCode">
        <el-input
          v-model="queryParams.contractCode"
          placeholder="请输入合同编号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="合同状态" prop="expirationStatus">
        <el-select
          v-model="queryParams.expirationStatus"
          placeholder="请选择合同状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in contract_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="contractList" style="width: 100%" border>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="contractCode" label="合同编号" align="center" />
      <el-table-column prop="beginTime" label="开始时间" align="center" />
      <el-table-column prop="endTime" label="结束时间" align="center" />
      <el-table-column prop="daysInterval" label="剩余天数" align="center" />
      <el-table-column prop="status" label="合同状态" align="center" />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.expirationStatus !== 'EXPIRATION'"
            link
            type="primary"
            @click="handleRenew(scope.row)"
            >续签</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { getWlStaffInfoConcat } from "@/api/personnelFiles/incumbentEmployee";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const queryForm = ref(null);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  contractCode: null,
  expirationStatus: null,
  staffId: null,
});

const total = ref(0);
const contractList = ref([]);

// 字典数据
const { contract_type, contract_status } = proxy.useDict(
  "contract_type",
  "contract_status"
);

/** 查询合同信息 */
function getList() {
  if (route.query.id) {
    getWlStaffInfoConcat(queryParams.value).then((response) => {
      if (response.code === 200) {
        contractList.value = response.rows || [];
        total.value = response.total;
      }
    });
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  queryParams.value.staffId = route.query.id;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryForm.value?.resetFields();
  handleQuery();
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: "/contract/index/addOrEdit",
    query: {
      id: row.id,
      view: true,
    },
    meta: { activeMenu: location.pathname },
  });
}

/** 续签按钮操作 */
function handleRenew(row) {
  router.push({
    path: "/contract/index/addOrEdit",
    query: {
      id: row.id,
      type: "renew",
    },
  });
}

onMounted(() => {
  queryParams.value.staffId = route.query.id;
  getList();
});
</script>

<style scoped lang="scss">
.contract-info {
  padding: 20px;
}

.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}

.search-box {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

.mb4 {
  margin-bottom: 1rem;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
