<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @downloadTemplate="handleDownloadTemplate"
    />

    <!-- 履历导出对话框 -->
    <el-dialog
      v-model="resumeExportOpen"
      title="履历导出"
      width="500px"
      append-to-body
    >
      <el-form
        ref="resumeExportFormRef"
        :model="resumeExportForm"
        :rules="resumeExportRules"
        label-width="100px"
      >
        <el-form-item label="开始时间" prop="beginTime">
          <el-date-picker
            v-model="resumeExportForm.beginTime"
            type="date"
            placeholder="选择开始时间"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="resumeExportForm.endTime"
            type="date"
            placeholder="选择结束时间"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="单位名称">
          <RemoteSelect
            v-model="resumeExportForm.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择单位"
          />
        </el-form-item>
        <el-form-item label="职务">
          <el-select
            v-model="resumeExportForm.professionalTitle"
            placeholder="请选择职务"
            style="width: 100%"
          >
            <el-option
              v-for="dict in professional_title"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职务级别" prop="positionLevel">
          <el-select
            v-model="resumeExportForm.positionLevel"
            placeholder="请选择职务级别"
            style="width: 100%"
          >
            <el-option
              v-for="dict in position_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="resumeExportOpen = false" class="cancel-btn"
            >取 消</el-button
          >
          <el-button type="primary" @click="exportResumeData">导 出</el-button>
        </div>
      </template>
    </el-dialog>

    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-tooltip
              content="姓名、所在单位、岗位、二级机构、档案号"
              placement="right"
            >
              <el-input
                class="w-[200px]"
                v-model="queryParams.keyword"
                placeholder="请输入关键字"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-tooltip>
          </el-form-item>
          <el-form-item label="社保身份" prop="socialSecurityStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.socialSecurityStatus"
              placeholder="请选择社保身份"
              clearable
            >
              <el-option
                v-for="dict in social_security_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            <el-select
              class="w-[200px]"
              v-model="queryParams.gender"
              placeholder="请选择性别"
              clearable
            >
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="人员类型" prop="personnelType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.personnelType"
              placeholder="请选择人员类型"
              clearable
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            <el-input-number
              :controls="false"
              v-model="queryParams.age"
              placeholder="请输入年龄"
              :min="0"
              :max="150"
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="学历" prop="highestEducation">
            <el-select
              class="w-[200px]"
              v-model="queryParams.highestEducation"
              placeholder="请选择学历"
              clearable
            >
              <el-option
                v-for="dict in education_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="职称" prop="titleCategory">
            <el-select
              class="w-[200px]"
              v-model="queryParams.titleCategory"
              placeholder="请选择职称"
              clearable
            >
              <el-option
                v-for="dict in title_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="从事工作" prop="jobContent">
            <el-select
              class="w-[200px]"
              v-model="queryParams.jobContent"
              placeholder="请选择从事工作"
              clearable
            >
              <el-option
                v-for="dict in work_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleBatchAdd"
              >人员导入</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <custom-export
              :params="{ ...queryParams, ids: selectionIds }"
              name="基本信息导出"
              url="/railway/wlStaffInfo/export"
              :fieldOptions="exportColumns"
            />
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExportResume"
              >履历导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExportAudit"
              >干审表导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              class="custom-btn"
              :disabled="selectionIds.length === 0"
              @click="handleDelete"
              >批量退场</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
          <custom-sort
            :fieldOptions="tableRef?.columns"
            :filterLabels="['操作']"
            v-model:sort-field="queryParams.orderByColumn"
            v-model:sort-order="queryParams.isAsc"
            @sort-change="getList"
          />
        </el-row>

        <el-table
          v-loading="loading"
          :data="employeeList"
          ref="tableRef"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            reserve-selection
            type="selection"
            width="50"
            align="center"
          />
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column
            label="社保身份"
            align="center"
            prop="socialSecurityStatus"
          >
            <template #default="scope">
              <dict-tag
                :options="social_security_status"
                :value="scope.row.socialSecurityStatus"
              />
            </template>
          </el-table-column>
          <el-table-column label="所在单位" align="center" prop="projectName" />
          <el-table-column label="岗位" align="center" prop="postName" />
          <el-table-column label="二级机构" align="center" prop="deptName"
            ><template #default="scope">
              <span v-if="scope.row.deptName">
                {{ scope.row.deptName }}
              </span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column label="年龄" align="center" prop="age" />
          <el-table-column label="学历" align="center" prop="highestEducation">
            <template #default="scope">
              <dict-tag
                :options="education_type"
                :value="scope.row.highestEducation"
              />
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center" prop="titleCategory">
            <template #default="scope">
              <dict-tag
                :options="title_category"
                :value="scope.row.titleCategory"
              />
            </template>
          </el-table-column>
          <el-table-column label="从事工作" align="center" prop="jobContent">
            <template #default="scope">
              <dict-tag :options="work_type" :value="scope.row.jobContent" />
            </template>
          </el-table-column>
          <el-table-column label="档案号" align="center" prop="archiveNumber" />
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="180"
          >
            <template #default="scope">
              <el-button type="text" @click="handleInfo(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >

              <el-button
                type="text"
                v-if="
                  scope.row.staffType === 'fzxlwg' ||
                  scope.row.staffType === 'fzxygzjbz'
                "
                @click="handleDelete(scope.row)"
                >退场</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  listEmployee,
  delEmployee,
} from '@/api/personnelFiles/incumbentEmployee';
import ImportExcel from '@/components/ImportExcel/index.vue';
import CustomSort from '@/components/CustomSort/index.vue';
const { proxy } = getCurrentInstance();
const router = useRouter();
import CustomExport from '@/components/CustomExport/index.vue';
import exportColumns from './exportColumns.js';
// 字典数据
const {
  sys_user_sex,
  social_security_status,
  personnel_type,
  education_type,
  title_category,
  work_type,
  professional_title,
  position_level,
} = proxy.useDict(
  'sys_user_sex',
  'social_security_status',
  'personnel_type',
  'education_type',
  'title_category',
  'work_type',
  'professional_title',
  'position_level'
);
const tableRef = ref(null);
// 已经勾选的数据ID
const selectionIds = ref([]);
// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 在职人员表格数据
const employeeList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  socialSecurityStatus: undefined,
  personnelType: undefined,
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('人员导入');
const importUrl = '/railway/wlStaffInfo/importData';
const templateUrl = '/railway/wlStaffInfo/importTemplate';

// 履历导出参数
const resumeExportOpen = ref(false);
const resumeExportForm = ref({
  beginTime: '',
  endTime: '',
  projectName: undefined,
  professionalTitle: undefined,
  positionLevel: undefined,
});
// 验证结束时间必须大于开始时间
function validateEndTime(rule, value, callback) {
  if (resumeExportForm.value.beginTime && value) {
    if (new Date(value) <= new Date(resumeExportForm.value.beginTime)) {
      callback(new Error('结束时间必须大于开始时间'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
const resumeExportRules = ref({
  beginTime: [
    { required: true, message: '开始时间不能为空', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '结束时间不能为空', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' },
  ],
});
/** 查询在职人员列表 */
function getList() {
  loading.value = true;
  listEmployee(queryParams.value).then((res) => {
    employeeList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  proxy.$refs['tableRef'].clearSelection();
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  router.push('/personnelFiles/incumbentEmployee/addOrEdit');
}

/** 批量新增按钮操作 */
function handleBatchAdd() {
  importOpen.value = true;
}

/** 查看 */
function handleInfo(row) {
  router.push({
    path: '/personnelFiles/incumbentEmployee/addOrEdit',
    query: { id: row.id, type: 'check' },
  });
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  router.push({
    path: '/personnelFiles/incumbentEmployee/addOrEdit',
    query: { id: row.id },
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || selectionIds.value;
  proxy.$modal
    .confirm('是否确认退场选择的数据项？')
    .then(function () {
      return delEmployee(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('操作成功');
    })
    .catch(() => {});
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  getList();
}

/** 基本信息导出按钮操作 */
function handleExportBasic() {
  proxy.download(
    'railway/wlStaffInfo/export',
    {
      ...queryParams.value,
    },
    `employee_basic_${new Date().getTime()}.xlsx`
  );
}

/** 履历导出按钮操作 */
function handleExportResume() {
  // 重置表单数据
  resumeExportForm.value = {
    beginTime: '',
    endTime: '',
    projectId: undefined,
    professionalTitle: undefined,
    positionLevel: undefined,
  };
  resumeExportOpen.value = true;
}

/** 履历导出数据 */
function exportResumeData() {
  proxy.$refs['resumeExportFormRef'].validate((valid) => {
    if (valid) {
      exportResumeDataHandle();
    }
  });
}

function exportResumeDataHandle() {
  proxy.download(
    'railway/wlStaffInfo/curriculumVitaeExport',
    {
      ...queryParams.value,
      ...resumeExportForm.value,
    },
    `履历导出_${new Date().getTime()}.zip`
  );
  resumeExportOpen.value = false;
}

/** 干审表导出按钮操作 */
function handleExportAudit() {
  proxy.download(
    'railway/wlStaffInfo/dryReviewExport',
    {
      ...queryParams.value,
    },
    `干审核表导出.zip`
  );
}

// 多选模式下的选择变更处理
function handleSelectionChange(selection) {
  selectionIds.value = selection.map((item) => item.id);
}
onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
div :deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}
</style>
