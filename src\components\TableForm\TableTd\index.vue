<template>
  <td
    class="label-td"
    v-if="label"
    :width="labelWidth || formContext.labelWidth"
    :align="labelAlign"
    :rowspan="labelRowsSpan"
  >
    <span v-if="required" class="text-[red]">*</span> {{ label }}
  </td>
  <td
    :align="align"
    :width="width"
    class="label-content p-0"
    :style="{ minHeight: height }"
    v-bind="$attrs"
  >
    <div :style="{ height: height }" ref="content">
      <slot></slot>
    </div>
  </td>
</template>
<script setup>
import { inject, ref, onMounted, nextTick } from "vue";
const formContext = inject("formContext");

const content = ref(null);
const props = defineProps({
  // 一直必填
  required: {
    type: Boolean,
    default: false,
  },
  // 标题名称
  label: {
    type: String,
    default: () => "",
  },
  /* 标题列宽度 */
  labelWidth: {
    type: String,
    default: () => "",
  },
  /* 标题对齐方式 */
  labelAlign: {
    type: String,
    default: () => "right",
  },
  /* 内容对齐方式 */
  align: {
    type: String,
    default: () => "center",
  },
  /* 内容列宽度 */
  width: {
    type: String,
    default: () => "",
  },
  /* 内容列高度 */
  height: {
    type: String,
    default: () => "32px",
  },
  labelRowsSpan: {
    type: Number,
    default: () => 1,
  },
});
onMounted(() => {
  nextTick(() => {
    if (props.disabled) {
    }
  });
});
</script>
<style lang="scss" scoped>
/* ===== 表单项基础样式 ===== */
:deep(.el-form-item) {
  margin: 0; /* 移除表单项默认边距 */
  height: 100%; /* 设置表单项高度充满容器 */
}

:deep(.el-form-item__error) {
  display: none; /* 隐藏表单项错误提示 */
}

/* ===== 输入框样式重置 ===== */
:deep(.el-input__wrapper) {
  border-radius: unset; /* 移除输入框圆角 */
  box-shadow: unset; /* 移除输入框默认阴影 */
}

:deep(.el-textarea__inner) {
  height: 100%; /* 文本域高度充满容器 */
  box-shadow: unset; /* 移除文本域默认阴影 */
  border-radius: unset; /* 移除文本域圆角 */
}

/* ===== 下拉选择框样式重置 ===== */
:deep(.el-select__wrapper) {
  box-shadow: unset; /* 移除选择框默认阴影 */
  border-radius: unset; /* 移除选择框圆角 */
  border: unset; /* 移除选择框边框 */
}

/* ===== 正常状态下的聚焦样式 ===== */
:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 4px rgba(118, 180, 172, 0.4); /* 聚焦时的绿色阴影 */
  z-index: 99; /* 提升层级确保阴影显示 */
  border-color: #76b4ac; /* 聚焦时的绿色边框 */
  background-color: #edf9f7; /* 聚焦时的浅绿色背景 */
}

:deep(.is-focus) {
  box-shadow: 0 0 0 4px rgba(118, 180, 172, 0.4); /* 聚焦时的绿色阴影 */
  z-index: 99; /* 提升层级 */
  border-color: #76b4ac; /* 聚焦时的绿色边框 */
  background-color: #edf9f7; /* 聚焦时的浅绿色背景 */
}

:deep(.is-focus .el-input__inner) {
  color: #007465; /* 聚焦时输入框文字颜色 */
}

:deep(.is-focused .el-select__placeholder) {
  color: #007465; /* 聚焦时选择框占位符颜色 */
}

:deep(.is-focused .el-select__placeholder.is-transparent) {
  color: #007465; /* 聚焦时透明占位符颜色 */
}

/* ===== 错误状态样式 ===== */
:deep(.is-error.is-required .el-select__wrapper) {
  box-shadow: unset; /* 移除错误状态选择框阴影 */
  background-color: #ffeeee !important; /* 错误状态的粉色背景 */
}

:deep(.is-error.is-required .el-input__wrapper) {
  box-shadow: unset; /* 移除错误状态输入框阴影 */
  background-color: #ffeeee !important; /* 错误状态的粉色背景 */
}

:deep(.is-error.is-required .is-focus) {
  box-shadow: 0 0 0 4px rgba(255, 168, 168, 0.4) !important; /* 错误状态聚焦时的红色阴影 */
  z-index: 99; /* 提升层级 */
}

:deep(.is-error.is-required .el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 4px rgba(255, 168, 168, 0.4) !important; /* 错误状态选择框聚焦时的红色阴影 */
  z-index: 99; /* 提升层级 */
}

:deep(.is-error.is-required .el-select__placeholder.is-transparent) {
  color: #a8abb2; /* 错误状态占位符颜色 */
}

/* ===== 悬停状态样式 ===== */
:deep(.el-select__wrapper.is-hovering:not(.is-focused)) {
  box-shadow: unset; /* 悬停但未聚焦时移除阴影 */
}

/* ===== 表格单元格样式 ===== */
.label-td {
  color: #444; /* 标签文字颜色 */
  font-size: 14px; /* 标签字体大小 */
  padding: 5px; /* 标签内边距 */
  vertical-align: middle; /* 标签垂直居中 */
}

.label-content {
  vertical-align: middle; /* 内容垂直居中 */
}
</style>

<style lang="scss">
/* ===== 全局样式：下拉选项悬停和选中效果 ===== */
/* 针对带有 custom-table-popper 类名的下拉框生效 */
.custom-table-popper .el-select-dropdown__item.is-hovering {
  background-color: #edf9f7 !important; /* 选项悬停时的浅绿色背景 */
  color: #007465 !important; /* 选项悬停时的绿色文字 */
}

.custom-table-popper .el-select-dropdown__item.is-selected {
  background-color: #edf9f7 !important; /* 选项选中时的浅绿色背景 */
  color: #007465 !important; /* 选项选中时的绿色文字 */
}
.custom-table-popper
  .el-date-table
  td.current:not(.disabled)
  .el-date-table-cell__text {
  border: #76b4ac solid 1px;
  background-color: #e5f1ef;
  color: #007465;
}
.custom-table-popper .el-date-table td.available:hover {
  color: #007465;
}
.custom-table-popper .el-date-table td.today .el-date-table-cell__text {
  color: #007465;
}
</style>
