import request from "@/utils/request";

// 查询退休年龄配置列表
export function listStaffRetireConfig(query) {
  return request({
    url: "/railway/wlStaffRetireConfig/list",
    method: "get",
    params: query,
  });
}

// 查询退休年龄配置详细
export function getStaffRetireConfig(id) {
  return request({
    url: "/railway/wlStaffRetireConfig/" + id,
    method: "get",
  });
}

// 新增退休年龄配置
export function addStaffRetireConfig(data) {
  return request({
    url: "/railway/wlStaffRetireConfig",
    method: "post",
    data: data,
  });
}

// 修改退休年龄配置
export function updateStaffRetireConfig(data) {
  return request({
    url: "/railway/wlStaffRetireConfig",
    method: "put",
    data: data,
  });
}

// 删除退休年龄配置
export function delStaffRetireConfig(id) {
  return request({
    url: "/railway/wlStaffRetireConfig/" + id,
    method: "delete",
  });
}
