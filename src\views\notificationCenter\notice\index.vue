<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="待办主题" prop="todoSubject">
            <el-input
              v-model="queryParams.todoSubject"
              placeholder="请输入待办主题"
              clearable
              class="w-[200px]"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
              class="w-[200px]"
            >
              <el-option label="全部" value="" />
              <el-option label="待处理" value="1" />
              <el-option label="已处理" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="notifyList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="待办主题" align="center" prop="todoSubject" />

          <el-table-column label="更新时间" align="center" prop="updateTime" />

          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.status === '2'"
                >已处理</el-tag
              >
              <el-tag type="info" v-else>待处理</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="100"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 查看待办详情对话框 -->
    <el-dialog title="待办详情" v-model="open" width="600px" append-to-body>
      <div v-if="notifyDetail">
        <div class="notify-detail-item">
          <span class="label">待办主题：</span>
          <span>{{ notifyDetail.todoSubject }}</span>
        </div>

        <div class="notify-detail-item">
          <span class="label">更新时间：</span>
          <span>{{ notifyDetail.updateTime }}</span>
        </div>
        <div class="notify-detail-item">
          <span class="label">更新人：</span>
          <span>{{
            notifyDetail.updateBy || notifyDetail.createBy || "--"
          }}</span>
        </div>
        <div class="notify-detail-item">
          <span class="label">状态：</span>
          <span>{{ notifyDetail.status === "2" ? "已处理" : "待处理" }}</span>
        </div>
        <div class="notify-detail-content">
          <div class="content-title">详细内容：</div>
          <div class="content-body" v-html="notifyDetail.content || '--'"></div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="open = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  listFlowNotify,
  readFlowNotify,
} from "@/api/notificationCenter/notice";
import useUserStore from "@/store/modules/user";
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 待办通知列表数据
const notifyList = ref([]);
// 是否显示弹出层
const open = ref(false);
// 待办详情
const notifyDetail = ref(null);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  todoSubject: undefined,
  todoType: "2",
  status: undefined,
  userId: useUserStore().id,
});

/** 查询待办通知列表 */
function getList() {
  loading.value = true;
  listFlowNotify(queryParams.value).then((response) => {
    notifyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 查看按钮操作 */
function handleView(row) {
  const todoId = row.todoId;
  notifyDetail.value = row;
  open.value = true;
  // 如果是未读状态，则标记为已读
  if (row.status !== "2") {
    readFlowNotify(todoId).then(() => {
      // 更新列表中的状态
      row.status = "2";
    });
  }
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.notify-detail-item {
  margin-bottom: 15px;
  font-size: 14px;
  display: flex;
}

.notify-detail-item .label {
  font-weight: bold;
  width: 100px;
}

.notify-detail-content {
  margin-top: 20px;
}

.notify-detail-content .content-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.notify-detail-content .content-body {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 100px;
}
</style>
