import request from "@/utils/request";

// 查询发文抄录列表
export function listDispatchArticle(query) {
  return request({
    url: "/railway/wlDispatchArticle/list",
    method: "get",
    params: query,
  });
}

// 查询发文抄录详细
export function getDispatchArticle(id) {
  return request({
    url: "/railway/wlDispatchArticle/" + id,
    method: "get",
  });
}

// 新增发文抄录
export function addDispatchArticle(data) {
  return request({
    url: "/railway/wlDispatchArticle",
    method: "post",
    data: data,
  });
}

// 修改发文抄录
export function updateDispatchArticle(data) {
  return request({
    url: "/railway/wlDispatchArticle",
    method: "put",
    data: data,
  });
}

// 删除发文抄录
export function delDispatchArticle(id) {
  return request({
    url: "/railway/wlDispatchArticle/" + id,
    method: "delete",
  });
}
