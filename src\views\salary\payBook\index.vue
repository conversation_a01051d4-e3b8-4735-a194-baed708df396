<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="三处/天诚薪酬" name="0"></el-tab-pane>
          <el-tab-pane label="辅助性用工" name="1"></el-tab-pane>
        </el-tabs>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="单位名称" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择项目"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="人员名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入人员名称"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="时间" prop="yearMonth">
            <el-date-picker
              v-model="monthrangeDate"
              type="monthrange"
              start-placeholder="--年--月"
              end-placeholder="--年--月"
              format="YYYY-MM"
              value-format="YYYY-MM"
              class="w-[200px]"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <custom-export
              :params="exportParams"
              name="导出"
              fileName="工资支付书数据.xlsx"
              url="/railway/salaryDetails/export"
              :fieldOptions="exportColumns"
            >
              <template #field="{ field }">
                <el-form-item
                  label="年月"
                  prop="yearMonth"
                  label-position="left"
                  :rules="{
                    required: true,
                    message: '年月不能为空',
                    trigger: 'change',
                  }"
                >
                  <el-date-picker
                    style="width: 50%"
                    v-model="field.yearMonth"
                    type="month"
                    placeholder="请选择年月"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    clearable
                  />
                </el-form-item>
              </template>
            </custom-export>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="salaryList" border>
          <el-table-column
            type="index"
            label="序号"
            width="100"
            align="center"
            fixed
          />

          <!-- 基本信息 -->
          <el-table-column label="基本信息" align="center">
            <el-table-column
              label="时间"
              align="center"
              prop="yearMonth"
              width="100"
            />
            <el-table-column
              label="姓名"
              align="center"
              prop="name"
              width="100"
            />
            <el-table-column
              label="编号"
              align="center"
              prop="code"
              width="100"
            />
            <el-table-column
              label="部门"
              align="center"
              prop="department"
              width="120"
            />
            <el-table-column
              label="岗位"
              align="center"
              prop="position"
              width="120"
            />
            <el-table-column
              label="性别"
              align="center"
              prop="gender"
              width="80"
            />
            <el-table-column
              label="民族"
              align="center"
              prop="ethnicity"
              width="100"
            />
            <el-table-column
              label="身份证号"
              align="center"
              prop="idNumber"
              width="180"
            />
            <el-table-column
              label="银行账号"
              align="center"
              prop="bankAccount"
              width="180"
            />
            <el-table-column
              label="薪酬类别"
              align="center"
              prop="salaryCategory"
              width="120"
            />
          </el-table-column>

          <!-- 工资信息 -->
          <el-table-column label="工资信息" align="center">
            <el-table-column label="基本年薪" align="center">
              <el-table-column
                label="标准"
                align="center"
                prop="baseSalaryStandard"
                width="120"
              />
              <el-table-column
                label="实发"
                align="center"
                prop="baseSalary"
                width="120"
              />
            </el-table-column>
            <el-table-column label="岗位工资" align="center">
              <el-table-column
                label="标准"
                align="center"
                prop="positionSalaryStandard"
                width="120"
              />
              <el-table-column
                label="实发"
                align="center"
                prop="positionSalary"
                width="120"
              />
              <el-table-column
                label="系数"
                align="center"
                prop="positionCoefficient"
                width="100"
              />
            </el-table-column>
            <el-table-column label="绩效工资" align="center">
              <el-table-column
                label="标准"
                align="center"
                prop="performanceSalaryStandard"
                width="120"
              />
              <el-table-column
                label="实发"
                align="center"
                prop="performanceSalary"
                width="120"
              />
              <el-table-column
                label="系数"
                align="center"
                prop="performanceCoefficient"
                width="100"
              />
            </el-table-column>
          </el-table-column>

          <!-- 津贴信息 -->
          <el-table-column label="津贴信息" align="center">
            <el-table-column label="持证津贴" align="center">
              <el-table-column
                label="标准"
                align="center"
                prop="certificationAllowanceStandard"
                width="120"
              />
              <el-table-column
                label="实发"
                align="center"
                prop="certificationAllowance"
                width="120"
              />
            </el-table-column>
            <el-table-column label="技术津贴" align="center">
              <el-table-column
                label="标准"
                align="center"
                prop="technicalAllowanceStandard"
                width="120"
              />
              <el-table-column
                label="实发"
                align="center"
                prop="technicalAllowance"
                width="120"
              />
            </el-table-column>
            <el-table-column label="其他津贴" align="center">
              <el-table-column
                label="电脑补贴"
                align="center"
                prop="computerSubsidy"
                width="120"
              />
              <el-table-column
                label="话费补贴"
                align="center"
                prop="phoneSubsidy"
                width="120"
              />
              <el-table-column
                label="交通补贴"
                align="center"
                prop="transportationSubsidy"
                width="120"
              />
              <el-table-column
                label="其他津贴"
                align="center"
                prop="otherAllowances"
                width="120"
              />
            </el-table-column>
          </el-table-column>

          <!-- 考勤信息 -->
          <el-table-column label="考勤信息" align="center">
            <el-table-column
              label="出勤天数"
              align="center"
              prop="attendanceDays"
              width="100"
            />
            <el-table-column
              label="制度工天"
              align="center"
              prop="systemWorkingDays"
              width="100"
            />
            <el-table-column
              label="法定工天"
              align="center"
              prop="legalWorkingDays"
              width="100"
            />
            <el-table-column
              label="年休工天"
              align="center"
              prop="annualLeaveDays"
              width="100"
            />
            <el-table-column label="病假工天" align="center">
              <el-table-column
                label="长期"
                align="center"
                prop="longTermSickLeave"
                width="100"
              />
              <el-table-column
                label="零星"
                align="center"
                prop="shortTermSickLeave"
                width="100"
              />
            </el-table-column>
            <el-table-column label="其他假期" align="center">
              <el-table-column
                label="婚假"
                align="center"
                prop="marriageLeaveDays"
                width="100"
              />
              <el-table-column
                label="产假"
                align="center"
                prop="maternityLeaveDays"
                width="100"
              />
              <el-table-column
                label="探亲"
                align="center"
                prop="familyVisitDays"
                width="100"
              />
              <el-table-column
                label="丧假"
                align="center"
                prop="bereavementLeaveDays"
                width="100"
              />
            </el-table-column>
          </el-table-column>

          <!-- 保险福利 -->
          <el-table-column label="保险福利" align="center">
            <el-table-column
              label="养老保险"
              align="center"
              prop="pensionInsurance"
              width="120"
            />
            <el-table-column
              label="医疗保险"
              align="center"
              prop="medicalInsurance"
              width="120"
            />
            <el-table-column
              label="失业保险"
              align="center"
              prop="unemploymentInsurance"
              width="120"
            />
            <el-table-column
              label="住房公积金"
              align="center"
              prop="housingFund"
              width="120"
            />
            <el-table-column
              label="企业年金"
              align="center"
              prop="enterpriseAnnuity"
              width="120"
            />
          </el-table-column>

          <!-- 其他费用 -->
          <el-table-column label="其他费用" align="center">
            <el-table-column
              label="水电费"
              align="center"
              prop="utilities"
              width="120"
            />
            <el-table-column
              label="房租"
              align="center"
              prop="rent"
              width="120"
            />
            <el-table-column
              label="物管费"
              align="center"
              prop="propertyFee"
              width="120"
            />
            <el-table-column
              label="备用金"
              align="center"
              prop="pettyCash"
              width="120"
            />
          </el-table-column>

          <!-- 辅助性用工额外信息 -->
          <el-table-column
            v-if="activeTab === '1'"
            label="辅助性用工信息"
            align="center"
          >
            <el-table-column
              label="员工类型"
              align="center"
              prop="staffType"
              width="120"
            />
            <el-table-column
              label="日工资标准"
              align="center"
              prop="dayWageScale"
              width="120"
            />
            <el-table-column
              label="计时工资"
              align="center"
              prop="hourlyWages"
              width="120"
            />
            <el-table-column
              label="生产奖励"
              align="center"
              prop="prodReward"
              width="120"
            />
            <el-table-column
              label="包干工资"
              align="center"
              prop="lumpSalary"
              width="120"
            />
            <el-table-column
              label="合并扣税"
              align="center"
              prop="mergeBuckleTax"
              width="120"
            />
          </el-table-column>

          <!-- 工资汇总 -->
          <el-table-column label="工资汇总" align="center" fixed="right">
            <el-table-column
              label="应发工资"
              align="center"
              prop="grossPay"
              width="120"
            />
            <el-table-column
              label="实发工资"
              align="center"
              prop="netPay"
              width="120"
            />
            <el-table-column
              label="个人所得税"
              align="center"
              prop="inputTax"
              width="120"
            />
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { listSalaryDetails } from '@/api/salary/projectOrgSalary';
import RemoteSelect from '@/components/RemoteSelect';
import CustomExport from '@/components/CustomExport/index.vue';
import exportColumns from './exportColumns.js';
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 总条数
const total = ref(0);
// 薪酬表格数据
const salaryList = ref([]);
// 显示搜索条件
const showSearch = ref(true);
// 当前选中的tab
const activeTab = ref('0');
const exportParams = ref({
  yearMonth: undefined,
});
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  name: undefined,
  type: activeTab.value,
});
const monthrangeDate = ref([]);
/** 查询薪酬列表 */
function getList() {
  loading.value = true;
  const [beginMonth, endMonth] = monthrangeDate.value;
  listSalaryDetails({ ...queryParams.value, beginMonth, endMonth }).then(
    (response) => {
      salaryList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
}
/** 标签页切换 */
function handleTabClick(e) {
  activeTab.value = e.props.name;
  queryParams.value.type = e.props.name;
  handleQuery();
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    '/railway/salaryDetails/export',
    {
      ...queryParams.value,
    },
    `工资支付书数据_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style>
