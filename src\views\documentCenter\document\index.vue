<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
    />
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="文档名称" prop="name">
            <el-input
              class="w-[200px]"
              v-model="queryParams.name"
              placeholder="请输入文档名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="文档类型" prop="type">
            <el-select
              class="w-[200px]"
              v-model="queryParams.type"
              placeholder="请选择文档类型"
              clearable
            >
              <el-option
                v-for="dict in document_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="单位" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              class="w-[200px]"
              placeholder="请选择单位"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col> -->
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="documentList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="文档名称" align="center" prop="name" />
          <el-table-column label="文档类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag :options="document_type" :value="scope.row.type" />
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center" prop="projectName" />

          <el-table-column label="附件" align="center">
            <template #default="scope">
              <attachment-display :attachments="scope.row.wlAnnexes" />
            </template>
          </el-table-column>
          <el-table-column label="描述" align="center" prop="remark">
            <template #default="scope">
              {{ scope.row.remark || '--' }}
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="160"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑文档对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form
        ref="documentForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="文档名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="单位" prop="projectId">
          <RemoteSelect
            v-model="form.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择项目单位"
          />
        </el-form-item>
        <el-form-item label="文档类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择文档类型">
            <el-option
              v-for="dict in document_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文档附件" prop="fileUrl">
          <file-upload
            class="w-full"
            :isCheckFileType="false"
            v-model="form.fileUrl"
            :file-size="60"
            :limit="10"
          />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            rows="3"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 查看文档对话框 -->
    <el-dialog
      title="查看文档"
      v-model="viewOpen"
      width="600px"
      append-to-body
      class="view-dialog"
    >
      <div class="document-view">
        <el-descriptions
          :column="1"
          border
          title="文档信息"
          size="default"
          :label-style="{ width: '120px' }"
        >
          <el-descriptions-item label="文档名称">
            {{ viewForm.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="单位">
            {{ viewForm.projectName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="文档类型">
            <dict-tag :options="document_type" :value="viewForm.type" />
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ parseTime(viewForm.createTime) || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="文档附件">
            <attachment-display :attachments="viewForm.wlAnnexes" />
          </el-descriptions-item>
          <el-descriptions-item label="描述">
            <span>{{ viewForm.remark || '-' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="flex justify-center">
          <el-button class="cancel-btn" @click="viewOpen = false"
            >关闭</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { parseTime } from '@/utils/welink';
import FileUpload from '@/components/FileUpload/index.vue';
import {
  listDocument,
  getDocument,
  addDocument,
  updateDocument,
  delDocument,
} from '@/api/documentCenter/document';
import { downloadFile } from '@/utils/welink';
const { proxy } = getCurrentInstance();
import AttachmentDisplay from '@/components/AttachmentDisplay/index.vue';
// 字典数据
const { document_type } = proxy.useDict('document_type');

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 文档表格数据
const documentList = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 是否显示查看弹出层
const viewOpen = ref(false);
// 查看表单
const viewForm = ref({});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入文档数据');
const importUrl = '/railway/wlArchive/import';
const templateUrl = '/system/document/importTemplate';

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  type: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  name: undefined,
  type: undefined,
  projectId: undefined,
  projectName: undefined,
  fileUrl: undefined,
});

// 表单校验
const rules = ref({
  name: [{ required: true, message: '文档名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '文档类型不能为空', trigger: 'change' }],
  projectId: [{ required: true, message: '单位不能为空', trigger: 'change' }],
  fileUrl: [{ required: true, message: '文档附件不能为空', trigger: 'blur' }],
});

/** 查询文档列表 */
function getList() {
  loading.value = true;
  listDocument(queryParams.value).then((response) => {
    documentList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: undefined,
    type: undefined,
    projectId: undefined,
    projectName: undefined,
    fileUrl: undefined,
  };
  proxy.resetForm('documentForm');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加文档';
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getDocument(id).then((response) => {
    form.value = response.data;
    form.value.fileUrl = response.data.wlAnnexes?.[0]?.path;
    form.value.fileName = response.data.wlAnnexes?.[0]?.name;
    open.value = true;
    title.value = '编辑文档';
  });
}

/** 查看按钮操作 */
function handleView(row) {
  viewForm.value = row;
  viewForm.value.fileUrl = row.wlAnnexes?.[0]?.path;
  viewForm.value.fileName = row.wlAnnexes?.[0]?.name;
  viewOpen.value = true;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['documentForm'].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      const data = {
        ...form.value,
        wlAnnexes: [{ path: form.value.fileUrl }],
      };
      if (form.value.id != null) {
        updateDocument(data)
          .then((response) => {
            proxy.$modal.msgSuccess('编辑成功');
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addDocument(data)
          .then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id;
  const name = row.name;
  proxy.$modal
    .confirm('是否确认删除文档名称为"' + name + '"的数据项?')
    .then(function () {
      return delDocument(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlArchive/export',
    {
      ...queryParams.value,
    },
    `文档管理_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true;
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  getList();
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.document-view {
  padding: 20px;
}
.view-dialog .el-dialog__body {
  background: #f8fafc;
  padding: 24px 32px 16px 32px;
  border-radius: 8px;
}
.document-view {
  padding: 0;
}
.el-descriptions {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}
.el-descriptions__title {
  font-size: 18px;
  font-weight: bold;
  color: #2674fe;
  margin-bottom: 8px;
}
.el-descriptions-item__label {
  font-weight: 500;
  color: #333;
  background: #f3f7fa;
}
.el-descriptions-item__content {
  color: #555;
}
</style>
