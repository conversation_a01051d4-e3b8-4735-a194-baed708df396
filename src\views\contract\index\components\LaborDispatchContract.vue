<template>
  <div>
    <!-- 甲方（用人单位信息） -->
    <div class="form-section">
      <h3>甲方（用人单位信息）</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="单位" prop="projectId" required>
            <RemoteSelect
              v-model="form.projectId"
              v-model:modelName="form.projectName"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择单位"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="法定代表人" prop="legalPerson" required>
            <el-input
              v-model="form.legalPerson"
              placeholder="请输入法定代表人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="委托代理人" prop="contacts" required>
            <el-input v-model="form.contacts" placeholder="请输入委托代理人" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="邮政编码" prop="firstPostalCode" required>
            <el-input
              v-model="form.firstPostalCode"
              placeholder="请输入邮政编码"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="公司地址" prop="address" required>
            <el-input
              v-model="form.address"
              type="textarea"
              rows="3"
              placeholder="请输入公司地址"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- 乙方（职员） -->
    <div class="form-section">
      <h3>乙方（职员）</h3>
      <el-row :gutter="20">
        <el-col :span="8">
          <!-- 人员选择对话框 -->
          <el-form-item label="人员选择" prop="staffId" required>
            <user-select
              :modelName="form.name"
              v-model="form.staffId"
              @change="handleUserSelect"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证号码" prop="idNumber">
            <el-input
              v-model="form.idNumber"
              placeholder="请输入身份证号码"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="form.phone"
              placeholder="请输入联系电话"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="出生日期" prop="birthDate">
            <el-date-picker
              v-model="form.birthDate"
              type="date"
              placeholder="请选择出生日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="人员类型" prop="staffType">
            <el-select
              v-model="form.staffType"
              placeholder="请选择人员类型"
              :disabled="true"
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="性别" prop="gender">
            <el-select
              v-model="form.gender"
              placeholder="请选择性别"
              :disabled="true"
            >
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="邮政编码" prop="lastPostalCode">
            <el-input
              v-model="form.lastPostalCode"
              placeholder="请输入邮政编码"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="家庭地址" prop="censusRegister" required>
            <el-input
              v-model="form.censusRegister"
              type="textarea"
              rows="3"
              placeholder="请输入家庭地址"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="现居住地" prop="currentAddress" required>
            <el-input
              v-model="form.currentAddress"
              type="textarea"
              rows="3"
              placeholder="请输入现居住地"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="上传手印"
            prop="wlAnnexes1"
            required
            :rules="{ required: true, message: '请上传手印' }"
          >
            <file-upload
              v-model="form.wlAnnexes1"
              :limit="1"
              :fileType="['jpg', 'png', 'jpeg']"
            >
              <template #tip>
                <div class="upload-tip">
                  请上传规格200*100px的图片格式（底色要透明）
                </div>
              </template>
            </file-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- 其他信息 -->
    <div class="form-section">
      <h3>其他信息</h3>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="工作内容和工作地点"
            label-width="150"
            prop="workInfo"
            :rules="workInfoRules"
          >
            <div class="flex w-full whitespace-normal flex-wrap">
              乙方同意根据甲方需要，在
              <RemoteSelect
                v-model="form.projectId"
                v-model:modelName="form.projectName"
                url="/system/dept/list"
                labelKey="deptName"
                valueKey="deptId"
                responsePath="data"
                :extraParams="{ parentId: '0' }"
                placeholder="请选择单位"
                class="w-[200px] mx-1"
              />
              （单位或部门），从事

              <el-select
                class="mx-1 w-[200px]"
                v-model="form.jobContent"
                placeholder="请选择从事工作"
              >
                <el-option
                  v-for="dict in work_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              工作， 工作地点为
              <el-input
                class="mx-1 w-[200px]"
                v-model="form.jobAddress"
                placeholder="请输入工作地点"
                @input="validateWorkInfo"
              >
              </el-input>
              。 执行。
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="工作时间" prop="jobDayHour" required>
            <div class="flex w-full whitespace-normal flex-wrap">
              乙方的岗位实行
              <el-input
                class="mx-1 w-[200px]"
                v-model="form.jobDayHour"
                placeholder="请输入工时"
              >
              </el-input>
              /时 工时制。
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入备注"
              :rows="3"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";
import RemoteSelect from "@/components/RemoteSelect/index";
import FileUpload from "@/components/FileUpload/index";

const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  personnel_type: {
    type: Array,
    required: true,
  },
  sys_user_sex: {
    type: Array,
    required: true,
  },
  work_type: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["openUserSelect", "update:form"]);

// 工作信息校验规则
const workInfoRules = [
  {
    validator: (rule, value, callback) => {
      if (
        !props.form.projectName &&
        !props.form.jobContent &&
        !props.form.jobAddress
      ) {
        callback(new Error("单位、工作内容和工作地点不能为空"));
      } else if (!props.form.projectName) {
        callback(new Error("请输入单位"));
      } else if (!props.form.jobContent) {
        callback(new Error("请输入工作内容"));
      } else if (!props.form.jobAddress) {
        callback(new Error("请输入工作地点"));
      } else {
        callback();
      }
    },
    trigger: ["blur", "change"],
  },
];

// 验证工作信息
function validateWorkInfo() {
  // 获取表单实例并手动验证字段
  const formRef = props.form?.$parent?.$refs?.formRef;
  if (formRef) {
    formRef.validateField("workInfo");
  }
}

// 工作标准选项
const jobWay_options = [
  { value: 1, label: "标准工时" },
  { value: 2, label: "综合计算工时" },
  { value: 3, label: "不定时工作制" },
];

// 选择人员
function handleUserSelect(val) {
  props.form.idNumber = val.idNumber;
  props.form.phone = val.phone;
  props.form.birthDate = val.birthDate;
  props.form.joinCrscDate = val.joinCrscDate;
  props.form.staffType = val.staffType;
  props.form.currentAddress = val.currentAddress;
  props.form.name = val.name;
  props.form.gender = val.gender;
  props.form.firstEducation = val.firstEducation;
  props.form.highestEducation = val.highestEducation;
}
</script>

<style scoped>
.form-section {
  margin-bottom: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-section h3 {
  margin-bottom: 20px;
  font-weight: bold;
  color: #303133;
}
.upload-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.5;
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

div /deep/ .el-input-number .el-input__inner {
  text-align: left !important;
}
</style>
