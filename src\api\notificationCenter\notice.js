import request from "@/utils/request";

// 查询预警通知列表
export function listWarningNotice(query) {
  return request({
    url: "/railway/warnInfo/list",
    method: "get",
    params: query,
  });
}

// 查询系统通知详细
export function getNotice(noticeId) {
  return request({
    url: "/system/notice/" + noticeId,
    method: "get",
  });
}

// 新增系统通知
export function addNotice(data) {
  return request({
    url: "/system/notice",
    method: "post",
    data: data,
  });
}

// 编辑系统通知
export function updateNotice(data) {
  return request({
    url: "/system/notice",
    method: "put",
    data: data,
  });
}

// 删除预警通知
export function delWarningNotice(noticeId) {
  return request({
    url: "/railway/warnInfo/" + noticeId,
    method: "delete",
  });
}

// 一键已读系统通知
export function readAllNotice() {
  return request({
    url: "/railway/warnInfo/readAll",
    method: "post",
  });
}

// 阅读预警通知
export function readWarningNotice(noticeId) {
  return request({
    url: "/railway/warnInfo/" + noticeId,
    method: "get",
  });
}

// 查询流程通知待办列表
export function listFlowNotify(query) {
  return request({
    url: "/flow/notify/list",
    method: "get",
    params: query,
  });
}

// 获取流程通知待办详细信息
export function getFlowNotify(todoId) {
  return request({
    url: "/flow/notify/" + todoId,
    method: "get",
  });
}

// 标记通知信息为已读
export function readFlowNotify(todoId) {
  return request({
    url: "/flow/notify/" + todoId,
    method: "put",
  });
}
