<template>
  <div class="app-container">
    <import-excel
      v-model:visible="importOpen"
      :title="typeDict[form.type].importTitle"
      :import-url="typeDict[form.type].importUrl"
      :template-url="typeDict[form.type].templateUrl"
      @success="handleImportSuccess"
    />
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="font-bold text-lg">{{ title }}</span>
        </div>
      </template>
      <el-form
        ref="dispatchArticleForm"
        :model="form"
        :rules="rules"
        label-width="120px"
        v-loading="loading"
        :disabled="mode === 'view'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发文标题" prop="title">
              <el-input
                v-if="mode !== 'view'"
                v-model="form.title"
                placeholder="请输入发文标题"
              />
              <span v-else>{{ form.title }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件号" prop="code">
              <el-input
                v-if="mode !== 'view'"
                v-model="form.code"
                placeholder="请输入文件号"
              />
              <span v-else>{{ form.code }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发文抄录类型" prop="type">
              <el-radio-group
                v-if="mode !== 'view'"
                v-model="form.type"
                @change="
                  () => {
                    staffIds = [];
                    staffInfo = [];
                  }
                "
              >
                <el-radio
                  v-for="dict in dispatch_article_type"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
              <dict-tag
                v-else
                :options="dispatch_article_type"
                :value="form.type"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.projectNames">
            <el-form-item label="抄送单位" prop="projectId">
              <span>{{ form.projectNames }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="附件" prop="wlAnnexes">
          <file-upload
            :disabled="mode === 'view'"
            v-model="form.wlAnnexes"
            :fileSize="60"
            :limit="10"
            :fileType="['doc', 'docx', 'pdf']"
          />
        </el-form-item>

        <el-form-item label="消息内容" prop="content">
          <editor
            v-if="mode !== 'view'"
            v-model="form.content"
            :min-height="300"
          />
          <div v-else v-html="form.content" class="rich-text-content"></div>
        </el-form-item>
      </el-form>
      <div class="px-5">
        <div class="flex items-center justify-start">
          <span class="font-bold text-lg mr-2">人员信息</span>
          <template v-if="mode !== 'view'">
            <el-button type="primary" class="mr-2" @click="importOpen = true"
              >导入人员</el-button
            >
            <user-select
              v-model="staffIds"
              multiple
              @change="getStaffInfo"
              placeholder="请选择人员"
            />
          </template>
        </div>
        <el-table :data="staffInfo" border class="mt-2" ref="tableRef">
          <el-table-column
            label="姓名"
            prop="staffName"
            width="100"
            align="center"
          />
          <el-table-column
            label="身份证号"
            prop="idNumber"
            width="250"
            align="center"
          />
          <el-table-column label="性别" prop="gender" align="center">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column
            v-if="form.type !== 'RE'"
            label="项目部名称"
            prop="projectName"
            width="200"
            align="center"
          >
            <template #default="scope">
              <RemoteSelect
                v-if="mode !== 'view'"
                v-model="scope.row.projectId"
                v-model:modelName="scope.row.projectName"
                url="/system/dept/list"
                labelKey="deptName"
                valueKey="deptId"
                responsePath="data"
                :extraParams="{ parentId: '0' }"
                placeholder="请选择"
              />
              <span v-else>{{ scope.row.projectName }}</span>
            </template>
          </el-table-column>

          <!-- 根据不同类型显示不同的列 -->
          <JLColumn
            v-if="form.type === 'JL'"
            v-slot="scope"
            :view-mode="mode === 'view'"
          />
          <JOColumn
            v-if="form.type === 'JO'"
            v-slot="scope"
            :view-mode="mode === 'view'"
          />
          <REColumn
            v-if="form.type === 'RE'"
            v-slot="scope"
            :view-mode="mode === 'view'"
          />
          <STColumn
            v-if="form.type === 'ST'"
            v-slot="scope"
            :view-mode="mode === 'view'"
          />

          <el-table-column
            v-if="mode !== 'view'"
            label="操作"
            prop="action"
            width="60"
            align="center"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <el-form-item class="mt-4" v-if="mode !== 'view'">
          <div class="flex justify-center">
            <el-button @click="resetForm">重置</el-button>
            <el-button
              type="primary"
              @click="submitForm"
              :loading="buttonLoading"
              >保 存</el-button
            >
          </div>
        </el-form-item>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  getDispatchArticle,
  addDispatchArticle,
  updateDispatchArticle,
} from "@/api/documentCenter/dispatchArticle";
import { JOColumn, JLColumn, REColumn, STColumn } from "./components";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
const { proxy } = getCurrentInstance();
const { work_type, title_category, sys_user_sex } = proxy.useDict(
  "work_type",
  "title_category",
  "sys_user_sex"
);
const tableRef = ref(null);
const optins = {
  JO: "jobList",
  JL: "postList",
  RE: "transferList",
  ST: "skillList",
};
const router = useRouter();
const route = useRoute();
const typeDict = ref({
  JO: {
    importTitle: "定职人员导入",
    importUrl: "/railway/wlDispatchArticle/job/import",
    templateUrl: "/railway/wlDispatchArticle/job/importTemplate",
  },
  JL: {
    importTitle: "职称人员导入",
    importUrl: "/railway/wlDispatchArticle/post/import",
    templateUrl: "/railway/wlDispatchArticle/post/importTemplate",
  },
  RE: {
    importTitle: "调动人员导入",
    importUrl: "/railway/wlDispatchArticle/transfer/import",
    templateUrl: "/railway/wlDispatchArticle/transfer/importTemplate",
  },
  ST: {
    importTitle: "技能鉴定人员导入",
    importUrl: "/railway/wlDispatchArticle/skill/import",
    templateUrl: "/railway/wlDispatchArticle/skill/importTemplate",
  },
});
//
const staffIds = ref([]);
// 信息
const staffInfo = ref([]);
// 导入参数
const importOpen = ref(false);

// 字典数据
const { dispatch_article_type } = proxy.useDict("dispatch_article_type");

// 遮罩层
const loading = ref(false);
// 按钮loading
const buttonLoading = ref(false);
// 操作模式 add/edit/view
const mode = ref(route.query.mode || "add");
// 文章ID
const articleId = ref(route.query.id);

// 标题
const title = computed(() => {
  if (mode.value === "add") {
    return "新增发文抄录";
  } else if (mode.value === "edit") {
    return "编辑发文抄录";
  } else {
    return "查看发文抄录";
  }
});

// 表单参数
const form = ref({
  id: undefined,
  title: undefined,
  code: undefined,
  type: "JL",
  projectId: undefined,
  wlAnnexes: [],
  content: undefined,
});

// 表单校验
const rules = ref({
  wlAnnexes: [{ required: true, message: "附件不能为空", trigger: "blur" }],
  title: [{ required: true, message: "发文标题不能为空", trigger: "blur" }],
  code: [{ required: true, message: "文件号不能为空", trigger: "blur" }],
  type: [
    { required: true, message: "发文抄录类型不能为空", trigger: "change" },
  ],
  projectId: [
    { required: true, message: "抄送单位不能为空", trigger: "change" },
  ],
  content: [{ required: true, message: "消息内容不能为空", trigger: "blur" }],
});

/** 取消按钮 */
function cancel() {
  router.go(-1);
}
function handleDelete(row) {
  const index = staffInfo.value.findIndex((i) => i.staffId === row.staffId);
  staffInfo.value.splice(index, 1);
  staffIds.value = staffInfo.value.map((i) => i.staffId);
}
// 人员选择
function getStaffInfo(val) {
  val.map((i) => {
    i.staffId = i.id;
    i.id = null;
    i.staffName = i.name;
    i.engageWork = i.jobContent;
    i.postType = i.titleCategory;
    // 如果是调动，则需要处理一下数据
    if (form.value.type === "RE") {
      i.oldProjectId = i.projectId;
      i.oldProjectName = i.projectName;
      i.oldDeptId = i.deptId;
      i.oldDeptName = i.deptName;
      i.oldPostName = i.postName;
      i.oldPostId = i.postId;
      i.projectId = null;
      i.projectName == null;
      i.deptId = null;
      i.deptName = null;
      i.postId = null;
      i.postName = null;
    }
    i.jobLevel = i.positionLevel;
    if (form.value.type === "ST") {
      i.engageWork = i.skillIdentification;
    }
  });
  staffInfo.value = val;
}
// 导入成功
function handleImportSuccess(response) {
  response.data.forEach((element) => {
    const index = staffInfo.value.findIndex(
      (i) => i.staffId === element.staffId
    );
    if (index > -1) {
      staffInfo.value[index] = { ...element };
    } else staffInfo.value.push({ ...element });
  });
  staffIds.value = staffInfo.value.map((i) => i.staffId);
}
// 重置
function resetForm() {
  proxy.$refs["dispatchArticleForm"].resetFields();
}
// 保存之前校验
function beforeSubmit() {
  const column = tableRef.value.columns;
  const whiteList = ["备注", "操作", "档案号"];
  const boo = column.some((item) => {
    if (!whiteList.includes(item.label)) {
      if (!staffInfo.value.some((info) => info[item.property])) {
        proxy.$modal.msgError(`${item.label}不能为空`);
        return item;
      }
    }
  });
  console.log(staffInfo.value);

  return !boo;
}
/** 表单提交 */
function submitForm() {
  proxy.$refs["dispatchArticleForm"].validate((valid) => {
    if (valid) {
      if (!beforeSubmit()) return;
      buttonLoading.value = true;
      const data = {
        ...form.value,
        [optins[form.value.type]]: staffInfo.value,
      };
      if (form.value.id != null) {
        updateDispatchArticle(data)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            cancel();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addDispatchArticle(data)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            cancel();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 获取发文抄录详情 */
function getDetail() {
  if (articleId.value) {
    loading.value = true;
    getDispatchArticle(articleId.value, route.query.type).then((response) => {
      form.value = response.data;
      staffInfo.value = form.value[optins[form.value.type]];
      staffIds.value = staffInfo.value.map((i) => i.staffId);
      form.value.projectNames = staffInfo.value
        .map((i) => i.projectName)
        ?.join(",");
      loading.value = false;
    });
  }
}

onMounted(() => {
  if (mode.value !== "add") {
    getDetail();
  }
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
div /deep/ .el-input-number .el-input__inner {
  text-align: left !important;
}
.rich-text-content {
  width: 100%;
  min-height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
}
</style>
