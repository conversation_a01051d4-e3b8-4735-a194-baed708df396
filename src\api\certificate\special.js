import request from "@/utils/request";

// 查询特种作业证书列表
export function listSpecialCertificate(query) {
  return request({
    url: "/railway/wlCertificateSpecial/project/list",
    method: "get",
    params: query,
  });
}

// 查询特种作业证书详细
export function getSpecialCertificate(id) {
  return request({
    url: "/railway/wlCertificateSpecial/project/" + id,
    method: "get",
  });
}

// 新增特种作业证书
export function addSpecialCertificate(data) {
  return request({
    url: "/railway/wlCertificateSpecial/project",
    method: "post",
    data: data,
  });
}

// 编辑特种作业证书
export function updateSpecialCertificate(data) {
  return request({
    url: "/railway/wlCertificateSpecial/project",
    method: "put",
    data: data,
  });
}

// 删除特种作业证书
export function delSpecialCertificate(id) {
  return request({
    url: "/railway/wlCertificateSpecial/project/" + id,
    method: "delete",
  });
}

// 上传特种作业证书
export function uploadSpecialCertificate(data) {
  return request({
    url: "/railway/wlCertificateSpecial/project/upload",
    method: "post",
    data: data,
  });
}

// 获取证书上传状态字典
export const certificateStatusOptions = [
  { label: "未上传", value: "HAVEN_T_UPLOADED", elTagType: "danger" },
  { label: "已上传", value: "HAVE_ALREADY_UPLOADED" },
];

// 查询特种作业证书人员明细列表
export function listSpecialCertificateDetail(query) {
  return request({
    url: "/railway/wlCertificateSpecial/list",
    method: "get",
    params: query,
  });
}

// 查询特种作业证书人员明细详情
export function getSpecialCertificateDetail(id) {
  return request({
    url: "/railway/wlCertificateSpecial/" + id,
    method: "get",
  });
}

// 新增特种作业证书人员明细
export function addSpecialCertificateDetail(data) {
  return request({
    url: "/railway/wlCertificateSpecial",
    method: "post",
    data: data,
  });
}

// 编辑特种作业证书人员明细
export function updateSpecialCertificateDetail(data) {
  return request({
    url: "/railway/wlCertificateSpecial",
    method: "put",
    data: data,
  });
}

// 删除特种作业证书人员明细
export function delSpecialCertificateDetail(id) {
  return request({
    url: "/railway/wlCertificateSpecial/" + id,
    method: "delete",
  });
}

// 提交审核特种作业证书
export function submitAuditSpecialCertificate(id) {
  return request({
    url: "/railway/wlCertificateSpecial/submit/" + id,
    method: "put",
  });
}

// 核销特种作业证书
export function verifySpecialCertificate(id) {
  return request({
    url: "/railway/wlCertificateSpecial/verify/" + id,
    method: "put",
  });
}

// 启动流程接口（提审/核销）
export function launchProcess(data) {
  return request({
    url: "/railway/wlCertificateSpecial/launchProcess",
    method: "post",
    data: data,
  });
}

// 查询退出特种作业证书台账
export function listExitSpecialCertificate(query) {
  return request({
    url: "/railway/wlCertificateSpecial/retiree/list",
    method: "get",
    params: query,
  });
}
