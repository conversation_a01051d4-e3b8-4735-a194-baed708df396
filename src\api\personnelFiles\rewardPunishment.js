import request from "@/utils/request";

// 查询奖惩处罚列表
export function listRewardPunishment(query) {
  return request({
    url: "/railway/wlBonusPenalty/list",
    method: "get",
    params: query,
  });
}

// 查询奖惩处罚详细
export function getRewardPunishment(id) {
  return request({
    url: "/railway/wlBonusPenalty/" + id,
    method: "get",
  });
}

// 新增奖惩处罚
export function addRewardPunishment(data) {
  return request({
    url: "/railway/wlBonusPenalty",
    method: "post",
    data: data,
  });
}

// 修改奖惩处罚
export function updateRewardPunishment(data) {
  return request({
    url: "/railway/wlBonusPenalty",
    method: "put",
    data: data,
  });
}

// 删除奖惩处罚
export function delRewardPunishment(id, staffId) {
  return request({
    url: "/railway/wlBonusPenalty/" + id + "/" + staffId,
    method: "delete",
  });
}

// 导出奖惩处罚
export function exportRewardPunishment(query) {
  return request({
    url: "/railway/wlBonusPenalty/export",
    method: "get",
    params: query,
  });
}
