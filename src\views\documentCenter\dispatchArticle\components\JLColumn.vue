<template>
  <el-table-column
    label="出生年月"
    prop="birthDate"
    width="150"
    align="center"
  />
  <el-table-column
    label="从事工作"
    prop="engageWork"
    width="200"
    align="center"
  >
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.engageWork"
        placeholder="请选择从事工作"
      >
        <el-option
          v-for="dict in work_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag v-else :options="work_type" :value="slotScope.row.engageWork" />
    </template>
  </el-table-column>
  <el-table-column label="专业技术职务" prop="artId" width="200" align="center">
    <template #default="slotScope">
      <RemoteSelect
        v-if="!viewMode"
        v-model="slotScope.row.artId"
        v-model:modelName="slotScope.row.technicalPosition"
        url="/railway/wlArtSubsidy/list"
        responsePath="rows"
        labelKey="artName"
        valueKey="id"
        placeholder="请选择职务"
      />
      <span v-else>{{ slotScope.row.technicalPosition }}</span>
    </template>
  </el-table-column>
  <el-table-column
    label="技术津贴（元）"
    prop="artSubsidy"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.artSubsidy"
        placeholder="请输入技术津贴"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.artSubsidy }}</span>
    </template>
  </el-table-column>
  <el-table-column
    label="执行时间"
    prop="executeDate"
    width="170"
    align="center"
  >
    <template #default="slotScope">
      <el-date-picker
        v-if="!viewMode"
        v-model="slotScope.row.executeDate"
        type="date"
        style="width: 100%"
        placeholder="请选择执行时间"
        value-format="YYYY-MM-DD"
      />
      <span v-else>{{ slotScope.row.executeDate }}</span>
    </template>
  </el-table-column>
  <el-table-column label="备注" prop="remark" align="center" width="200">
    <template #default="slotScope">
      <el-input v-if="!viewMode" v-model="slotScope.row.remark" />
      <span v-else>{{ slotScope.row.remark }}</span>
    </template>
  </el-table-column>
</template>

<script setup>
import { getCurrentInstance } from "vue";

defineProps({
  viewMode: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();
const { work_type } = proxy.useDict("work_type");
</script>
