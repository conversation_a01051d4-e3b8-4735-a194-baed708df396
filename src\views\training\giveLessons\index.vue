<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="培训单位" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择培训单位"
            ></RemoteSelect>
          </el-form-item>
          <el-form-item label="授课班名" prop="className">
            <el-input
              class="w-[200px]"
              v-model="queryParams.className"
              placeholder="请输入授课班名"
              clearable
            />
          </el-form-item>
          <el-form-item label="授课教师" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.staffName"
              placeholder="请输入授课教师"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
        <div class="statistics-row">
          <div>课时数：{{ totalClassHour }}</div>
          <div>授课津贴：{{ totalAmount }} 元</div>
        </div>

        <el-table v-loading="loading" :data="teacherList">
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="授课单位" align="center" prop="projectName" />
          <el-table-column label="授课班名" align="center" prop="className" />
          <el-table-column
            label="授课教师名称"
            align="center"
            prop="staffName"
          />
          <el-table-column label="职务" align="center" prop="position">
            <template #default="scope">
              <dict-tag
                :options="professional_title"
                :value="scope.row.position"
              />
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center" prop="jobTitle">
            <template #default="scope">
              <dict-tag :options="title_category" :value="scope.row.jobTitle" />
            </template>
          </el-table-column>
          <el-table-column
            label="授课内容"
            align="center"
            prop="lectureContent"
          />
          <el-table-column label="参考资料" align="center" prop="reference" />
          <el-table-column label="资料是否自编" align="center" prop="isSelf">
            <template #default="scope">
              <span>{{ scope.row.isSelf === '1' ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="课时数" align="center" prop="classHour" />
          <el-table-column label="津贴标准" align="center" prop="artSubsidy" />
          <el-table-column label="金额" align="center" prop="artSubsidyTotal" />
          <el-table-column label="填报时间" align="center" prop="createTime">
            <template #default="scope">
              {{ parseTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="120"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import { listWlTeacher, getWlTeacher } from '@/api/training/giveLessons';
import { useRouter } from 'vue-router';

const router = useRouter();
const { proxy } = getCurrentInstance();

// 字典数据
const { professional_title, title_category } = proxy.useDict(
  'professional_title',
  'title_category'
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 教师授课表格数据
const teacherList = ref([]);

// 计算总课时
const totalClassHour = ref(0);

// 计算总金额
const totalAmount = ref(0);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  projectName: undefined,
  className: undefined,
  staffName: undefined,
});

/** 查询兼职教师授课信息列表 */
function getList() {
  loading.value = true;
  listWlTeacher(queryParams.value)
    .then((response) => {
      let list = response.data.list;
      teacherList.value = list.rows;
      total.value = list.total;
      totalAmount.value = response.data.price;
      totalClassHour.value = response.data.time;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  queryParams.value.projectName = undefined;

  handleQuery();
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: '/training/selfTraining/editor',
    query: { id: row.id, type: 'look' },
    meta: { activeMenu: location.pathname },
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlTeacher/warn/export',
    {
      ...queryParams.value,
    },
    `兼职教师授课津贴_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.statistics-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  div {
    min-width: 120px;
    padding: 8px 18px;
    background: #e9f1ff;
    border-radius: 4px;
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: center;
  }
}

.box-card-no-radius {
  border-radius: 0;
}
</style>
