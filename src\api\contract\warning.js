import request from "@/utils/request";

// 查询合同预警列表
export function listContractWarning(query) {
  return request({
    url: "/railway/wlContract/warn/list",
    method: "get",
    params: query,
  });
}

// 查询合同预警详细
export function getContractWarning(id) {
  return request({
    url: "/railway/wlContract/warning/" + id,
    method: "get",
  });
}

// 导出合同预警
export function exportContractWarning(query) {
  return request({
    url: "/railway/wlContract/warning/export",
    method: "get",
    params: query,
  });
}

// 续签预警合同
export function renewWarningContract(data) {
  return request({
    url: "/railway/wlContract/warning/renew",
    method: "post",
    data: data,
  });
}
