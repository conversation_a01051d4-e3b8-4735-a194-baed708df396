<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
      <div style="display: flex; align-items: center">
        <div>学历：</div>
        <el-select
          v-model="currentYear"
          placeholder="请选择最高学历"
          @change="handleChange"
          clearable
          class="w-[200px]"
        >
          <el-option
            v-for="dict in education_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </div>
    </div>
    <div ref="chartRef" class="chart"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, getCurrentInstance } from "vue";
import * as echarts from "echarts/core";
import { BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
} from "echarts/components";
import { LabelLayout, UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
const { proxy } = getCurrentInstance();
const { education_type } = proxy.useDict("education_type");
const emit = defineEmits(["change"]);
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});

const chartRef = ref(null);
const currentYear = ref("");
let chart = null;

const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    color: ["#08C3D2"],
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: props.chartData.xAxisData,
      // axisLabel: {
      //   interval: 0,
      //   rotate: 30
      // }
    },
    yAxis: {
      minInterval: 1,
      type: "value",
      // name: '人数',
      nameTextStyle: {
        color: "#999",
        fontSize: 12,
      },
    },

    series: [
      {
        name: "人数",
        type: "bar",
        barWidth: "22",
        label: {
          show: true,
          position: "top", // 展示在柱子的上方
        },
        data: props.chartData.seriesData.map((value) => ({
          value,
          // itemStyle: {
          //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //     { offset: 0, color: '#83bff6' },
          //     { offset: 0.5, color: '#188df0' },
          //     { offset: 1, color: '#188df0' }
          //   ])
          // },
          // emphasis: {
          //   itemStyle: {
          //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //       { offset: 0, color: '#2378f7' },
          //       { offset: 0.7, color: '#2378f7' },
          //       { offset: 1, color: '#83bff6' }
          //     ])
          //   }
          // }
        })),
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart && chart.resize();
  });
};

watch(
  () => props.chartData,
  () => {
    chart && chart.dispose();
    initChart();
  },
  { deep: true }
);

const handleChange = (value) => {
  emit("change", value);
};

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
}

.chart {
  width: 100%;
  height: 300px;
}
</style>
