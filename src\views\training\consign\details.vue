<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
    />
    <el-card class="box-card">
      <div class="card-header" style="margin-top: 0">
        <span class="card-title">培训基本信息</span>
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="mt-4"
        :disabled="isReadOnly"
      >
        <!-- 第一行 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="培训单位" prop="projectId">
              <RemoteSelect
                v-model="form.projectId"
                :multiple="false"
                url="/system/dept/list"
                labelKey="deptName"
                valueKey="deptId"
                :extraParams="{ parentId: '0' }"
                placeholder="请选择培训单位"
              ></RemoteSelect>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训班名" prop="className">
              <el-input v-model="form.className" placeholder="请输入培训班名" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训形式" prop="trainingMode">
              <el-select
                v-model="form.trainingMode"
                placeholder="请选择培训形式"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in training_form"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训开始时间" prop="beginTime">
              <el-date-picker
                v-model="form.beginTime"
                type="date"
                placeholder="选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="date"
                placeholder="选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训信息来源" prop="sources">
              <el-input
                v-model="form.sources"
                placeholder="请输入培训信息来源"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="集团内/外培训" prop="isInterior">
              <el-select
                v-model="form.isInterior"
                placeholder="请选择内/外培训"
                style="width: 100%"
              >
                <el-option label="内" value="1" />
                <el-option label="外" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训学校/单位" prop="trainingUnit">
              <el-input
                v-model="form.trainingUnit"
                placeholder="请输入培训学校/单位"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训内容" prop="trainingContent">
              <el-input
                v-model="form.trainingContent"
                type="textarea"
                :rows="3"
                placeholder="请输入培训内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 附件上传 -->
      <div class="card-header">
        <span class="card-title">上传附件</span>
      </div>
      <div style="width: 30%">
        <file-upload
          v-if="!isReadOnly"
          v-model="form.wlAnnexes"
          :fileType="fileType"
          :fileSize="60"
          :limit="10"
        ></file-upload>
        <attachment-display v-else :attachments="form.wlAnnexes" />
      </div>

      <!-- 培训效果评估与培训费金额报销 -->
      <div class="card-header">
        <span class="card-title">培训效果评估与培训费金额报销</span>
      </div>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            class="custom-btn"
            @click="handleImport"
            :disabled="isReadOnly"
            >导入人员</el-button
          >
        </el-col>
        <el-button
          class="custom-btn"
          type="primary"
          @click="handleAddPerson"
          :disabled="isReadOnly"
          >新增人员</el-button
        >
      </el-row>

      <el-table :data="evaluateCosts" border style="width: 100%">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="姓名" align="center" prop="staffName" />
        <el-table-column label="身份证号" align="center" prop="idNumber" />
        <el-table-column label="培训成绩" align="center" prop="score" />
        <el-table-column label="评估等级" align="center" prop="evaluateGrade">
          <template #default="scope">
            <dict-tag
              :options="evaluation_level"
              :value="scope.row.evaluateGrade"
            />
          </template>
        </el-table-column>
        <el-table-column label="评估(获证清况)" align="center" prop="remark" />
        <el-table-column
          label="附件"
          align="center"
          prop="wlAnnexes"
          width="300px"
        >
          <template #default="scope">
            <attachment-display :attachments="scope.row.wlAnnexes" />
          </template>
        </el-table-column>
        <el-table-column label="培训费" align="center" prop="trainingFee" />
        <el-table-column label="资料费" align="center" prop="materialFee" />
        <el-table-column label="交通费" align="center" prop="trafficFee" />
        <el-table-column label="住宿费" align="center" prop="roomFee" />
        <el-table-column label="办证费" align="center" prop="paperFee" />
        <el-table-column label="技能鉴定费" align="center" prop="identifyFee" />
        <el-table-column label="其他" align="center" prop="otherFee" />
        <el-table-column label="合计" align="center" prop="totalFee">
          <template #default="scope">
            <span>{{ calcTotalFee(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="职务主管" align="center" prop="financeStaff" />
        <el-table-column label="报批时间" align="center" prop="submitTime">
          <template #default="scope">
            {{ parseTime(scope.row.submitTime, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="150">
          <template #default="scope">
            <!-- <el-button
              type="text"
              size="small"
              @click="handleEditPerson(scope.row)"
              :disabled="isReadOnly"
            >编辑</el-button> -->
            <el-button
              type="text"
              size="small"
              @click="handleDeletePerson(scope.$index)"
              :disabled="isReadOnly"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="total-row">
        <span>合计：{{ totalAmount }} 元</span>
      </div>

      <!-- 按钮组 -->
      <div class="button-container">
        <el-button
          type="primary"
          @click="submitForm"
          :loading="btnLoading"
          v-if="!isReadOnly"
          >保存</el-button
        >
      </div>
    </el-card>

    <!-- 人员新增/编辑弹窗 -->
    <el-dialog
      v-model="personDialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      :show-close="false"
      class="person-dialog"
      append-to-body
    >
      <el-form
        ref="personFormRef"
        :model="personForm"
        :rules="personRules"
        label-width="120px"
        label-position="right"
        class="person-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="委培人员姓名" prop="staffName">
              <div
                class="input-with-select"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                "
              >
                <el-input
                  style="width: 75%"
                  disabled
                  v-model="personForm.staffName"
                  placeholder="请输入委培人员姓名"
                />
                <user-select
                  style="width: 20%"
                  v-model="personForm.staffId"
                  :showSelected="false"
                  @change="selectPerson"
                  buttonText="选择"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idNumber">
              <el-input
                disabled
                v-model="personForm.idNumber"
                placeholder="请输入身份证号码(系统自动填充)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训成绩" prop="score">
              <el-input-number
                v-model="personForm.score"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评估等级" prop="evaluateGrade">
              <el-select
                v-model="personForm.evaluateGrade"
                placeholder="请选择评估等级"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in evaluation_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="评估(获证情况)" prop="remark">
              <el-input
                v-model="personForm.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入评估(获证情况)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训费" prop="trainingFee">
              <el-input-number
                v-model="personForm.trainingFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资料费" prop="materialFee">
              <el-input-number
                v-model="personForm.materialFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交通费" prop="trafficFee">
              <el-input-number
                v-model="personForm.trafficFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="住宿费" prop="roomFee">
              <el-input-number
                v-model="personForm.roomFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="办证费" prop="paperFee">
              <el-input-number
                v-model="personForm.paperFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技能鉴定费" prop="identifyFee">
              <el-input-number
                v-model="personForm.identifyFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他" prop="otherFee">
              <el-input-number
                v-model="personForm.otherFee"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合计" prop="totalFee">
              <el-input-number
                v-model="personForm.totalFee"
                :min="0"
                :precision="2"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职务主管" prop="financeStaff">
              <el-input
                v-model="personForm.financeStaff"
                placeholder="请输入职务主管"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报批时间" prop="submitTime">
              <el-date-picker
                v-model="personForm.submitTime"
                type="date"
                placeholder="选择报批时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="评估附件">
              <file-upload
                v-model="personForm.wlAnnexes"
                :fileType="fileType"
                :fileSize="60"
                :limit="10"
                :disabled="isReadOnly"
              ></file-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button plain @click="cancelDialog">取消</el-button>
          <el-button type="primary" plain @click="submitPersonForm"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  getWlTrainingDepute,
  addWlTrainingDepute,
  updateWlTrainingDepute,
} from '@/api/training/consign';
import AttachmentDisplay from '@/components/AttachmentDisplay/index.vue';
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 字典数据
const { training_form, training_status, evaluation_level } = proxy.useDict(
  'training_form',
  'training_status',
  'evaluation_level'
);

const viewType = route.query.type;

// 计算属性：根据viewType判断是否为只读模式
const isReadOnly = computed(() => {
  return viewType === 'look';
});

// 表单参数
const formRef = ref(null);
const form = ref({
  id: undefined,
  projectId: undefined,
  className: '',
  trainingMode: undefined,
  beginTime: '',
  endTime: '',
  sources: '',
  isInterior: '1',
  trainingUnit: '',
  trainingContent: '',
  wlAnnexes: [],
  status: '0',
  evaluateCosts: [],
});

// 表单校验规则
const rules = ref({
  projectId: [
    { required: true, message: '培训单位不能为空', trigger: 'change' },
  ],
  className: [{ required: true, message: '培训班名不能为空', trigger: 'blur' }],
  trainingMode: [
    { required: true, message: '培训形式不能为空', trigger: 'change' },
  ],
  beginTime: [
    { required: true, message: '开始时间不能为空', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '结束时间不能为空', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' },
  ],
  sources: [
    { required: true, message: '培训信息来源不能为空', trigger: 'blur' },
  ],
  isInterior: [
    { required: true, message: '请选择是否内部培训', trigger: 'change' },
  ],
  trainingUnit: [
    { required: true, message: '培训学校/单位不能为空', trigger: 'blur' },
  ],
  trainingContent: [
    { required: true, message: '培训内容不能为空', trigger: 'blur' },
  ],
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入委培管理人员记录');
const importUrl = '/railway/wlTrainingDepute/importDataCost';
const templateUrl = '/railway/wlTrainingDepute/staff/importTemplate';

// 验证结束时间必须大于开始时间
function validateEndTime(rule, value, callback) {
  if (form.value.beginTime && value) {
    if (new Date(value) <= new Date(form.value.beginTime)) {
      callback(new Error('结束时间必须大于开始时间'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}

// 评估列表
const evaluateCosts = ref([]);

// 上传文件类型
const fileType = ref([
  'png',
  'jpg',
  'docx',
  'xlsx',
  'pptx',
  'pdf',
  'rar',
  'zip',
]);
const btnLoading = ref(false);

// 计算总金额
const totalAmount = computed(() => {
  return evaluateCosts.value
    .reduce((sum, item) => {
      return sum + (Number(item.totalFee) || 0);
    }, 0)
    .toFixed(2);
});
function calcTotalFee(row) {
  let total =
    row.trainingFee +
    row.materialFee +
    row.trafficFee +
    row.roomFee +
    row.paperFee +
    row.identifyFee +
    row.otherFee;
  row.totalFee = total;
  return total;
}

// 人员弹窗相关数据
const personDialogVisible = ref(false);
const dialogTitle = ref('新增人员');
const personFormRef = ref(null);
const editIndex = ref(-1);
const personForm = ref({
  staffId: '',
  staffName: '',
  idNumber: '',
  score: 0,
  evaluateGrade: '',
  remark: '',
  trainingFee: 0,
  materialFee: 0,
  trafficFee: 0,
  roomFee: 0,
  paperFee: 0,
  identifyFee: 0,
  otherFee: 0,
  totalFee: 0,
  financeStaff: '',
  submitTime: '',
  wlAnnexes: [],
});
let checkTotalHours = (rule, value, callback) => {
  if (Number(value) < 0) {
    callback(new Error('数值不能小于0'));
  }
  callback();
};
// 人员表单校验规则
const personRules = ref({
  staffName: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  idNumber: [{ required: true, message: '身份证号不能为空', trigger: 'blur' }],
  evaluateGrade: [
    { required: true, message: '评估等级不能为空', trigger: 'change' },
  ],
  remark: [{ required: true, message: '评估不能为空', trigger: 'blur' }],
  financeStaff: [
    { required: true, message: '职务主管不能为空', trigger: 'blur' },
  ],
  submitTime: [
    { required: true, message: '报批时间不能为空', trigger: 'change' },
  ],
  trainingFee: [
    { required: true, message: '培训费不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  materialFee: [
    { required: true, message: '资料费不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  trafficFee: [
    { required: true, message: '交通费不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  roomFee: [
    { required: true, message: '住宿费不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  paperFee: [
    { required: true, message: '办证费不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  identifyFee: [
    { required: true, message: '技能鉴定费不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  otherFee: [
    { required: true, message: '其他费用不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  totalFee: [
    { required: true, message: '合计不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
});

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true;
}

// 监听费用变化自动计算总额
watch(
  personForm,
  (newVal) => {
    const {
      trainingFee,
      materialFee,
      trafficFee,
      roomFee,
      paperFee,
      identifyFee,
      otherFee,
    } = newVal;

    personForm.value.totalFee =
      Number(trainingFee || 0) +
      Number(materialFee || 0) +
      Number(trafficFee || 0) +
      Number(roomFee || 0) +
      Number(paperFee || 0) +
      Number(identifyFee || 0) +
      Number(otherFee || 0);
  },
  { deep: true }
);

// 新增人员
function handleAddPerson() {
  resetPersonForm();
  dialogTitle.value = '新增人员';
  editIndex.value = -1;
  personDialogVisible.value = true;
}

// 编辑人员
function handleEditPerson(row) {
  dialogTitle.value = '编辑人员';
  const index = evaluateCosts.value.findIndex((item) => item === row);
  editIndex.value = index;
  personForm.value = JSON.parse(JSON.stringify(row));
  personDialogVisible.value = true;
}

// 删除人员
function handleDeletePerson(index) {
  proxy.$modal
    .confirm('确认删除该人员记录?')
    .then(() => {
      evaluateCosts.value.splice(index, 1);
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}
// 查看附件
function openPath(path) {
  if (!path) return;
  window.open(path);
}
// 选择人员
function selectPerson(user) {
  personForm.value.staffName = user.name;
  personForm.value.idNumber = user.idNumber || '';
}

// 处理附件上传
function handleFileChange(file) {
  // 处理附件上传
}

// 取消人员弹窗
function cancelDialog() {
  personDialogVisible.value = false;
  resetPersonForm();
}
/** 导入成功回调 */
function handleImportSuccess(response) {
  evaluateCosts.value = [...evaluateCosts.value, ...response.data];
}
// 提交人员表单
function submitPersonForm() {
  personFormRef.value.validate((valid) => {
    if (valid) {
      const personData = JSON.parse(JSON.stringify(personForm.value));

      // 附件处理
      personData.wlAnnexes = personForm.value.wlAnnexes.map((e) => {
        let value = {
          name: e.name,
          path: e.url,
        };
        return value;
      });

      if (editIndex.value > -1) {
        // 编辑模式
        evaluateCosts.value.splice(editIndex.value, 1, personData);
      } else {
        // 新增模式
        evaluateCosts.value.push(personData);
      }

      cancelDialog();
      proxy.$modal.msgSuccess(editIndex.value > -1 ? '修改成功' : '新增成功');
    }
  });
}

// 重置人员表单
function resetPersonForm() {
  personForm.value = {
    staffId: '',
    staffName: '',
    idNumber: '',
    score: 0,
    evaluateGrade: '',
    remark: '',
    trainingFee: 0,
    materialFee: 0,
    trafficFee: 0,
    roomFee: 0,
    paperFee: 0,
    identifyFee: 0,
    otherFee: 0,
    totalFee: 0,
    financeStaff: '',
    submitTime: '',
    wlAnnexes: [],
  };
  if (personFormRef.value) {
    personFormRef.value.resetFields();
  }
}

// 提交表单
function submitForm() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const submitForm = JSON.parse(JSON.stringify(form.value));
      submitForm.evaluateCosts = evaluateCosts.value;
      submitForm.beginTime = submitForm.beginTime + ' 00:00:00';
      submitForm.endTime = submitForm.endTime + ' 23:59:59';
      btnLoading.value = true;
      // 附件处理
      submitForm.wlAnnexes = form.value.wlAnnexes.map((e) => {
        let value = {
          name: e.name,
          path: e.url,
        };
        return value;
      });

      try {
        if (submitForm.id) {
          // 修改
          await updateWlTrainingDepute(submitForm);
          proxy.$modal.msgSuccess('修改成功');
        } else {
          // 新增
          await addWlTrainingDepute(submitForm);
          proxy.$modal.msgSuccess('新增成功');
        }
        goBack();
      } catch (error) {
        console.error('提交失败', error);
      } finally {
        btnLoading.value = false;
      }
    }
  });
}

// 返回按钮
function goBack() {
  router.replace('/training/consign');
}

// 获取详情
function getDetail(id) {
  getWlTrainingDepute(id).then((res) => {
    const data = res.data;
    form.value = {
      ...data,
      beginTime: data.beginTime ? data.beginTime.split(' ')[0] : '',
      endTime: data.endTime ? data.endTime.split(' ')[0] : '',
    };

    // 处理评估列表
    if (data.evaluateCosts && data.evaluateCosts.length) {
      evaluateCosts.value = data.evaluateCosts;
    }

    // 处理附件
    if (data.wlAnnexes) {
      form.value.wlAnnexes = data.wlAnnexes.map((e) => {
        return {
          name: e.name,
          url: e.path,
        };
      });
    } else {
      form.value.wlAnnexes = [];
    }
  });
}

// 页面加载时，如果有ID参数，则加载详情
onMounted(() => {
  const id = route.query.id;
  if (id) {
    getDetail(id);
  }
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .box-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      margin-top: 30px;

      .card-title {
        font-size: 16px;
        font-weight: bold;
        position: relative;
        padding-left: 10px;
      }
    }
  }

  .total-row {
    text-align: right;
    padding: 10px 0;
    font-weight: 400;
    font-size: 16px;
    background-color: #f5f6fb;
    padding-right: 50px;
  }

  .button-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 10px 0;
  }

  // 人员选择按钮样式
  .input-with-select {
    position: relative;

    .select-text {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #409eff;
      cursor: pointer;
      font-size: 14px;
    }

    .el-input {
      width: 100%;
    }
  }
}

.person-dialog {
  :deep(.el-dialog__header) {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    margin-right: 0;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .el-button {
      min-width: 80px;
      margin: 0 10px;
    }
  }
}
.elLink {
  cursor: pointer;
}
.elLink:hover {
  color: #409eff;
}
</style>
