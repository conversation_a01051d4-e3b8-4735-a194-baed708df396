<template>
  <div class="skeleton-examples">
    <h1>SkeletonLoader 使用示例</h1>

    <div class="example-section">
      <h2>1. 表单布局示例</h2>
      <div class="example-controls">
        <el-button @click="toggleFormLoading">
          {{ formLoading ? "停止加载" : "开始加载" }}
        </el-button>
      </div>
      <div class="example-content">
        <SkeletonLoader
          v-if="formLoading"
          layout="form"
          :show-search="true"
          :show-photo="true"
          :form-rows="8"
          :form-cols="4"
          :show-buttons="true"
        />
        <div v-else class="actual-content">
          <p>这里是实际的表单内容...</p>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h2>2. 表格布局示例</h2>
      <div class="example-controls">
        <el-button @click="toggleTableLoading">
          {{ tableLoading ? "停止加载" : "开始加载" }}
        </el-button>
      </div>
      <div class="example-content">
        <SkeletonLoader
          v-if="tableLoading"
          layout="table"
          :show-toolbar="true"
          :show-pagination="true"
          :table-rows="8"
          :table-cols="5"
        />
        <div v-else class="actual-content">
          <p>这里是实际的表格内容...</p>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h2>3. 列表布局示例</h2>
      <div class="example-controls">
        <el-button @click="toggleListLoading">
          {{ listLoading ? "停止加载" : "开始加载" }}
        </el-button>
      </div>
      <div class="example-content">
        <SkeletonLoader
          v-if="listLoading"
          layout="list"
          :list-items="6"
          :show-search="true"
        />
        <div v-else class="actual-content">
          <p>这里是实际的列表内容...</p>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h2>4. 详情页布局示例</h2>
      <div class="example-controls">
        <el-button @click="toggleDetailLoading">
          {{ detailLoading ? "停止加载" : "开始加载" }}
        </el-button>
      </div>
      <div class="example-content">
        <SkeletonLoader
          v-if="detailLoading"
          layout="detail"
          :detail-blocks="3"
          :show-search="false"
          :show-buttons="false"
        />
        <div v-else class="actual-content">
          <p>这里是实际的详情内容...</p>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h2>5. 自定义列数示例</h2>
      <div class="example-controls">
        <el-button @click="toggleCustomLoading">
          {{ customLoading ? "停止加载" : "开始加载" }}
        </el-button>
      </div>
      <div class="example-content">
        <SkeletonLoader
          v-if="customLoading"
          layout="form"
          :form-rows="5"
          :form-cols="[3, 4, 2, 4, 1]"
          :show-photo="false"
        />
        <div v-else class="actual-content">
          <p>这里是每行不同列数的表单...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import SkeletonLoader from "./index.vue";

const formLoading = ref(false);
const tableLoading = ref(false);
const listLoading = ref(false);
const detailLoading = ref(false);
const customLoading = ref(false);

const toggleFormLoading = () => {
  formLoading.value = !formLoading.value;
};

const toggleTableLoading = () => {
  tableLoading.value = !tableLoading.value;
};

const toggleListLoading = () => {
  listLoading.value = !listLoading.value;
};

const toggleDetailLoading = () => {
  detailLoading.value = !detailLoading.value;
};

const toggleCustomLoading = () => {
  customLoading.value = !customLoading.value;
};
</script>

<style scoped lang="scss">
.skeleton-examples {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    text-align: center;
    margin-bottom: 40px;
    color: #333;
  }

  .example-section {
    margin-bottom: 40px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    overflow: hidden;

    h2 {
      background: #f8f9fa;
      margin: 0;
      padding: 15px 20px;
      border-bottom: 1px solid #e6e6e6;
      color: #555;
    }

    .example-controls {
      padding: 15px 20px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .example-content {
      padding: 20px;
      min-height: 200px;

      .actual-content {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        background: #f8f9fa;
        border-radius: 6px;
        color: #666;
        font-size: 16px;
      }
    }
  }
}
</style>
