<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      :extraParams="{ projectId: form.projectId }"
      @success="getList"
    />
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="年月时间" prop="serviceTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.serviceTime"
              type="month"
              placeholder="选择年月"
              format="YYYY-MM"
              value-format="YYYY-MM"
              clearable
            />
          </el-form-item>
          <el-form-item label="单位名称" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择单位"
            ></RemoteSelect>
          </el-form-item>
          <el-form-item label="证书上传状态" prop="status">
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择上传状态"
              clearable
            >
              <el-option
                v-for="dict in certificateStatusOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="certificateList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间" align="center" prop="serviceTime" />
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column label="证书上传状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="certificateStatusOptions"
                :value="scope.row.newStatus"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="160"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleUpload(scope.row)"
                >上传</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑特种作业证书对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="certificateForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="时间" prop="serviceTime">
          <el-date-picker
            v-model="form.serviceTime"
            type="month"
            placeholder="选择年月"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item label="项目" prop="projectId">
          <RemoteSelect
            class="w-[200px]"
            v-model="form.projectId"
            v-modelName="form.projectName"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择项目单位"
          ></RemoteSelect>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import ImportExcel from '@/components/ImportExcel/index.vue';
import {
  listSpecialCertificate,
  addSpecialCertificate,
  updateSpecialCertificate,
} from '@/api/certificate/special';

import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
// 获取证书上传状态字典
const certificateStatusOptions = [
  { label: '未上传', value: 'HAVEN_T_UPLOADED', elTagType: 'danger' },
  { label: '已上传', value: 'HAVE_ALREADY_UPLOADED' },
];

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 上传按钮状态
const uploadLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 特种作业证书表格数据
const certificateList = ref([]);

// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  serviceTime: undefined,
  projectId: undefined,
  status: route.query.status,
});

// 表单参数
const form = ref({
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  serviceTime: undefined,
  status: 'HAVEN_T_UPLOADED',
});

// 上传表单
const uploadForm = ref({
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  serviceTime: undefined,
  fileUrl: undefined,
});

// 表单校验
const rules = ref({
  projectId: [{ required: true, message: '项目不能为空', trigger: 'change' }],
  serviceTime: [{ required: true, message: '时间不能为空', trigger: 'blur' }],
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入特种作业证书');
const importUrl = '/railway/wlCertificateSpecial/importData';
const templateUrl = '/railway/wlCertificateSpecial/importTemplate';

/** 查询特种作业证书列表 */
function getList() {
  // 开启加载状态
  loading.value = true;
  // 调用listSpecialCertificate函数并处理响应
  listSpecialCertificate(queryParams.value).then((response) => {
    // 更新证书列表
    certificateList.value = response.rows;
    // 更新总数
    total.value = response.total;
    // 关闭加载状态
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    projectId: undefined,
    projectName: undefined,
    serviceTime: undefined,
    status: 'HAVEN_T_UPLOADED',
  };
  proxy.resetForm('certificateForm');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 上传按钮操作 */
function handleUpload(row) {
  form.value.projectId = row.projectId;
  importOpen.value = true;
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: '/certificate/certificateSpecial/details',
    query: {
      projectId: row.projectId,
      serviceTime: row.serviceTime,
      projectName: row.projectName,
    },
    meta: { activeMenu: location.pathname },
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['certificateForm'].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateSpecialCertificate(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess('编辑成功');
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addSpecialCertificate(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.cert-preview {
  margin-top: 10px;
}
</style>
