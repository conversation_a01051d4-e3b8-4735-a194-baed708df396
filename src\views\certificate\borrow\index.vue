<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-tabs v-model="activeTab" type="border-card" class="mb-4">
          <el-tab-pane label="我发起的" name="myBorrow"></el-tab-pane>
          <el-tab-pane label="待审核" name="waitApproval"></el-tab-pane>
          <el-tab-pane label="待归还" name="waitReturn"></el-tab-pane>
          <el-tab-pane label="全部借阅" name="allBorrow"></el-tab-pane>
        </el-tabs>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="员工姓名" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.staffName"
              placeholder="请输入员工姓名"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="部门名称" prop="deptName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.deptName"
              placeholder="请输入部门名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="借阅状态" prop="borrowStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.borrowStatus"
              placeholder="请选择借阅状态"
              clearable
            >
              <el-option
                v-for="dict in borrow_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="borrowList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="借阅编号" align="center" prop="borrowNo" />
          <el-table-column label="员工姓名" align="center" prop="staffName" />
          <el-table-column label="部门名称" align="center" prop="deptName" />
          <el-table-column label="身份证号" align="center" prop="idNumber" />
          <el-table-column
            label="联系方式"
            align="center"
            prop="contactPhone"
          />
          <el-table-column
            label="借阅开始时间"
            align="center"
            prop="borrowStartTime"
            width="120"
          >
            <template #default="scope">
              <span>{{
                parseTime(scope.row.borrowStartTime, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="借阅结束时间"
            align="center"
            prop="borrowEndTime"
            width="120"
          >
            <template #default="scope">
              <span>{{
                parseTime(scope.row.borrowEndTime, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="借阅状态" align="center" prop="borrowStatus">
            <template #default="scope">
              <dict-tag
                :options="borrow_status"
                :value="scope.row.borrowStatus"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="借阅原因"
            align="center"
            prop="borrowReason"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="申请时间"
            align="center"
            prop="createTime"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="220"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                type="text"
                @click="handleUpdate(scope.row)"
                v-if="scope.row.borrowStatus === '0'"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleApprove(scope.row)"
                v-if="scope.row.borrowStatus === '1'"
                >审核</el-button
              >
              <el-button
                type="text"
                @click="handleReturn(scope.row)"
                v-if="scope.row.borrowStatus === '2'"
                >归还</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-if="
                  scope.row.borrowStatus === '0' ||
                  scope.row.borrowStatus === '3' ||
                  scope.row.borrowStatus === '4'
                "
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或修改证书借阅弹窗 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form
        ref="borrowForm"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="员工" prop="staffId">
              <user-select
                v-model="form.staffId"
                @change="handleUserChange"
                placeholder="请选择员工"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idNumber">
              <el-input v-model="form.idNumber" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactPhone">
              <el-input
                v-model="form.contactPhone"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="borrowStartTime">
              <el-date-picker
                v-model="form.borrowStartTime"
                type="date"
                placeholder="选择开始时间"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="borrowEndTime">
              <el-date-picker
                v-model="form.borrowEndTime"
                type="date"
                placeholder="选择结束时间"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="借阅原因" prop="borrowReason">
              <el-input
                v-model="form.borrowReason"
                type="textarea"
                placeholder="请输入借阅原因"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件" prop="attachmentFiles">
              <file-upload v-model="form.attachmentFiles" :limit="5" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 证书借阅审核弹窗 -->
    <el-dialog
      title="借阅审核"
      v-model="approveOpen"
      width="500px"
      append-to-body
    >
      <el-form
        ref="approveForm"
        :model="approveForm"
        :rules="approveRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="approveResult">
          <el-radio-group v-model="approveForm.approveResult">
            <el-radio :label="'2'">通过</el-radio>
            <el-radio :label="'3'">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="approveRemark">
          <el-input
            v-model="approveForm.approveRemark"
            type="textarea"
            placeholder="请输入审核意见"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="approveOpen = false"
            >取 消</el-button
          >
          <el-button type="primary" @click="submitApproveForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 证书归还弹窗 -->
    <el-dialog
      title="借阅归还"
      v-model="returnOpen"
      width="500px"
      append-to-body
    >
      <el-form
        ref="returnForm"
        :model="returnForm"
        :rules="returnRules"
        label-width="100px"
      >
        <el-form-item label="归还日期" prop="returnDate">
          <el-date-picker
            v-model="returnForm.returnDate"
            type="date"
            placeholder="选择归还日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="归还备注" prop="returnRemark">
          <el-input
            v-model="returnForm.returnRemark"
            type="textarea"
            placeholder="请输入归还备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="returnOpen = false"
            >取 消</el-button
          >
          <el-button type="primary" @click="submitReturnForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CertificateBorrow">
import {
  ref,
  reactive,
  toRefs,
  onMounted,
  watch,
  getCurrentInstance,
} from "vue";
import { parseTime } from "@/utils/welink";
import { deptTreeSelect } from "@/api/system/user";
import {
  listCertificateBorrow,
  getCertificateBorrow,
  addCertificateBorrow,
  updateCertificateBorrow,
  delCertificateBorrow,
  approveCertificateBorrow,
  returnCertificateBorrow,
} from "@/api/certificate/borrow";

const { proxy } = getCurrentInstance();
const { borrow_status } = proxy.useDict("borrow_status");

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 借阅表格数据
const borrowList = ref([]);
// 部门树选项
const deptOptions = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示审核弹出层
const approveOpen = ref(false);
// 是否显示归还弹出层
const returnOpen = ref(false);
// 当前选中的tab
const activeTab = ref("myBorrow");

// 表单参数
const formData = reactive({
  form: {
    id: undefined,
    borrowNo: undefined,
    staffId: undefined,
    staffName: undefined,
    deptId: undefined,
    deptName: undefined,
    idNumber: undefined,
    contactPhone: undefined,
    borrowStartTime: undefined,
    borrowEndTime: undefined,
    borrowStatus: "0", // 默认状态为草稿
    borrowReason: undefined,
    attachmentFiles: [],
  },
  rules: {
    staffId: [{ required: true, message: "请选择员工", trigger: "change" }],
    deptId: [{ required: true, message: "请选择所属部门", trigger: "change" }],
    idNumber: [
      { required: true, message: "请输入身份证号", trigger: "blur" },
      {
        pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        message: "请输入正确的身份证号码",
        trigger: "blur",
      },
    ],
    contactPhone: [
      { required: true, message: "请输入联系方式", trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    borrowStartTime: [
      { required: true, message: "请选择开始时间", trigger: "change" },
    ],
    borrowEndTime: [
      { required: true, message: "请选择结束时间", trigger: "change" },
    ],
    borrowReason: [
      { required: true, message: "请输入借阅原因", trigger: "blur" },
    ],
  },
});

// 审核表单参数
const approveFormData = reactive({
  approveForm: {
    id: undefined,
    approveResult: "2", // 默认通过
    approveRemark: undefined,
  },
  approveRules: {
    approveResult: [
      { required: true, message: "请选择审核结果", trigger: "change" },
    ],
  },
});

// 归还表单参数
const returnFormData = reactive({
  returnForm: {
    id: undefined,
    returnDate: undefined,
    returnRemark: undefined,
  },
  returnRules: {
    returnDate: [
      { required: true, message: "请选择归还日期", trigger: "change" },
    ],
  },
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffName: undefined,
  deptName: undefined,
  borrowStatus: undefined,
  tabType: "myBorrow", // 默认为我发起的
});

const { form, rules } = toRefs(formData);
const { approveForm, approveRules } = toRefs(approveFormData);
const { returnForm, returnRules } = toRefs(returnFormData);

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data;
  });
}

/** 查询借阅列表 */
function getList() {
  loading.value = true;
  queryParams.value.tabType = activeTab.value;
  listCertificateBorrow(queryParams.value)
    .then((response) => {
      borrowList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 监听tab变化重新查询 */
watch(activeTab, (newVal) => {
  queryParams.value.pageNum = 1;
  getList();
});

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    borrowNo: undefined,
    staffId: undefined,
    staffName: undefined,
    deptId: undefined,
    deptName: undefined,
    idNumber: undefined,
    contactPhone: undefined,
    borrowStartTime: undefined,
    borrowEndTime: undefined,
    borrowStatus: "0",
    borrowReason: undefined,
    attachmentFiles: [],
  };
  proxy.resetForm("borrowForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getDeptTree();
  open.value = true;
  title.value = "添加证书借阅";
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  getDeptTree();
  const id = row.id || row.borrowId;
  getCertificateBorrow(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "查看证书借阅";
    // 设置为只读状态
    proxy.setReadOnly();
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getDeptTree();
  const id = row.id || row.borrowId;
  getCertificateBorrow(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改证书借阅";
  });
}

/** 用户选择变更事件 */
function handleUserChange(user) {
  if (user) {
    form.value.staffName = user.userName || "";
    form.value.deptId = user.deptId;
    form.value.deptName = user.deptName || "";
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["borrowForm"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateCertificateBorrow(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCertificateBorrow(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || row.borrowId;
  proxy.$modal
    .confirm("是否确认删除该证书借阅记录？")
    .then(function () {
      return delCertificateBorrow(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "railway/wlCertificateBorrow/export",
    {
      ...queryParams.value,
    },
    `certificate_borrow_${new Date().getTime()}.xlsx`
  );
}

/** 审核按钮操作 */
function handleApprove(row) {
  approveForm.value.id = row.id || row.borrowId;
  approveForm.value.approveResult = "2";
  approveForm.value.approveRemark = undefined;
  approveOpen.value = true;
}

/** 提交审核表单 */
function submitApproveForm() {
  proxy.$refs["approveForm"].validate((valid) => {
    if (valid) {
      approveCertificateBorrow(approveForm.value).then((response) => {
        proxy.$modal.msgSuccess("审核完成");
        approveOpen.value = false;
        getList();
      });
    }
  });
}

/** 归还按钮操作 */
function handleReturn(row) {
  returnForm.value.id = row.id || row.borrowId;
  returnForm.value.returnDate = new Date().toISOString().slice(0, 10); // 默认今天
  returnForm.value.returnRemark = undefined;
  returnOpen.value = true;
}

/** 提交归还表单 */
function submitReturnForm() {
  proxy.$refs["returnForm"].validate((valid) => {
    if (valid) {
      returnCertificateBorrow(returnForm.value).then((response) => {
        proxy.$modal.msgSuccess("归还成功");
        returnOpen.value = false;
        getList();
      });
    }
  });
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: 0px !important;
  }
}
</style>
