<template>
  <div class="p-4 bg-white mb-4">
    <div class="flex flex-col">
      <div class="flex justify-between">
        <span class="section-title">{{ title }}</span>
        <div
          v-if="routeName"
          class="view-more"
          @click="handleClickCard(routeName)"
        >
          <span>查看更多</span>
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="flex w-full gap-4" v-if="stats.length <= 2">
        <div
          v-for="(stat, index) in stats"
          :key="index"
          class="rounded-[10px] py-6 px-2 flex items-center mt-4 flex-1"
          :style="{ backgroundColor: stat.bgColor }"
          @click="handleClickCard(stat.routeName)"
        >
          <img :src="stat.icon" class="w-[60px] h-[60px] mr-4 ml-8" />
          <div class="flex flex-col justify-between">
            <span>{{ stat.label }}</span>
            <span class="font-bold text-[28px] ml-2">{{ stat.value }}</span>
          </div>
        </div>
      </div>
      <template v-else>
        <div class="flex w-full gap-4">
          <div
            v-for="(stat, index) in stats.slice(0, 2)"
            :key="index"
            class="rounded-[10px] py-6 px-2 flex items-center mt-4 flex-1"
            :style="{ backgroundColor: stat.bgColor }"
            @click="handleClickCard(stat.routeName)"
          >
            <img :src="stat.icon" class="w-[60px] h-[60px] mr-4 ml-8" />
            <div class="flex flex-col justify-between">
              <span>{{ stat.label }}</span>
              <span class="font-bold text-[28px] ml-2">{{ stat.value }}</span>
            </div>
          </div>
        </div>
        <div class="flex w-full gap-4" v-if="stats.length > 2">
          <div
            v-for="(stat, index) in stats.slice(2)"
            :key="index + 2"
            class="rounded-[10px] py-6 px-2 flex items-center mt-4 flex-1"
            :style="{ backgroundColor: stat.bgColor }"
            @click="handleClickCard(stat.routeName)"
          >
            <img :src="stat.icon" class="w-[60px] h-[60px] mr-4 ml-8" />
            <div class="flex flex-col justify-between">
              <span>{{ stat.label }}</span>
              <span class="font-bold text-[28px] ml-2">{{ stat.value }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
const router = useRouter();
defineProps({
  title: {
    type: String,
    required: true,
  },
  routeName: {
    type: String,
    required: false,
  },
  stats: {
    type: Array,
    required: true,
  },
});

const handleClickCard = (routeName) => {
  if (routeName) {
    router.push({ name: routeName });
  }
};
</script>

<style lang="scss" scoped>
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
.view-more {
  display: flex;
  align-items: center;
  color: #2674fe;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #ebf7fd;
  }

  i {
    margin-left: 4px;
    font-size: 12px;
  }
}
</style>
