<template>
  <div class="summary-info-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="年月" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          placeholder="请选择年月"
          value-format="YYYY-MM"
          class="w-[200px]"
          clearable
        />
      </el-form-item>
      <el-form-item label="建设状态" prop="constructionStatus">
        <el-select
          v-model="queryParams.constructionStatus"
          placeholder="请选择建设状态"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in construction_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="机构属性" prop="orgAttribute">
        <el-select
          v-model="queryParams.orgAttribute"
          placeholder="请选择机构属性"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in organization_attribute"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员状态" prop="positionStatus">
        <el-select
          v-model="queryParams.positionStatus"
          placeholder="请选择人员状态"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in position_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery" class="reset-btn">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计数据展示 -->
    <div class="statistics-container">
      <div class="statistics-grid">
        <div
          class="stat-item"
          v-for="(item, index) in statisticsData"
          :key="index"
          :data-index="index"
        >
          <div class="stat-title">{{ item.title }}</div>
          <div class="stat-content">
            <span class="stat-label">上月：</span>
            <span class="stat-value">{{ item.lastNumber }}</span>
            <span class="stat-label">，本月：</span>
            <span class="stat-value">{{ item.nowNumber }}</span>
            <span
              class="stat-change"
              :class="getChangeClass(item.changeNumber)"
            >
              ，月{{ item.changeNumber >= 0 ? '新增' : '减少'
              }}{{ Math.abs(item.changeNumber) }}人
            </span>
          </div>
        </div>
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" class="custom-btn" @click="handleExport">
          导出汇总数据
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="summaryList" border>
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          <span>{{ getIndex(scope.$index) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="机构"
        align="center"
        prop="deptName"
        min-width="120"
      />
      <el-table-column label="职工" align="center" prop="zgNumber" width="80" />
      <el-table-column label="天诚" align="center" prop="tcNumber" />
      <el-table-column label="辅助性" align="center" prop="fzNumber" />
      <el-table-column label="自建班组辅助性" align="center" prop="bzNumber" />
      <el-table-column label="合计" align="center" prop="total" />
      <el-table-column
        label="定员定编"
        align="center"
        prop="staffQuota"
        width="100"
      >
        <template #default="scope"> -- </template>
      </el-table-column>
      <el-table-column
        label="不占定编（见习生、助勤）"
        align="center"
        prop="nonQuotaStaff"
        width="180"
      >
        <template #default="scope"> -- </template>
      </el-table-column>
      <el-table-column
        label="超减编情况"
        align="center"
        prop="quotaStatus"
        width="120"
      >
        <template #default="scope"> -- </template>
      </el-table-column>
      <el-table-column
        label="人员工资"
        align="center"
        prop="wages"
        width="100"
      />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        min-width="120"
      >
        <template #default="scope"> -- </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import {
  listSummaryInfo,
  getStatisticsData,
} from '@/api/instrument/dynamicPanel';
import { downloadFile } from '@/utils/welink';
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 汇总信息表格数据
const summaryList = ref([]);

// 字典数据
const { construction_status, organization_attribute, position_status } =
  proxy.useDict(
    'construction_status',
    'organization_attribute',
    'position_status'
  );

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  yearMonth: undefined, // 默认当前年月
  constructionStatus: undefined,
  orgAttribute: undefined,
  positionStatus: undefined,
});

// 统计数据
const statisticsData = ref([]);

/** 查询汇总信息列表 */
function getList() {
  loading.value = true;
  listSummaryInfo(queryParams.value).then((response) => {
    summaryList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取变化的样式类 */
function getChangeClass(change) {
  return {
    'stat-increase': change > 0,
    'stat-decrease': change < 0,
    'stat-no-change': change === 0,
  };
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 获取统计数据 */
function getStatistics() {
  getStatisticsData(queryParams.value).then((response) => {
    const titles = ['职工人数', '天诚人数', '辅助性人数'];
    response.data = response.data.map((item, index) => ({
      ...item,
      title: titles[index],
      changeNumber: item.nowNumber - item.lastNumber,
    }));
    // 计算总计
    // 上月
    const lastNumber = response.data.reduce(
      (acc, item) => acc + item.lastNumber,
      0
    );
    // 本月
    const nowNumber = response.data.reduce(
      (acc, item) => acc + item.nowNumber,
      0
    );
    // 变化
    const changeNumber = nowNumber - lastNumber;
    response.data.push({
      title: '总计',
      lastNumber,
      nowNumber,
      changeNumber,
    });
    statisticsData.value = response.data;
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    '/dynamics/export',
    {
      ...queryParams.value,
    },
    `汇总信息_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
  getStatistics();
});
</script>

<style scoped>
.summary-info-container {
  padding: 0;
}

.statistics-container {
  padding: 16px 0;
}

.statistics-grid {
  display: flex;
  gap: 12px;
}

.stat-item {
  background: white;
  padding: 16px 12px;
  border-radius: 10px;
  flex: 1;
  text-align: center;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f5f5f5;
  transition: all 0.3s ease;
}

.stat-item:hover {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
  border-color: #e0e0e0;
}

.stat-title {
  font-size: 13px;
  font-weight: 600;
  color: #555;
  margin-bottom: 8px;
  position: relative;
}

.stat-title::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
  vertical-align: middle;
}

.stat-item[data-index='0'] .stat-title::before {
  background: #ff6b6b;
}
.stat-item[data-index='1'] .stat-title::before {
  background: #4ecdc4;
}
.stat-item[data-index='2'] .stat-title::before {
  background: #45b7d1;
}
.stat-item[data-index='3'] .stat-title::before {
  background: #96c93d;
}

.stat-content {
  font-size: 11px;
  color: #666;
}

.stat-value {
  font-weight: 700;
  color: #333;
  font-size: 18px;
  margin: 0 2px;
}

.stat-change {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 8px;
  display: inline-block;
  margin-top: 4px;
}
</style>
