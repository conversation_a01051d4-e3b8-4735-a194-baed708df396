/**
 * 天然气管理
 */

import request from "@/utils/request";

// 查询天然气设备列表
export function listDevice(query) {
  return request({
    url: "/gas/device/list",
    method: "get",
    params: query,
  });
}

// 查询天然气设备详细
export function getDevice(deviceId) {
  return request({
    url: "/gas/device/" + deviceId,
    method: "get",
  });
}

// 新增天然气设备
export function addDevice(data) {
  return request({
    url: "/gas/device",
    method: "post",
    data: data,
  });
}

// 编辑天然气设备
export function updateDevice(data) {
  return request({
    url: "/gas/device",
    method: "put",
    data: data,
  });
}

// 删除天然气设备
export function delDevice(deviceId) {
  return request({
    url: "/gas/device/" + deviceId,
    method: "delete",
  });
}
