<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="预警级别" prop="ruleName">
            <el-input v-model="queryParams.ruleName" placeholder="预警级别" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="规则状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-tabs v-model="activeTab" @tab-click="handleTabChange">
          <el-tab-pane label="超员预警规则" name="OVER_MAN" />
          <el-tab-pane label="退休人员预警规则" name="RETIREE" />
          <el-tab-pane label="合同到期预警规则" name="EXPIRATION_OF_CONTRACT" />
          <el-tab-pane label="证书到期预警规则" name="CERTIFICATE_EXPIRES" />
        </el-tabs>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="earlyWarningList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="预警级别" align="center" prop="ruleName">
          </el-table-column>
          <el-table-column
            :label="thresholdLabel"
            align="center"
            prop="ruleValue"
          />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="150"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <!-- <el-button
                type="text"
                @click="handleDelete(scope.row)"
                
                >删除</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑预警规则配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="earlyWarningForm"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="预警级别" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="预警级别" />
        </el-form-item>
        <el-form-item :label="thresholdLabel" prop="ruleValue">
          <el-input-number
            class="w-full"
            :controls="false"
            v-model="form.ruleValue"
            :precision="0"
            :step="1"
            :min="0"
            placeholder="请输入规则阈值"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, computed } from "vue";
import {
  listEarlyWarning,
  getEarlyWarning,
  addEarlyWarning,
  updateEarlyWarning,
  delEarlyWarning,
} from "@/api/basic/earlyWarning";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_normal_disable, warning_level } = proxy.useDict(
  "sys_normal_disable",
  "warning_level"
);

// 激活的标签页
const activeTab = ref("OVER_MAN");

// 阈值标签文字
const thresholdLabel = computed(() => {
  switch (activeTab.value) {
    case "OVER_MAN":
      return "超员人数";
    case "RETIREE":
      return "临期月份";
    case "EXPIRATION_OF_CONTRACT":
      return "临期月份";
    case "CERTIFICATE_EXPIRES":
      return "临期月份";
    default:
      return "规则阈值";
  }
});

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 预警规则配置表格数据
const earlyWarningList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  ruleName: undefined,
  ruleType: activeTab.value,
  status: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  ruleName: undefined,
  ruleType: activeTab.value,
  ruleValue: 0,
  status: "0",
});

// 表单校验
const rules = ref({
  ruleName: [
    { required: true, message: "预警级别不能为空", trigger: "change" },
  ],
  ruleValue: [{ required: true, message: "规则阈值不能为空", trigger: "blur" }],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
});

/** 处理标签页切换 */
function handleTabChange(e) {
  activeTab.value = e.props.name;
  queryParams.value.ruleType = activeTab.value;
  getList();
}

/** 查询预警规则配置列表 */
function getList() {
  loading.value = true;
  queryParams.value.ruleType = activeTab.value;
  listEarlyWarning(queryParams.value).then((response) => {
    earlyWarningList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    ruleName: undefined,
    ruleType: activeTab.value,
    ruleValue: 0,
    status: "0",
  };
  proxy.resetForm("earlyWarningForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  form.value.ruleType = activeTab.value;
  open.value = true;
  title.value = "添加预警规则配置";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getEarlyWarning(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑预警规则配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["earlyWarningForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateEarlyWarning(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addEarlyWarning(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id;
  proxy.$modal
    .confirm('是否确认删除预警规则配置编号为"' + id + '"的数据项？')
    .then(function () {
      return delEarlyWarning(id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
div :deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}
</style>
