<template>
  <div>
    <!-- 排序按钮 -->
    <el-button type="text" @click="handleOpenDialog" class="text-[#425C96]">
      <el-icon class="ml-2"><Fold /></el-icon>
      <span> 自定义排序 </span>
    </el-button>

    <!-- 排序设置弹窗 -->
    <el-dialog
      title="自定义排序"
      v-model="dialogVisible"
      width="500px"
      append-to-body
    >
      <div class="sort-container">
        <!-- 排序字段选择区域 -->
        <div class="field-select">
          <div class="select-label">
            <span>排序字段：</span>
            <el-select
              v-model="tempSelectedField"
              placeholder="请选择排序字段"
              style="width: 100%; margin-top: 10px"
              clearable
            >
              <el-option
                v-for="item in filteredFieldOptions"
                :key="item.property"
                :label="item.label"
                :value="item.property"
              />
            </el-select>
          </div>

          <!-- 排序方式 -->
          <div class="sort-direction">
            <span class="required-label">排序方式：</span>
            <el-radio-group v-model="tempSortDirection">
              <el-radio :label="'asc'">升序</el-radio>
              <el-radio :label="'desc'">降序</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-center">
          <el-button @click="dialogVisible = false" class="cancel-btn"
            >取消</el-button
          >
          <el-button type="primary" @click="handleSort">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from "vue";

const props = defineProps({
  // 可选排序字段列表
  fieldOptions: {
    type: Array,
    default: () => [],
  },
  // 需要过滤的label数组
  filterLabels: {
    type: Array,
    default: () => [],
  },
  // 排序字段
  sortField: {
    type: String,
    default: "",
  },
  // 排序顺序
  sortOrder: {
    type: String,
    default: "asc",
  },
});

const emit = defineEmits([
  "sort-change",
  "update:sortField",
  "update:sortOrder",
]);

// 弹窗显示状态
const dialogVisible = ref(false);
// 临时存储选择的字段和排序方式，确认时才更新
const tempSelectedField = ref("");
const tempSortDirection = ref("asc");

// 监听props变化，更新临时存储的值
watch(
  () => props.sortField,
  (newVal) => {
    tempSelectedField.value = newVal;
  },
  { immediate: true }
);

watch(
  () => props.sortOrder,
  (newVal) => {
    tempSortDirection.value = newVal;
  },
  { immediate: true }
);

// 监听fieldOptions变化，不再自动设置默认字段
watch(
  () => props.fieldOptions,
  () => {
    // 移除自动选择第一个字段的逻辑
  },
  { immediate: true }
);

// 过滤后的字段选项
const filteredFieldOptions = computed(() => {
  if (!props.filterLabels.length) {
    return props.fieldOptions;
  }
  return props.fieldOptions.filter(
    (item) => !props.filterLabels.includes(item.label) && item.label
  );
});

// 打开排序弹窗
const handleOpenDialog = () => {
  dialogVisible.value = true;
  // 初始化临时值为当前绑定的值
  tempSelectedField.value = props.sortField;
  tempSortDirection.value = props.sortOrder;

  // 移除自动选择第一个字段的逻辑
};

// 执行排序
const handleSort = () => {
  // 更新绑定的值
  emit("update:sortField", tempSelectedField.value);
  emit("update:sortOrder", tempSortDirection.value);

  // 获取选中字段的详细信息（如果有选择字段的话）
  let label = "";
  if (tempSelectedField.value) {
    const fieldDetail = props.fieldOptions.find(
      (item) => item.property === tempSelectedField.value
    );
    label = fieldDetail?.label || "";
  }

  // 触发排序事件
  emit("sort-change", {
    prop: tempSelectedField.value,
    order: tempSortDirection.value,
    label: label,
  });

  // 关闭弹窗
  dialogVisible.value = false;
};
</script>

<style scoped>
.sort-container {
  padding: 10px;
}

.sort-direction {
  margin-top: 10px;
}

.required-label::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}
</style>
