<template>
  <div class="app-container">
    <el-card>
      <div class="basic-info">
        <div class="flex justify-between mb-4">
          <p class="section-title mt-5 mb-3">委培班次基本信息</p>
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">单位名称：</span>
                <span>{{ info.projectName || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训班名：</span>
                <span>{{ info.className || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训形式：</span>
                <dict-tag :options="training_form" :value="info.trainingMode" />
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训开始时间：</span>
                <span>{{
                  parseTime(info.beginTime, "{y}-{m}-{d}") || "--"
                }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训结束时间：</span>
                <span>{{
                  parseTime(info.endTime, "{y}-{m}-{d}") || "--"
                }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训信息来源：</span>
                <span>{{ info.sources || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训内容：</span>
                <span>{{ info.trainingContent || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">集团内/外培训：</span>
                <span>{{ info.isInterior == 1 ? "内" : "外" || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="info-item">
                <span class="label">培训学校或单位：</span>
                <span>{{ info.trainingUnit || "--" }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item">
                <span class="label">附件：</span>
                <attachment-display :attachments="info.wlAnnexes" />
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 兼职教师授课津贴报批 -->
        <div class="section-title mt-5 mb-3">培训效果评估与培训费金额报销</div>

        <el-table :data="evaluateCosts" border style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column label="姓名" align="center" prop="staffName" />
          <el-table-column label="身份证号" align="center" prop="idNumber" />
          <el-table-column label="培训成绩" align="center" prop="score" />
          <el-table-column label="评估等级" align="center" prop="evaluateGrade">
            <template #default="scope">
              <dict-tag
                :options="evaluation_level"
                :value="scope.row.evaluateGrade"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="评估(获证清况)"
            align="center"
            prop="remark"
          />
          <el-table-column
            label="附件"
            align="center"
            prop="wlAnnexes"
            width="300px"
          >
            <template #default="scope">
              <attachment-display :attachments="scope.row.wlAnnexes" />
            </template>
          </el-table-column>
          <el-table-column label="培训费" align="center" prop="trainingFee" />
          <el-table-column label="资料费" align="center" prop="materialFee" />
          <el-table-column label="交通费" align="center" prop="trafficFee" />
          <el-table-column label="住宿费" align="center" prop="roomFee" />
          <el-table-column label="办证费" align="center" prop="paperFee" />
          <el-table-column
            label="技能鉴定费"
            align="center"
            prop="identifyFee"
          />
          <el-table-column label="其他" align="center" prop="otherFee" />
          <el-table-column label="合计" align="center" prop="totalFee">
            <template #default="scope">
              <span>{{ calcTotalFee(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="职务主管"
            align="center"
            prop="financeStaff"
          />
          <el-table-column label="报批时间" align="center" prop="submitTime">
            <template #default="scope">
              {{ parseTime(scope.row.submitTime, "{y}-{m}-{d}") }}
            </template>
          </el-table-column>
        </el-table>

        <div class="total-row">
          <span>合计：{{ totalAmount }} 元</span>
        </div>

        <!-- 审核组件 -->
        <ProcessComponent :nodeInfo="nodeInfo" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { getWlTrainingDepute } from "@/api/training/consign";
import { useRoute, useRouter } from "vue-router";
import { getCurrentInstance } from "vue";
import ProcessComponent from "@/components/ProcessComponent";
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
import { getBusinessIdByInstanceId } from "@/api/process/audit";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
// 节点信息
const nodeInfo = ref({});
// 字典数据
const {
  training_form,
  training_type,
  professional_title,
  title_category,
  assessment_method,
  evaluation_level,
} = proxy.useDict(
  "training_form",
  "training_type",
  "professional_title",
  "title_category",
  "assessment_method",
  "evaluation_level"
);

const info = ref({});

const evaluateCosts = ref([]);
// 计算总金额
const totalAmount = computed(() => {
  return evaluateCosts.value
    .reduce((sum, item) => {
      return sum + (Number(item.totalFee) || 0);
    }, 0)
    .toFixed(2);
});
function calcTotalFee(row) {
  let total =
    row.trainingFee +
    row.materialFee +
    row.trafficFee +
    row.roomFee +
    row.paperFee +
    row.identifyFee +
    row.otherFee;
  row.totalFee = total;
  return total;
}

// 下载附件
function downloadFile(path) {
  if (path) {
    window.open(path);
  }
}

/** 查询委培详细 */
function getInfo(id) {
  getWlTrainingDepute(id).then((response) => {
    info.value = response.data;
    if (response.data.evaluateCosts) {
      evaluateCosts.value = response.data.evaluateCosts;
    } else {
      evaluateCosts.value = [];
    }
  });
}

/** 返回按钮 */
function goBack() {
  router.go(-1);
}

onMounted(async () => {
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  if (instanceId) {
    const res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
    nodeInfo.value = res.data;
  }
  if (id) {
    getInfo(id);
  }
});
</script>

<style scoped lang="scss">
.info-content {
  background-color: #f3f7fc;
  padding: 30px 5% 2px 5%;
}

.el-row {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 140px;
    text-align: left;
    color: #606266;
    padding-right: 10px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
}

.total-row {
  text-align: right;
  padding: 10px 0;
  font-weight: 400;
  font-size: 16px;
  background-color: #f5f6fb;
  padding-right: 50px;
}

.empty-annex {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}
.elLink {
  cursor: pointer;
}
.elLink:hover {
  color: #409eff;
}

@media screen and (max-width: 768px) {
  .info-item .label {
    min-width: 110px;
  }
}
</style>
