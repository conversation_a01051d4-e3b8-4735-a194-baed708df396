<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
    </div>
    <div class="chart-container">
      <div class="chart-wrapper">
        <div ref="chartRef" class="chart"></div>
      </div>
      <div class="legend-wrapper">
        <div
          class="legend-item"
          v-for="(item, index) in chartData.data"
          :key="index"
        >
          <div
            class="color-block"
            :style="{ backgroundColor: colors[index % colors.length] }"
          ></div>
          <span class="legend-text">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from "vue";
import * as echarts from "echarts/core";
import { PieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  CanvasRenderer,
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});

const chartRef = ref(null);
let chart = null;

const colors = [
  "#FEDB65",
  "#FFA06C",
  "#FB7293",
  "#E062AE",
  "#E690D1",
  "#E7BCF3",
  "#9FE6B8",
  "#9D96F5",
  "#8378EA",
  "#96BFFF",
  "#abd4f4",
  "#fee4cb",
];

const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c}人 ({d}%)",
    },
    series: [
      {
        name: "学历结构",
        type: "pie",
        radius: ["0", "55%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          normal: {
            show: true,
            textStyle: {
              fontSize: 12,
            },
            formatter: "{b}\n{c}人\n{d}%",
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "14",
            fontWeight: "bold",
          },
        },

        data: props.chartData.data?.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: colors[index % colors.length],
          },
        })),
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart && chart.resize();
  });
};

watch(
  () => props.chartData,
  (newVal, oldVal) => {
    chart && chart.dispose();
    props.chartData = newVal;
    initChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.chart-container {
  display: flex;
  height: 300px;
  margin-top: 10px;
}

.chart-wrapper {
  flex: 3;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.legend-wrapper {
  flex: 1;
  padding-left: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.color-block {
  width: 12px;
  height: 12px;
  margin-right: 8px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #333;
}
.card-header {
  min-height: 32px;
}
</style>
