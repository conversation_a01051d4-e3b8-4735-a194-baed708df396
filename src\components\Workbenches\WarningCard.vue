<template>
  <div class="p-4 bg-white">
    <div class="flex justify-between items-center">
      <span class="section-title">{{ title }}</span>
      <div class="view-more" @click="$emit('more')">
        <span>查看更多</span>
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
    </div>
    <!-- 预警类型tab -->
    <div class="bg-[#F5F6FB] p-2 px-4 flex gap-10 rounded-[8px] mt-2 relative">
      <span
        v-for="(type, idx) in warningTypes"
        :key="type.value"
        class="font-bold cursor-pointer relative"
        :class="{
          active: activeType === type.value,
          'cursor-not-allowed': loading,
        }"
        @click="$emit('change-type', type.value)"
      >
        {{ type.label }}
      </span>
    </div>
    <div class="mt-3"></div>
    <!-- 预警列表容器 -->
    <div class="warning-container" v-loading="loading">
      <!-- 预警列表 -->
      <div
        class="flex flex-col p-3 warning-border"
        v-for="(item, idx) in warningList"
        :key="idx"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-tag :type="item.tagType || 'danger'" effect="dark" round>
              {{ item.tagLabel }}
            </el-tag>

            <div class="flex flex-col ml-5">
              <span class="font-bold">
                {{ item.title }}
              </span>
              <span class="mt-1 text-sm text-[#999]">{{ item.time }}</span>
            </div>
          </div>
          <button class="watch-btn" @click="$emit('view', item)">查看</button>
        </div>
      </div>
      <div v-if="!warningList || warningList.length === 0">
        <Empty />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowRight } from "@element-plus/icons-vue";
import Empty from "@/components/Empty/index.vue";

defineEmits(["more", "change-type", "view"]);

defineProps({
  title: {
    type: String,
    default: "预警消息",
  },
  warningTypes: {
    type: Array,
    default: () => [],
  },
  activeType: {
    type: String,
    default: "",
  },
  warningList: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped>
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.view-more {
  display: flex;
  align-items: center;
  color: #2674fe;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #ebf7fd;
  }

  i {
    margin-left: 4px;
    font-size: 12px;
  }
}

.active {
  color: #2674fe;
}

.active::after {
  content: "";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -5px;
  width: 50%;
  height: 2px;
  background-color: #2674fe;
}

.warning-border {
  border-bottom: #d1d1d1 1px dotted;
}

.warning-container {
  position: relative;
  min-height: 100px;
}

.cursor-not-allowed {
  opacity: 0.6;
}

.watch-btn {
  min-width: 80px;
  height: 32px;
  border-radius: 16px;
  color: #0570c0;
  border: none;
  padding: 0 12px;
  font-size: 12px;
  cursor: pointer;
  background: #fff;
  border-radius: 18px;
  border: 1px solid #0570c0;
}
</style>
