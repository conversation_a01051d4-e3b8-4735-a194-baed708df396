<template>
  <div class="flex justify-center">
    <div class="flex overflow-auto pl-[65px]">
      <div
        class="flex flex-col items-start"
        v-for="(item, index) in nodeInfo.processInstanceNodeList"
        :key="item.nodeId"
      >
        <div class="flex flex-col">
          <div class="flex items-center justify-start">
            <div
              v-if="isShowIcon(item, index)"
              class="w-[36px] h-[36px] rounded-full z-10"
            >
              <img
                class="w-full h-full"
                v-show="item.status === '2'"
                src="@/assets/images/tgIcon.png"
              />
              <img
                class="w-full h-full"
                v-show="item.status === '3'"
                src="@/assets/images/bhIcon.png"
              />
            </div>
            <div v-else class="w-[36px] h-[36px] flex items-center">
              <div
                class="w-1/2 h-1/2 bg-[#F0F0F0] rounded-full z-10"
                :style="{
                  backgroundColor: getCircleBgColor(item, index),
                }"
              ></div>
              <div
                class="w-1/2 h-[4px] bg-[#F0F0F0] ml-[-1px]"
                :style="{
                  backgroundColor: getLineBgColor(item, index),
                }"
                v-show="index != nodeInfo.processInstanceNodeList.length - 1"
              ></div>
            </div>

            <div
              class="w-[200px] h-[4px] bg-[#F0F0F0] ml-[-1px]"
              :style="{
                backgroundColor: getLineBgColor(item, index),
              }"
              v-show="index != nodeInfo.processInstanceNodeList.length - 1"
            ></div>
          </div>
        </div>
        <div class="flex justify-center items-center mt-3">
          <div
            class="flex relative"
            :class="{
              'left-[-65px]': !isShowIcon(item, index),
              'left-[-59px]': isShowIcon(item, index),
            }"
          >
            <div class="flex items-center">
              <div class="w-[150px] text-center">
                <span
                  class="font-bold"
                  :style="{
                    color: getTextColor(item, index),
                  }"
                  >{{ item.nodeName }}</span
                >
                <div v-if="!index" class="flex flex-col">
                  <span class="mt-2 text-[#999999]">{{ item.updateTime }}</span>
                  <span class="mt-2 font-bold">{{ item.createBy }}</span>
                </div>
                <div v-else-if="index && item.status !== '1'">
                  <div
                    class="flex flex-col"
                    v-for="(
                      user, userIndex
                    ) in item.processInstanceNodeStaffList"
                    :key="userIndex"
                  >
                    <span class="mt-2 text-[#999999]">{{
                      user.updateTime
                    }}</span>
                    <span class="mt-2 font-bold">{{ user.createBy }}</span>
                    <span class="mt-2" v-show="user.instruction">
                      审核意见：{{ user.instruction }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  // 节点信息
  nodeInfo: {
    type: Object,
    required: true,
  },
});

// 获取节点线条颜色
function getLineBgColor(item, index) {
  const { nodeInfo } = props;
  let status = item.status;
  let nextStatus = nodeInfo.processInstanceNodeList[index + 1]?.status;
  // 如果是最后一个节点，并且前一个节点状态不是待审核则显示激活颜色
  if (index === nodeInfo.processInstanceNodeList.length - 1) {
    const lastStatus = nodeInfo.processInstanceNodeList[index - 1]?.status;
    if (lastStatus !== "1") {
      return "#2674FE";
    }
  }
  // 不是待审状态该节点也显示激活颜色
  if (nextStatus !== "1") {
    return "#2674FE";
  }
  // 如果当前节点不是待审核并且下一个节点是最后一个节点
  if (status !== "1" && index === nodeInfo.processInstanceNodeList.length - 2) {
    return "#2674FE";
  }
  return "#F0F0F0";
}

// 获取节点圆圈颜色
function getCircleBgColor(item, index) {
  const { nodeInfo } = props;
  let status = item.status;
  // 如果是最后一个节点，并且前一个节点状态不是待审核则显示激活颜色
  if (index === nodeInfo.processInstanceNodeList.length - 1) {
    const lastStatus = nodeInfo.processInstanceNodeList[index - 1]?.status;
    if (lastStatus !== "1") {
      return "#2674FE";
    }
  }
  if (status !== "1") {
    return "#2674FE";
  }

  return "#F0F0F0";
}

// 获取节点文字颜色
function getTextColor(item, index) {
  const { nodeInfo } = props;
  let status = item.status;
  // 如果是最后一个节点，并且前一个节点状态不是待审核则显示激活颜色
  if (index === nodeInfo.processInstanceNodeList.length - 1) {
    const lastStatus = nodeInfo.processInstanceNodeList[index - 1]?.status;
    if (lastStatus !== "1") {
      return "#2674FE";
    }
  }
  if (status !== "1") {
    return "#2674FE";
  }
  return "";
}

// 判断节点是否显示icon图标
function isShowIcon(item, index) {
  const { nodeInfo } = props;
  let status = item.status;
  if (index === 0 || index === nodeInfo.processInstanceNodeList.length - 1) {
    return false;
  }
  if (status !== "1") {
    return true;
  }

  return false;
}
</script>
