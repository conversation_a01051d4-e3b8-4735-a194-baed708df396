<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <el-form
        :inline="true"
        :model="queryParams"
        ref="queryForm"
        class="search-form"
      >
        <el-form-item label="人员类型" prop="staffType">
          <el-select
            v-model="queryParams.staffType"
            placeholder="请选择人员类型"
            clearable
            class="w-[200px]"
          >
            <el-option
              v-for="dict in personnel_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="单位名称" prop="projectId">
          <RemoteSelect
            v-model="queryParams.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            responsePath="data"
            placeholder="请选择单位名称"
            clearable
            class="w-[200px]"
            :extraParams="{ parentId: '0' }"
            @change="handleProjectChange"
          />
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <RemoteSelect
            v-model="queryParams.deptId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            responsePath="data"
            placeholder="请选择部门"
            clearable
            class="w-[200px]"
            :extraParams="{ parentId: 9999 }"
          />
        </el-form-item>
        <el-form-item label="岗位" prop="postId">
          <RemoteSelect
            v-model="queryParams.postId"
            url="/system/post/list"
            labelKey="postName"
            valueKey="postId"
            placeholder="请选择岗位"
            clearable
            class="w-[200px]"
          />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select
            v-model="queryParams.gender"
            placeholder="请选择性别"
            clearable
            class="w-[200px]"
          >
            <el-option label="全部性别" value="" />
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="社保身份" prop="socialSecurityStatus">
          <el-select
            v-model="queryParams.socialSecurityStatus"
            placeholder="请选择社保身份"
            clearable
            class="w-[200px]"
          >
            <el-option
              v-for="dict in social_security_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button class="reset-btn" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <div class="dashboard-container">
      <el-row :gutter="20">
        <!-- 学历结构 -->
        <el-col :xs="24" :sm="12" :md="10" class="dashboard-item">
          <education-chart :chartData="chartData.education" />
        </el-col>
        <!-- 职务级别分布 -->
        <el-col :xs="24" :sm="12" :md="14" class="dashboard-item">
          <position-level-chart
            :chartData="chartData.positionLevel"
            @change="fetchJobStructureData"
          />
        </el-col>

        <!-- 年龄结构 -->
        <el-col :xs="24" :sm="12" :md="10" class="dashboard-item">
          <age-structure-chart :chartData="chartData.ageStructure" />
        </el-col>
        <!-- 退休人员预测与分析 -->
        <el-col :xs="24" :sm="12" :md="14" class="dashboard-item">
          <recruitment-gender-chart
            :chartData="chartData.recruitmentGender"
            :queryParams="queryParams"
            ref="recruitmentGenderChart"
          />
        </el-col>

        <!-- 技术职称及工作情况 -->
        <el-col :span="24" class="dashboard-item" style="height: 490px">
          <technical-management-chart
            :chartData="chartData.technicalManagement"
            @change="fetchSkillsData"
          />
        </el-col>

        <!-- 自培信息统计与分析 -->
        <el-col :span="24" class="dashboard-item" style="height: 490px">
          <volunteer-info-chart :chartData="chartData.volunteerInfo" />
        </el-col>

        <!-- 委培评价等级占比分析 -->
        <el-col :xs="24" :sm="12" :md="10" class="dashboard-item">
          <training-evaluation-chart
            :chartData="chartData.trainingEvaluation"
          />
        </el-col>
        <!-- 委培费用排名 -->
        <el-col :xs="24" :sm="12" :md="14" class="dashboard-item">
          <training-cost-chart
            :chartData="chartData.trainingCost"
            @change="fetchDeputeCostData"
          />
        </el-col>

        <!-- 劳动生产率统计排名 -->
        <el-col :span="24" class="dashboard-item" style="height: 430px">
          <productivity-chart
            :chartData="chartData.productivity"
            @change="fetchProductivityData"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getCurrentInstance } from 'vue';
import EducationChart from './components/EducationChart.vue';
import PositionLevelChart from './components/PositionLevelChart.vue';
import AgeStructureChart from './components/AgeStructureChart.vue';
import RecruitmentGenderChart from './components/RecruitmentGenderChart.vue';
import TechnicalManagementChart from './components/TechnicalManagementChart.vue';
import VolunteerInfoChart from './components/VolunteerInfoChart.vue';
import TrainingEvaluationChart from './components/TrainingEvaluationChart.vue';
import TrainingCostChart from './components/TrainingCostChart.vue';
import ProductivityChart from './components/ProductivityChart.vue';
import RemoteSelect from '@/components/RemoteSelect';
import {
  getAgeStructure,
  getDegreeData,
  getDeputeAnalyseData,
  getDeputeCostData,
  getJobStructureData,
  getProductivityData,
  getRetireLineData,
  getSelfAnalyseData,
  getSkillsData,
} from '@/api/instrument';

const { proxy } = getCurrentInstance();
const recruitmentGenderChart = ref(null);
// 字典数据
const {
  sys_user_sex,
  professional_title,
  social_security_status,
  personnel_type,
  evaluation_level,
} = proxy.useDict(
  'sys_user_sex',
  'professional_title',
  'social_security_status',
  'personnel_type',
  'evaluation_level'
);

// 查询参数
const queryParams = ref({
  staffType: '',
  projectId: '',
  deptId: '',
  postId: '',
  gender: '',
  socialSecurityStatus: '',
});

/** 单位变更操作 */
function handleProjectChange(project) {
  if (project && project.deptId) {
    queryParams.value.deptId = undefined; // 重置部门ID
  } else {
    queryParams.value.projectId = undefined;
    queryParams.value.deptId = undefined;
  }
}

// 图表数据
const chartData = ref({
  education: {
    title: '学历结构',
    data: [],
  },
  positionLevel: {
    title: '职务级别分布',
    xAxisData: [],
    seriesData: [],
  },
  ageStructure: {
    title: '年龄结构',
    data: [],
  },
  recruitmentGender: {
    title: '退休人员预测与分析',
    years: [],
    data: [],
  },
  technicalManagement: {
    title: '技术职称及工作情况',
    dict: {
      age24: '24岁以下',
      age24z30: '24-30岁',
      age31z40: '31-40岁',
      age41z50: '41-50岁',
      age51z55: '51-55岁',
      age56: '56岁以上',
    },
    categories: [],
    data: [],
  },
  volunteerInfo: {
    title: '自培信息统计与分析',
    xAxis: [],
    staffNums: [],
    reportTimes: [],
    approveTimes: [],
    costs: [],
  },
  trainingEvaluation: {
    title: '委培评价等级占比分析',
    data: [],
  },
  trainingCost: {
    title: '委培费用排名',
    xAxis: [],
    data: [],
  },
  productivity: {
    title: '劳动生产率统计排名',
    data: [],
    dict: {},
  },
});

// 查询方法
const handleQuery = () => {
  // 根据查询条件获取数据
  fetchAllDashboardData();
};
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}
// 获取所有仪表盘数据
const fetchAllDashboardData = () => {
  recruitmentGenderChart.value.getList();
  fetchAgeStructureData();
  fetchDegreeData();
  fetchDeputeAnalyseData();
  fetchDeputeCostData();
  fetchJobStructureData();
  fetchProductivityData();
  fetchRetireData();
  fetchSelfAnalyseData();
  fetchSkillsData();
};

// 获取年龄结构数据
const fetchAgeStructureData = () => {
  getAgeStructure(queryParams.value).then((res) => {
    chartData.value.ageStructure.data = res.data.map((i) => {
      const keys = Object.keys(i);
      return {
        name: keys[0],
        value: i[keys[0]],
      };
    });
  });
};

// 获取学历数据
const fetchDegreeData = () => {
  getDegreeData(queryParams.value).then((res) => {
    chartData.value.education.data = res.data;
  });
};

// 获取委培评价数据
const fetchDeputeAnalyseData = () => {
  getDeputeAnalyseData(queryParams.value).then((res) => {
    chartData.value.trainingEvaluation.data = evaluation_level.value?.map(
      (item) => {
        const data = res.data?.find((j) => j.evaluateGrade == item.value);
        return { name: item.label, value: data ? data.num : 0 };
      }
    );
  });
};

// 获取委培费用排名数据
const fetchDeputeCostData = (params = {}) => {
  getDeputeCostData({ ...queryParams.value, ...params }).then((res) => {
    if (res.code === 200) {
      chartData.value.trainingCost.xAxis = res.data?.map(
        (item) => item.evaluateGrade
      );
      chartData.value.trainingCost.data = res.data?.map((item) => item.num);
    }
  });
};

// 获取职务级别分布数据
const fetchJobStructureData = (val) => {
  getJobStructureData({ ...queryParams.value, degree: val }).then((res) => {
    chartData.value.positionLevel.xAxisData = res.data?.map((i) => i.name);
    chartData.value.positionLevel.seriesData = res.data?.map((i) => i.value);
  });
};

// 获取劳动生产率统计排名数据
const fetchProductivityData = (val = '') => {
  getProductivityData({ ...queryParams.value, yearMonth: val }).then((res) => {
    // 更新劳动生产率统计排名图表数据
    chartData.value.productivity.xAxis = res.data?.map((i) => i.projectName);
    chartData.value.productivity.data = res.data?.map((i) => {
      chartData.value.productivity.dict[i.projectName] = i;
      return i.avgAmount;
    });
  });
};
// 获取字典标签
function getDictLabel(dict, value) {
  const item = dict.find((d) => d.value === value);
  return item ? item.label : '';
}

// 获取退休人员预测与分析数据
const fetchRetireData = () => {
  // 获取退休人员预测与分析曲线数据
  getRetireLineData(queryParams.value).then((res) => {
    chartData.value.recruitmentGender.years = res.data.year;
    delete res.data.year;
    const arr = [];
    for (const key in res.data) {
      arr.push({
        name: getDictLabel(sys_user_sex.value, key),
        value: res.data[key],
      });
    }
    chartData.value.recruitmentGender.data = arr;
  });
};

// 获取自培信息统计与分析数据
const fetchSelfAnalyseData = () => {
  getSelfAnalyseData(queryParams.value).then((res) => {
    // 更新自培信息统计与分析图表数据
    const arr = res.data?.reverse();
    chartData.value.volunteerInfo.xAxis = arr.map((item) => item.name);
    // 培训人数
    chartData.value.volunteerInfo.staffNums = arr.map((item) => item.staffNum);
    // 上报课时
    chartData.value.volunteerInfo.reportTimes = arr.map(
      (item) => item.reportTime
    );
    // 审批课时
    chartData.value.volunteerInfo.approveTimes = arr.map(
      (item) => item.approveTime
    );
    // 学习费用
    chartData.value.volunteerInfo.costs = arr.map((item) => item.costs);
  });
};

// 获取技术职称及工作情况数据
const fetchSkillsData = (params = {}) => {
  getSkillsData({ ...queryParams.value, ...params }).then((res) => {
    const categories = [];
    const data = [];
    res.data?.forEach((i) => {
      const name = getDictLabel(professional_title.value, Object.keys(i)?.[0]);
      categories.push(name);
    });
    for (const key in chartData.value.technicalManagement.dict) {
      const value = [];
      const label = chartData.value.technicalManagement.dict[key];
      res.data.forEach((i) => {
        value.push(Object.values(i)?.[0]?.[key]);
      });
      data.push({ name: label, data: value });
    }
    chartData.value.technicalManagement.categories = categories;
    chartData.value.technicalManagement.data = data;
  });
};

// 初始化数据
onMounted(() => {
  // 页面加载时获取初始数据
  fetchAllDashboardData();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.dashboard-item {
  margin-bottom: 20px;
  height: 400px;
}

.box-card {
  margin-bottom: 20px;
}
</style>
