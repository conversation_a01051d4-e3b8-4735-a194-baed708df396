<template>
  <el-card class="chart-card">
    <div slot="header" class="card-header">
      <span class="font-bold">入职机构排名</span>
      <div class="search-filters">
        <el-date-picker
          v-model="params.yearMonth"
          type="month"
          placeholder="选择年月"
          style="width: 200px; margin-right: 10px"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleSearch"
        />
        <RemoteSelect
          v-model="params.projectId"
          url="/system/dept/list"
          labelKey="deptName"
          valueKey="deptId"
          responsePath="data"
          placeholder="选择单位"
          hasDefault
          :clearable="false"
          style="width: 200px"
          :extraParams="{ parentId: '0' }"
          @change="handleSearch"
        />
      </div>
    </div>
    <div class="chart-container" ref="chartRef"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from "vue";
import * as echarts from "echarts";
import RemoteSelect from "@/components/RemoteSelect";

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["more", "search"]);

const chartRef = ref(null);
let chartInstance = null;
let resizeObserver = null;

const initChart = async () => {
  await nextTick();

  if (!chartRef.value) {
    console.warn("Chart container not found");
    return;
  }

  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 确保容器有尺寸
  const container = chartRef.value;
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    setTimeout(() => {
      initChart();
    }, 100);
    return;
  }

  chartInstance = echarts.init(container);
  updateChart();

  // 添加resize监听
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  window.addEventListener("resize", handleResize);

  // 使用ResizeObserver监听容器尺寸变化
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      handleResize();
    });
    resizeObserver.observe(container);
  }
};

const updateChart = () => {
  if (!chartInstance) return;
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        return params[0].name + "<br/>" + "入职人数: " + params[0].value + "人";
      },
    },
    grid: {
      left: "8%",
      right: "8%",
      bottom: "8%",
      top: "8%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      axisLabel: {
        fontSize: 12,
        color: "#666",
      },
      minInterval: 1,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#f0f0f0",
          type: "dashed",
        },
      },
    },
    yAxis: {
      type: "category",
      data: props.chartData.organizations || [],
      axisLabel: {
        interval: 0,
        fontSize: 12,
        color: "#666",
        formatter: function (value) {
          return value.length > 8 ? value.substring(0, 8) + "..." : value;
        },
      },
    },
    series: [
      {
        name: "入职人数",
        type: "bar",
        data: props.chartData.data || [],
        itemStyle: {
          color: function (params) {
            // 根据排名设置不同颜色
            const colors = [
              "#FF6B6B",
              "#4ECDC4",
              "#45B7D1",
              "#96CEB4",
              "#FFEAA7",
              "#DDA0DD",
              "#98D8C8",
              "#F7DC6F",
              "#BB8FCE",
              "#85C1E9",
            ];
            return colors[params.dataIndex] || "#85C1E9";
          },
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        label: {
          show: true,
          position: "right",
          fontSize: 12,
          color: "#666",
          formatter: "{c}人",
        },
      },
    ],
  };

  chartInstance.setOption(option, true);

  // 强制resize确保图表正确显示
  setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 100);
};

const handleMore = () => {
  emit("more");
};

const handleSearch = () => {
  emit("search", props.params);
};

watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

onMounted(() => {
  // 延迟初始化确保DOM完全渲染
  setTimeout(() => {
    initChart();
  }, 200);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  window.removeEventListener("resize", () => {});
});
</script>

<style scoped>
.chart-card {
  height: 450px;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  color: #2c3e50;
}

.search-filters {
  display: flex;
  align-items: center;
}

.chart-container {
  height: 390px;
  width: 100%;
  min-height: 390px;
  position: relative;
}
</style>
