<template>
  <div class="bg-[#f3f7fc]">
    <el-card class="custom-card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="项目" prop="projectId">
          <RemoteSelect
            class="w-[200px]"
            v-model="queryParams.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择项目"
          ></RemoteSelect>
        </el-form-item>
        <el-form-item label="姓名" prop="staffName">
          <el-input
            class="w-[200px]"
            v-model="queryParams.staffName"
            placeholder="请输入姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="岗位" prop="postId">
          <RemoteSelect
            v-model="queryParams.postId"
            url="/system/post/list"
            labelKey="postName"
            valueKey="postId"
            placeholder="请选择岗位"
            class="w-[200px]"
          ></RemoteSelect>
        </el-form-item>
        <el-form-item label="人员类型" prop="staffType">
          <el-select
            class="w-[200px]"
            v-model="queryParams.staffType"
            placeholder="请选择人员类型"
            clearable
          >
            <el-option
              v-for="dict in personnel_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="证书类别" prop="certificateCategory">
          <el-select
            class="w-[200px]"
            v-model="queryParams.certificateCategory"
            placeholder="请选择证书类别"
            clearable
          >
            <el-option
              v-for="dict in certificate_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="证书编号" prop="certificateCode">
          <el-input
            class="w-[200px]"
            v-model="queryParams.certificateCode"
            placeholder="请输入证书编号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="证书状态" prop="expirationStatus">
          <el-select
            class="w-[200px]"
            v-model="queryParams.expirationStatus"
            placeholder="请选择证书状态"
            clearable
          >
            <el-option
              v-for="dict in certificate_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery" class="reset-btn">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <el-row :gutter="10" class="mb8">
        <!-- 只保留导出按钮 -->
        <el-col :span="1.5">
          <el-button class="custom-btn" @click="handleExport">导出</el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="certificateList">
        <el-table-column type="index" label="序号" width="50" align="center">
          <template #default="scope">
            <span>{{ getIndex(scope.$index) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="姓名" align="center" prop="staffName" />
        <el-table-column
          label="身份证号码"
          align="center"
          prop="idNumber"
          width="160"
        />
        <el-table-column label="人员类型" align="center" prop="staffType">
          <template #default="scope">
            <dict-tag :options="personnel_type" :value="scope.row.staffType" />
          </template>
        </el-table-column>
        <el-table-column label="岗位" align="center" prop="postName" />
        <el-table-column
          label="证书编号"
          align="center"
          prop="certificateCode"
        />
        <el-table-column
          label="证书名称"
          align="center"
          prop="certificateMajor"
        />
        <el-table-column
          label="证书类别"
          align="center"
          prop="certificateCategory"
        >
          <template #default="scope">
            <dict-tag
              :options="certificate_category"
              :value="scope.row.certificateCategory"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="主专业"
          align="center"
          prop="certificateMajor"
        />
        <el-table-column label="签发日期" align="center" prop="signDate">
          <template #default="scope">
            <span>{{ parseTime(scope.row.signDate, '{y}-{m-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="主专业有效期"
          align="center"
          prop="effectiveDate"
          width="120"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="证书状态" align="center" prop="status">
          <template #default="scope">
            <span>{{ scope.row.status }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="RegistrationPostCertificateLedger">
// 修改组件名称
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { parseTime } from '@/utils/welink';
import {
  listExitRegisterCertificate,
  delCertificateEnroll,
} from '@/api/certificate/enroll'; // API路径也可能需要修改

const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const {
  personnel_type,
  certificate_category,
  certificate_status, // 保留证书状态
} = proxy.useDict(
  'personnel_type',
  'certificate_category',
  'certificate_status'
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 证书表格数据
const certificateList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectName: undefined,
  staffName: undefined,
  postName: undefined,
  staffType: undefined,
  certificateCategory: undefined,
  certificateCode: undefined,
  expirationStatus: undefined, // 保留证书状态查询
});

/** 查询注册/岗位证书列表 */
function getList() {
  loading.value = true;

  listExitRegisterCertificate(queryParams.value)
    .then((response) => {
      certificateList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: '/certificate/certificateEnroll/form',
    query: { id: row.id, view: true, from: 'exitRegistrationPostView' }, // 添加来源标记
    meta: { activeMenu: location.pathname },
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(`确认要删除"${row.staffName}"的注册/岗位证书信息吗？`)
    .then(function () {
      return delCertificateEnroll(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlCertificateEnroll/retire/export',
    {
      ...queryParams.value,
    },
    `退出人员注册岗位证书信息_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
<style scoped>
.custom-card {
  border: unset;
  background-color: #fff;
  box-shadow: unset;
  border-radius: 0 0 10px 10px;
}
</style>
