<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <div class="w-full">
            <span class="font-bold relative">培训计划基本信息：</span>
          </div>
          <el-form-item label="时间" prop="serviceTime">
            <el-date-picker
              v-model="projectRow.serviceTime"
              type="year"
              placeholder="选择年"
              clearable
              class="w-[200px]"
              format="YYYY"
              value-format="YYYY"
              disabled
            />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="projectRow.projectName"
              placeholder="请输入项目名称"
              clearable
              class="w-[200px]"
              @keyup.enter="handleQuery"
              disabled
            />
          </el-form-item>
          <el-form-item label="培训计划数量" prop="num">
            <el-input-number
              v-model="projectRow.num"
              :min="0"
              placeholder="数量"
              controls-position="right"
              class="w-[200px]"
              disabled
            />
          </el-form-item>
          <el-form-item label="上传状态" prop="status">
            <el-select
              v-model="projectRow.status"
              placeholder="请选择状态"
              clearable
              class="w-[200px]"
              @change="handleQuery"
              disabled
            >
              <el-option
                v-for="item in planStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <div class="w-full">
            <span class="font-bold relative">培训计划详情列表：</span>
          </div>
          <el-form-item label="培训班名" prop="className">
            <el-input
              v-model="queryParams.className"
              placeholder="请输入培训班名"
              clearable
              class="w-[200px]"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="计划月份" prop="planYearMonth">
            <el-date-picker
              v-model="queryParams.planYearMonth"
              type="month"
              placeholder="选择计划月份"
              clearable
              class="w-[200px]"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" type="success" @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="planList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="培训班名称" align="center" prop="className" />
          <el-table-column
            label="培训对象"
            align="center"
            prop="traineeObject"
          />
          <el-table-column
            label="培训目的"
            align="center"
            prop="traineePurpose"
          />
          <el-table-column label="计划月份" align="center" prop="planYearMonth">
            <template #default="scope">
              {{ parseTime(scope.row.planYearMonth, "{y}-{m}") }}
            </template>
          </el-table-column>
          <el-table-column
            label="培训时长(次)"
            align="center"
            prop="trainingDuration"
          />
          <el-table-column
            label="培训人数"
            align="center"
            prop="trainingNumber"
          />
          <el-table-column label="主办单位" align="center" prop="organizer" />
          <el-table-column
            label="培训地点"
            align="center"
            prop="trainingPlace"
          />
          <el-table-column label="培训分类" align="center" prop="trainingType">
            <template #default="scope">
              <dict-tag
                :options="training_type"
                :value="scope.row.trainingType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="关联培训名称"
            align="center"
            prop="selfName"
          />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleStatusChange(scope.row)">{{
                scope.row.status === "0" ? "禁用" : "启用"
              }}</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑培训计划对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="planForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="培训班名称" prop="className">
          <el-input v-model="form.className" placeholder="请输入培训班名称" />
        </el-form-item>
        <el-form-item label="培训对象" prop="traineeObject">
          <el-input v-model="form.traineeObject" placeholder="请输入培训对象" />
        </el-form-item>
        <el-form-item label="培训目的" prop="traineePurpose">
          <el-input
            v-model="form.traineePurpose"
            type="textarea"
            placeholder="请输入培训目的"
          />
        </el-form-item>
        <el-form-item label="计划月份" prop="planYearMonth">
          <el-date-picker
            v-model="form.planYearMonth"
            type="month"
            placeholder="选择计划月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="培训时长(次)" prop="trainingDuration">
          <el-input
            v-model="form.trainingDuration"
            placeholder="请输入培训时长"
          />
        </el-form-item>
        <el-form-item label="培训人数" prop="trainingNumber">
          <el-input-number
            v-model="form.trainingNumber"
            :min="0"
            placeholder="请输入培训人数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="主办单位" prop="organizer">
          <el-input v-model="form.organizer" placeholder="请输入主办单位" />
        </el-form-item>
        <el-form-item label="培训地点" prop="trainingPlace">
          <el-input v-model="form.trainingPlace" placeholder="请输入培训地点" />
        </el-form-item>
        <el-form-item label="培训分类" prop="trainingType">
          <el-input v-model="form.trainingType" placeholder="请输入培训分类" />
        </el-form-item>
        <el-form-item label="关联培训项目" prop="projectId">
          <RemoteSelect
            class="w-[200px]"
            v-model="form.projectId"
            v-modelName="form.projectName"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            responsePath="data"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择培训项目"
          ></RemoteSelect>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  listPlan,
  getPlan,
  addPlan,
  updatePlan,
  delPlan,
} from "@/api/training/plan";
import { parseTime } from "@/utils/welink";
const route = useRoute();
const router = useRouter();

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_normal_disable, training_type } = proxy.useDict(
  "sys_normal_disable",
  "training_type"
);
const planStatusOptions = [
  { label: "未上传", value: "HAVEN_T_UPLOADED", elTagType: "danger" },
  { label: "已上传", value: "HAVE_ALREADY_UPLOADED" },
];
// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 培训计划表格数据
const planList = ref([]);
// 培训项目选项
const projectOptions = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  className: undefined,
  planYearMonth: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  className: undefined,
  traineeObject: undefined,
  traineePurpose: undefined,
  planYearMonth: undefined,
  trainingDuration: undefined,
  trainingNumber: 0,
  organizer: undefined,
  trainingPlace: undefined,
  trainingType: undefined,
  projectId: undefined,
  projectName: undefined,
  status: "0",
});

// 表单校验
const rules = ref({
  className: [
    { required: true, message: "培训班名称不能为空", trigger: "blur" },
  ],
  traineeObject: [
    { required: true, message: "培训对象不能为空", trigger: "blur" },
  ],
  traineePurpose: [
    { required: true, message: "培训目的不能为空", trigger: "blur" },
  ],
  planYearMonth: [
    { required: true, message: "计划月份不能为空", trigger: "blur" },
  ],
  trainingDuration: [
    { required: true, message: "培训时长不能为空", trigger: "blur" },
  ],
  trainingNumber: [
    { required: true, message: "培训人数不能为空", trigger: "blur" },
  ],
  organizer: [{ required: true, message: "主办单位不能为空", trigger: "blur" }],
  trainingPlace: [
    { required: true, message: "培训地点不能为空", trigger: "blur" },
  ],
  trainingType: [
    { required: true, message: "培训分类不能为空", trigger: "blur" },
  ],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
});
const projectRow = ref("");

/** 查询培训计划列表 */
function getList() {
  loading.value = true;
  listPlan(queryParams.value).then((response) => {
    planList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    className: undefined,
    traineeObject: undefined,
    traineePurpose: undefined,
    planYearMonth: undefined,
    trainingDuration: undefined,
    trainingNumber: 0,
    organizer: undefined,
    trainingPlace: undefined,
    trainingType: undefined,
    projectId: undefined,
    projectName: undefined,
    status: "0",
  };
  proxy.resetForm("planForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 返回按钮 */
function goBack() {
  router.replace("/training/trainingPlan");
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();

  open.value = true;
  title.value = "添加培训计划";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getPlan(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑培训计划";
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除培训计划名称为"' + row.className + '"的数据项？')
    .then(function () {
      return delPlan(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  // proxy.$modal.confirm("是否确认导出所有培训计划数据项？").then(() => {
  //   proxy.$modal.loading("正在导出数据，请稍候...");
  //   exportPlan(queryParams.value).then((response) => {
  //     proxy.download(response.msg);
  //     proxy.$modal.closeLoading();
  //   });
  // });
  proxy.download(
    "railway/wlTrainingPlan/warn/export",
    {
      ...queryParams.value,
    },
    `${projectRow.value.projectName}_培训计划_${new Date().getTime()}.xlsx`
  );
}

/** 修改状态操作 */
function handleStatusChange(row) {
  let text = row.status === "0" ? "禁用" : "启用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.className + '"培训计划吗?')
    .then(function () {
      return updatePlan({
        id: row.id,
        status: row.status === "0" ? "1" : "0",
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["planForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updatePlan(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addPlan(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

onMounted(() => {
  projectRow.value = route.query;
  queryParams.value.projectId = route.query?.projectId;
  getList();
});
</script>
<style scoped>
.font-bold {
  font-weight: 700;
}
.w-full {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
