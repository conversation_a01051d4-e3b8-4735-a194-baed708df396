<template>
  <el-card class="chart-card">
    <div slot="header" class="card-header">
      <span class="font-bold">离职统计</span>

      <div class="search-filters">
        <el-date-picker
          v-model="params.yearMonth"
          type="month"
          placeholder="选择年月"
          :clearable="false"
          style="width: 200px; margin-right: 10px"
          format="YYYY-MM"
          value-format="YYYY-MM"
          @change="handleSearch"
        />
        <RemoteSelect
          v-model="params.projectId"
          url="/system/dept/list"
          labelKey="deptName"
          valueKey="deptId"
          placeholder="选择单位"
          hasDefault
          :clearable="false"
          style="width: 200px"
          :extraParams="{ parentId: '0' }"
          @change="handleSearch"
        />
      </div>
    </div>
    <div class="chart-content">
      <!-- 数据卡片展示区 -->
      <div class="data-cards">
        <div
          v-for="(dept, index) in chartData.typeMapList"
          :key="dept.typeName || index"
          class="data-card"
        >
          <div class="card-title">{{ dept.typeName }}</div>
          <div class="card-values">
            <div class="value-item">
              <span class="label">今年</span>
              <span class="value current">{{ dept.nowNumber || 0 }}</span>
            </div>
            <div class="value-item">
              <span class="label">去年</span>
              <span class="value comparison">{{ dept.lastNumber || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表展示区 -->
      <div class="chart-container" ref="chartRef"></div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted, computed } from 'vue';
import * as echarts from 'echarts';
import RemoteSelect from '@/components/RemoteSelect';

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['more', 'search']);

const chartRef = ref(null);
let chartInstance = null;
let resizeObserver = null;

// 计算图表数据
const chartSeriesData = computed(() => {
  const departments = props.chartData.departments || [];
  return {
    categories: departments.map((dept) => dept.deptName),
    actualValues: departments.map((dept) => dept.lastNumber || 0),
    comparisonValues: departments.map((dept) => dept.nowNumber || 0),
  };
});

const initChart = async () => {
  await nextTick();

  if (!chartRef.value) {
    console.warn('Chart container not found');
    return;
  }

  if (chartInstance) {
    chartInstance.dispose();
  }

  const container = chartRef.value;
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    setTimeout(() => {
      initChart();
    }, 100);
    return;
  }

  chartInstance = echarts.init(container);
  updateChart();

  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  window.addEventListener('resize', handleResize);

  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      handleResize();
    });
    resizeObserver.observe(container);
  }
};

const updateChart = () => {
  if (!chartInstance) return;

  const seriesData = chartSeriesData.value;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        let result = params[0].name + '<br/>';
        params.forEach(function (item) {
          result += item.seriesName + ': ' + item.value + '<br/>';
        });
        return result;
      },
    },
    legend: {
      data: ['今年', '去年'],
      top: 20,
      textStyle: {
        fontSize: 12,
      },
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '12%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: seriesData.categories,
      axisLabel: {
        fontSize: 11,
        color: '#666',
        interval: 0,
        rotate: 45,
        formatter: function (value) {
          return value.length > 4 ? value.substring(0, 7) + '...' : value;
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}',
        fontSize: 12,
        color: '#666',
      },
      minInterval: 1,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '今年',
        type: 'bar',
        data: seriesData.actualValues,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4A90E2' },
            { offset: 1, color: '#357ABD' },
          ]),
        },
        barWidth: '35%',
        label: {
          show: true,
          position: 'top',
          fontSize: 10,
          color: '#666',
          formatter: '{c}',
        },
      },
      {
        name: '去年',
        type: 'bar',
        data: seriesData.comparisonValues,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#F5A623' },
            { offset: 1, color: '#E8951C' },
          ]),
        },
        barWidth: '35%',
        label: {
          show: true,
          position: 'top',
          fontSize: 10,
          color: '#666',
          formatter: '{c}',
        },
      },
    ],
  };

  chartInstance.setOption(option, true);

  setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 100);
};

const handleMore = () => {
  emit('more');
};

const handleSearch = () => {
  emit('search', props.params);
};

watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

onMounted(() => {
  setTimeout(() => {
    initChart();
  }, 200);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  window.removeEventListener('resize', () => {});
});
</script>

<style scoped>
.chart-card {
  height: 500px;
  width: 100%;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  color: #2c3e50;
}

.search-filters {
  display: flex;
  align-items: center;
}

.chart-content {
  display: flex;
  height: 420px;
}

.data-cards {
  width: 280px;
  padding: 20px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.data-card {
  background: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.data-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-values {
  display: flex;
  justify-content: space-between;
}

.value-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.value {
  display: block;
  font-size: 18px;
  font-weight: 700;
}

.value.current {
  color: #4a90e2;
}

.value.comparison {
  color: #f5a623;
}

.chart-container {
  flex: 1;
  height: 420px;
  min-height: 420px;
  position: relative;
  padding: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-content {
    flex-direction: column;
    height: auto;
  }

  .data-cards {
    width: 100%;
    flex-direction: row;
    padding: 16px;
    height: auto;
    overflow-x: auto;
  }

  .data-card {
    margin-bottom: 0;
    margin-right: 12px;
    flex: 0 0 160px;
    min-width: 160px;
  }

  .data-card:last-child {
    margin-right: 0;
  }

  .chart-container {
    height: 300px;
    min-height: 300px;
    padding: 0 5px;
  }

  .chart-card {
    height: auto;
  }
}
</style>
