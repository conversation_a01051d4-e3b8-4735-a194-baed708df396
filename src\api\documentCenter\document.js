import request from "@/utils/request";

// 查询文档列表
export function listDocument(query) {
  return request({
    url: "/railway/wlArchive/list",
    method: "get",
    params: query,
  });
}

// 查询文档详细
export function getDocument(documentId) {
  return request({
    url: "/railway/wlArchive/" + documentId,
    method: "get",
  });
}

// 新增文档
export function addDocument(data) {
  return request({
    url: "/railway/wlArchive",
    method: "post",
    data: data,
  });
}

// 编辑文档
export function updateDocument(data) {
  return request({
    url: "/railway/wlArchive",
    method: "put",
    data: data,
  });
}

// 删除文档
export function delDocument(documentId) {
  return request({
    url: "/railway/wlArchive/" + documentId,
    method: "delete",
  });
}

// 导出文档
export function exportDocument(query) {
  return request({
    url: "/railway/wlArchive/export",
    method: "get",
    params: query,
  });
}

// 下载文档
export function downloadDocument(fileName) {
  return request({
    url: "/railway/wlArchive/download",
    method: "get",
    params: { fileName },
    responseType: "blob",
  });
}
