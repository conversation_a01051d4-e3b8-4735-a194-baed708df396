<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="项目名称" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择项目"
              clearable
              @change="handleProjectChange"
            />
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.deptId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              placeholder="请选择部门"
              clearable
              :extraParams="{ parentId: 9999 }"
            />
          </el-form-item>
          <el-form-item label="预警级别" prop="warnLevel">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.warnLevel"
              url="/railway/wlEarlyWarning/list"
              labelKey="ruleName"
              valueKey="id"
              placeholder="请选择预警级别"
              clearable
              :extraParams="{ ruleType: 'OVER_MAN' }"
            />
          </el-form-item>
          <el-form-item label="预警时间" prop="yearMonth">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.yearMonth"
              type="date"
              placeholder="请选择预警时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="warningList">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          />
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column label="部门" align="center" prop="deptName" />
          <el-table-column label="岗位名称" align="center" prop="postName" />
          <el-table-column label="预警人数" align="center" prop="beyond" />
          <el-table-column
            label="实际人数"
            align="center"
            prop="peopleNumber"
          />
          <el-table-column
            label="超员人数"
            align="center"
            prop="beyondNumber"
          />
          <el-table-column label="预警级别" align="center" prop="warnLevel" />
          <el-table-column
            label="预警时间"
            align="center"
            prop="formYear"
            width="180"
          />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { listOverEmploymentWarning } from '@/api/personnelFiles/overEmploymentWarning';
import RemoteSelect from '@/components/RemoteSelect/index';
const { proxy } = getCurrentInstance();
import { parseTime } from '@/utils/welink';

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 超员预警表格数据
const warningList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  projectId: undefined,
  deptId: undefined,
  warningLevel: undefined,
  yearMonth: parseTime(new Date(), '{y}-{m}'),
});

/** 项目变更操作 */
function handleProjectChange(project) {
  if (project && project.deptId) {
    queryParams.value.deptId = undefined; // 重置机构ID
  } else {
    queryParams.value.projectId = undefined;
    queryParams.value.deptId = undefined;
  }
}

/** 查询超员预警列表 */
function getList() {
  loading.value = true;
  listOverEmploymentWarning(queryParams.value).then((res) => {
    warningList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  queryParams.value.formYear = undefined;

  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlProjectOrgStaff/staffWarnExport',
    {
      ...queryParams.value,
    },
    `超员预警导出_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
