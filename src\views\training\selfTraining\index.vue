<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @downloadTemplate="handleDownloadTemplate"
    />

    <div class="main-box-card">
      <el-card class="box-card">
        <el-tabs v-model="activeTab" @tab-change="tabChange">
          <el-tab-pane label="我发起的自培" name="myTraining"></el-tab-pane>
          <el-tab-pane label="审核中的自培" name="reviewing"></el-tab-pane>
          <el-tab-pane label="全部自培" name="allTraining"></el-tab-pane>
        </el-tabs>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="培训单位" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择培训单位"
            ></RemoteSelect>
          </el-form-item>

          <el-form-item label="培训班名" prop="className">
            <el-input
              class="w-[200px]"
              v-model="queryParams.className"
              placeholder="请输入培训班名"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训开始时间" prop="beginTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.beginTime"
              type="date"
              placeholder="选择开始时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训结束时间" prop="endTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.endTime"
              type="date"
              placeholder="选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训形式" prop="trainingMode">
            <el-select
              class="w-[200px]"
              v-model="queryParams.trainingMode"
              placeholder="请选择培训形式"
              clearable
            >
              <el-option
                v-for="dict in training_form"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="培训类型" prop="trainingType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.trainingType"
              placeholder="请选择培训类型"
              clearable
            >
              <el-option
                v-for="dict in training_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item> -->

          <el-form-item
            v-show="activeTab !== 'reviewing'"
            label="状态"
            prop="status"
          >
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option
                v-for="dict in training_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5" v-if="activeTab !== 'reviewing'">
            <el-button class="custom-btn" type="primary" @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5" v-if="activeTab !== 'reviewing'">
            <el-button class="custom-btn" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
        <div class="cardItem">
          <div>入学人数:{{ totalEnrollmentCount }}</div>
          <div>管理人员:{{ totalManagerCount }}</div>
          <div>技术人员:{{ totalProfessionalCount }}</div>
          <div>工人:{{ totalSkillCount }}</div>
          <div>劳务人员:{{ totalOtherCount }}</div>
          <div>结业人数:{{ totalGraduaCount }}</div>
          <div>学习课时:{{ totalHours }}</div>
        </div>

        <el-table v-loading="loading" :data="selfTrainingList">
          <!-- <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column> -->
          <el-table-column
            label="单位"
            width="250"
            align="center"
            prop="deptName"
          />
          <el-table-column label="班名" align="center" prop="className" />

          <el-table-column label="培训类别" align="center">
            <el-table-column
              label="岗前培训"
              align="center"
              prop="beforeHour"
            />
            <el-table-column
              label="岗位培训"
              align="center"
              prop="networkTraining"
            >
              <el-table-column
                label="安全培训"
                align="center"
                prop="secureHour"
              />
              <el-table-column label="技术培训" align="center" prop="artHour" />
            </el-table-column>

            <el-table-column label="其他培训" align="center" prop="otherHour" />
          </el-table-column>

          <el-table-column label="培训形式" align="center" prop="trainingMode">
            <template #default="scope">
              <dict-tag
                :options="training_form"
                :value="scope.row.trainingMode"
              />
            </template>
          </el-table-column>
          <el-table-column label="培训起止时间" align="center">
            <el-table-column label="开始时间" align="center" prop="beginTime">
              <template #default="scope">
                {{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="endTime">
              <template #default="scope">
                {{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="入学人数" align="center" prop="">
            <template #default="scope">
              {{ scope.row.trainingStaffs?.length || 0 }}
            </template>
          </el-table-column>

          <el-table-column label="培训人员结构人数" align="center">
            <el-table-column label="管理人员" align="center" prop="manager">
              <template #default="scope">
                {{
                  scope.row.trainingStaffs?.filter((item) => item.type == 0)
                    .length || 0
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="技术人员"
              align="center"
              prop="professional"
            >
              <template #default="scope">
                {{
                  scope.row.trainingStaffs?.filter((item) => item.type == 1)
                    .length || 0
                }}
              </template>
            </el-table-column>
            <el-table-column label="工人" align="center" prop="skill">
              <template #default="scope">
                {{
                  scope.row.trainingStaffs?.filter((item) => item.type == 2)
                    .length || 0
                }}
              </template>
            </el-table-column>
            <el-table-column label="劳务人员" align="center" prop="other">
              <template #default="scope">
                {{
                  scope.row.trainingStaffs?.filter((item) => item.type == 3)
                    .length || 0
                }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="结业人数"
            align="center"
            prop="graduaNumber"
          />
          <el-table-column label="学习课时" align="center" prop="">
            <template #default="scope">
              {{
                scope.row.beforeHour +
                scope.row.secureHour +
                scope.row.artHour +
                scope.row.otherHour
              }}
            </template>
          </el-table-column>
          <el-table-column label="是否考试" align="center" prop="">
            <template #default="scope">
              {{ scope.row.assessWay.includes('0') ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="现场检查" align="center" prop="">
            <template #default="scope">
              {{ scope.row.assessWay.includes('1') ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="现场提问" align="center" prop="">
            <template #default="scope">
              {{ scope.row.assessWay.includes('2') ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="问卷调查" align="center" prop="">
            <template #default="scope">
              {{ scope.row.assessWay.includes('3') ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="其他方式" align="center" prop="">
            <template #default="scope">
              {{ scope.row.assessWay.includes('4') ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            label="附件"
            align="center"
            prop="wlAnnexes"
            width="100px"
          >
            <template #default="scope">
              <attachment-display :attachments="scope.row.wlAnnexes" />
            </template>
          </el-table-column>

          <el-table-column
            label="审核状态"
            align="center"
            prop="status"
            fixed="right"
          >
            <template #default="scope">
              <dict-tag :options="training_status" :value="scope.row.status" />
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="200"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="text"
                v-if="scope.row.status == '0'"
                @click="handleReport(scope.row)"
                >提报</el-button
              >
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                type="text"
                v-if="scope.row.status == '0'"
                @click="handleEdit(scope.row, 'edit')"
                >编辑</el-button
              >
              <el-button
                type="text"
                v-if="scope.row.status == '0'"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或修改自培训对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="培训单位" prop="projectId">
          <RemoteSelect
            v-model="form.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择培训单位"
          ></RemoteSelect>
        </el-form-item>
        <el-form-item label="培训班名" prop="className">
          <el-input v-model="form.className" placeholder="请输入培训班名" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="网络培训" prop="networkTraining">
              <el-input-number v-model="form.networkTraining" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安全培训" prop="safetyTraining">
              <el-input-number v-model="form.safetyTraining" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="技术培训" prop="technicalTraining">
              <el-input-number v-model="form.technicalTraining" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他培训" prop="otherTraining">
              <el-input-number v-model="form.otherTraining" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="培训形式" prop="trainingType">
          <el-select v-model="form.trainingType" placeholder="请选择培训形式">
            <el-option
              v-for="dict in training_form"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker
                v-model="form.beginTime"
                type="date"
                placeholder="选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="date"
                placeholder="选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="入学人数" prop="enrollmentCount">
          <el-input-number v-model="form.enrollmentCount" :min="0" />
        </el-form-item>
        <el-form-item label="管理人员" prop="manager">
          <el-input v-model="form.manager" placeholder="请输入管理人员" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted, watch } from 'vue';
import ImportExcel from '@/components/ImportExcel/index.vue';
import {
  listWlTrainingSelf,
  getWlTrainingSelf,
  addWlTrainingSelf,
  updateWlTrainingSelf,
  delWlTrainingSelf,
  subLaunchProcess,
  downloadWlTrainingSelf,
} from '@/api/training/selfTraining';
import { getUserProfile } from '@/api/system/user';
import { useRouter } from 'vue-router';
import AttachmentDisplay from '@/components/AttachmentDisplay/index.vue';
const router = useRouter();

const { proxy } = getCurrentInstance();
// 字典数据
const { training_form, training_type, training_status } = proxy.useDict(
  'training_form',
  'training_type',
  'training_status'
);
// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 自培训表格数据
const selfTrainingList = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 当前选中的标签页
const activeTab = ref('myTraining');
const userId = ref(null);

/** 计算总学习课时 */
const totalHours = computed(() => {
  if (!selfTrainingList.value || selfTrainingList.value.length === 0) return 0;

  let total = 0;
  selfTrainingList.value.forEach((item) => {
    // 汇总各类培训课时
    total += Number(item.beforeHour || 0); // 岗前培训
    total += Number(item.otherHour || 0); //
    total += Number(item.secureHour || 0); // 安全培训
    total += Number(item.artHour || 0); // 技术培训
  });

  return total;
});
// 入学人数
const totalManagerCount = ref(0); //管理人员
const totalProfessionalCount = ref(0); //技术人员
const totalSkillCount = ref(0); //工人
const totalOtherCount = ref(0); //劳务人员
const totalEnrollmentCount = computed(() => {
  if (!selfTrainingList.value || selfTrainingList.value.length === 0) return 0;

  let total = 0;
  totalManagerCount.value = 0;
  totalProfessionalCount.value = 0;
  totalSkillCount.value = 0;
  totalOtherCount.value = 0;
  selfTrainingList.value.forEach((item) => {
    total += Number(item.trainingStaffs?.length || 0);
    totalManagerCount.value +=
      item.trainingStaffs?.filter((item) => item.type == 0).length || 0;
    totalProfessionalCount.value +=
      item.trainingStaffs?.filter((item) => item.type == 1).length || 0;
    totalSkillCount.value +=
      item.trainingStaffs?.filter((item) => item.type == 2).length || 0;
    totalOtherCount.value +=
      item.trainingStaffs?.filter((item) => item.type == 3).length || 0;
  });

  return total;
});
const totalGraduaCount = computed(() => {
  return selfTrainingList.value.reduce(
    (acc, item) => acc + (item.graduaNumber || 0),
    0
  );
});

// 下载附件
function handleDownload(row) {
  downloadWlTrainingSelf(row.id)
    .then((response) => {
      const blob = new Blob([response], { type: 'application/zip' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = row.deptName + '自培附件';
      link.click();
      URL.revokeObjectURL(link.href);
    })
    .catch(() => {});
}

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  className: undefined,
  beginTime: undefined,
  endTime: undefined,
  trainingType: undefined,
  status: undefined,
  userId: undefined,
  trainingMode: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  trainingprojectName: undefined,
  className: undefined,
  networkTraining: 0,
  safetyTraining: 0,
  technicalTraining: 0,
  otherTraining: 0,
  trainingType: undefined,
  beginTime: undefined,
  endTime: undefined,
  enrollmentCount: 0,
  manager: undefined,
  status: '0',
  remark: undefined,
});

// 表单校验
const rules = ref({
  trainingprojectName: [
    { required: true, message: '培训单位不能为空', trigger: 'change' },
  ],
  className: [{ required: true, message: '培训班名不能为空', trigger: 'blur' }],
  trainingType: [
    { required: true, message: '培训形式不能为空', trigger: 'change' },
  ],
  beginTime: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
  enrollmentCount: [
    { required: true, message: '入学人数不能为空', trigger: 'blur' },
  ],
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入培训记录');
const importUrl = '/railway/wlTrainingSelf/importData';
const templateUrl = '/railway/wlTrainingSelf/importTemplate';

/** 查询自培训记录列表 */
function getList() {
  loading.value = true;
  listWlTrainingSelf(queryParams.value)
    .then((response) => {
      selfTrainingList.value = response.list.rows;
      total.value = response.list.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 监听标签页变化 */
function tabChange(newVal) {
  activeTab.value = newVal;
  proxy.resetForm('queryForm');
  queryParams.value.pageNum = 1;

  // 根据不同的标签页设置不同的查询参数
  if (newVal === 'myTraining') {
    queryParams.value.userId = userId.value;
    queryParams.value.status = undefined;
  } else if (newVal === 'reviewing') {
    queryParams.value.userId = userId.value;
    queryParams.value.status = '1'; // 审核中状态值为1
  } else if (newVal === 'allTraining') {
    queryParams.value.userId = undefined;
    queryParams.value.status = undefined;
  }
  // 查询数据
  getList();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');

  // 根据当前选中的标签页重新设置查询参数
  if (activeTab.value === 'myTraining') {
    queryParams.value.userId = userId.value;
  } else if (activeTab.value === 'reviewing') {
    queryParams.value.userId = userId.value;
    queryParams.value.status = '1';
  }
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  router.push({
    path: '/training/selfTraining/editor',
    query: {},
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // resetForm();
  // const id = row.id || row.id;
  // getWlTrainingSelf(id).then(response => {
  //   form.value = response.data;
  //   open.value = true;
  //   title.value = "修改自培管理记录";
  // });
  router.push({
    path: '/training/selfTraining/editor',
    query: {
      id: row.id,
    },
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['form'].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateWlTrainingSelf(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addWlTrainingSelf(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids;
  const projectName = row.projectName;
  proxy.$modal
    .confirm(`是否确认删除单位为"${projectName}"的自培?`)
    .then(function () {
      return delWlTrainingSelf(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true;
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlTrainingSelf/warn/export',
    {
      ...queryParams.value,
    },
    `自培管理记录_${new Date().getTime()}.xlsx`
  );
}
/** 提报按钮操作 */
function handleReport(row) {
  let value = {
    templateId: 10005,
    bizId: row.id,
  };
  const projectName = row.deptName;
  proxy.$modal
    .confirm(`是否确认提报单位为"${projectName}"的自培?`)
    .then(function () {
      return subLaunchProcess(value);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('提报成功');
    })
    .catch(() => {});
}
/** 审核按钮操作 */
function handleReview(row) {}

function handleView(row) {
  proxy.getProcessRouterPath(row, '10005', row.id);
}
/** 查看按钮操作 */
function handleEdit(row, type = 'edit') {
  router.push({
    path: '/training/selfTraining/editor',
    query: {
      id: row.id,
      type: type,
    },
    meta: { activeMenu: location.pathname },
  });
}

/** 下载模板操作 */
function handleDownloadTemplate() {
  proxy.download(
    'railway/wlTrainingSelf/importTemplate',
    {},
    `自培管理模板_${new Date().getTime()}.xlsx`
  );
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  getList();
}

/** 表单重置 */
function resetForm() {
  form.value = {
    id: undefined,
    trainingprojectName: undefined,
    className: undefined,
    networkTraining: 0,
    safetyTraining: 0,
    technicalTraining: 0,
    otherTraining: 0,
    trainingType: undefined,
    beginTime: undefined,
    endTime: undefined,
    enrollmentCount: 0,
    manager: undefined,
    status: '0',
    remark: undefined,
  };
  proxy.resetForm('form');
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  resetForm();
}

onMounted(async () => {
  const { data } = await getUserProfile(); // 获取当前登录用户信息
  userId.value = data.userId;
  tabChange(activeTab.value);
});
</script>

<style scoped lang="scss">
::v-deep(.el-tabs--border-card) {
  border-bottom: 0px;
  margin-bottom: 20px;
  .el-tabs__content {
    padding: 0px !important;
  }
}
.cardItem {
  display: flex;
  align-items: center;
  div {
    min-width: 100px;
    padding: 8px 18px;
    background: #e9f1ff;
    border-radius: 4px;
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: center;
    margin-bottom: 10px;
  }
}
</style>
