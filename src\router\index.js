import { createWebHistory, createRouter } from "vue-router";
/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "/index",
    children: [
      {
        path: "/index",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "工作台", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "编辑生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
  // 人员档案
  {
    path: "/personnelFiles/incumbentEmployee/addOrEdit",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlStaffInfo:list"],
    children: [
      {
        path: "",
        component: () =>
          import("@/views/personnelFiles/incumbentEmployee/addOrEdit/index"),
        name: "IncumbentEmployeeAddOrEdit",
        meta: { title: "", activeMenu: "/personnelFiles/incumbentEmployee" },
      },
    ],
  },
  // 项目人员申请
  {
    path: "/personnelFiles/projectStaffApply/addOrEdit",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlProjectApply:list"],
    children: [
      {
        path: "",
        component: () =>
          import("@/views/personnelFiles/projectStaffApply/addOrEdit"),
        name: "projectStaffApplyAddOrEdit",
        meta: { title: "", activeMenu: "/personnelFiles/projectStaffApply" },
      },
    ],
  },

  // 特种作业基础信息
  {
    path: "/certificate/certificateSpecial/details",
    component: Layout,
    name: "certificateSpecialDetails",
    permissions: ["railway:wlCertificateSpecial:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/certificate/special/details"),
        name: "certificateSpecialDetails",
        hidden: true,
        meta: { title: "1", activeMenu: "/certificate/certificateSpecial" },
      },
    ],
  },
  // 特种作业基础信息表单
  {
    path: "/certificate/certificateSpecial/form",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlCertificateSpecial:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/certificate/special/form"),
        name: "CertificateSpecialForm",
        meta: { title: "", activeMenu: "/certificate/certificateSpecial" },
      },
    ],
  },
  // 特种作业证书审核
  {
    path: "/certificate/specialAudit",
    component: Layout,
    hidden: false,
    permissions: ["railway:wlCertificateSpecial:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/certificate/audit/index"),
        name: "specialAudit",
        meta: {
          title: "特种作业证书审核",
          icon: "audit",
          activeMenu: "/certificate/specialAudit",
        },
      },
    ],
  },
  // 特种作业证书审核详情
  {
    path: "/certificate/specialAudit/detail",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlCertificateSpecial:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/certificate/audit/detail"),
        name: "specialAuditDetail",
        meta: {
          title: "证书审核详情",
          activeMenu: "/certificate/specialAudit",
        },
      },
    ],
  },
  // 注册/岗位证书基础信息表单
  {
    path: "/certificate/certificateEnroll/form",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlCertificateEnroll:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/certificate/enroll/form"),
        name: "CertificateEnrollForm",
        meta: { title: "", activeMenu: "/certificate/certificateEnroll" },
      },
    ],
  },
  // 培训计划
  {
    path: "/training/trainingPlan/details",
    component: Layout,
    hidden: false,
    permissions: ["railway:wlCertificateSpecial:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/training/plan/details"),
        name: "trainingPlan",
        meta: { title: "培训计划", activeMenu: "/training/trainingPlan" },
      },
    ],
  },
  // 自培管理详情
  {
    path: "/training/selfTraining/editor",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlTrainingSelf:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/training/selfTraining/editor"),
        name: "selfTrainingEditor",
        meta: { title: "操作自培管理", activeMenu: "/training/selfTraining" },
      },
    ],
  },
  // 委培管理详情界面
  {
    path: "/training/consign/details",
    component: Layout,
    hidden: false,
    permissions: ["railway:wlTrainingDepute:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/training/consign/details"),
        name: "consignDetails",
        meta: { title: "委培管理详情", activeMenu: "/training/consign" },
      },
    ],
  },

  // 流程审核
  {
    path: "/process/audit/details",
    component: Layout,
    hidden: false,
    permissions: ["process:audit:query"],
    children: [
      {
        path: "personnelAudit",
        component: () => import("@/views/process/audit/details/personnelAudit"),
        name: "personnelAudit",
        meta: {
          title: "人员修改流程审核",
          activeMenu: "/process/processAudit",
        },
      },
      {
        path: "personnelUpdateAudit",
        component: () =>
          import("@/views/process/audit/details/personnelUpdateAudit"),
        name: "personnelUpdateAudit",
        meta: {
          title: "人员修改流程审核",
          activeMenu: "/process/processAudit",
        },
      },
      {
        path: "selfTrainingAudit",
        component: () =>
          import("@/views/process/audit/details/selfTrainingAudit"),
        name: "selfTrainingDetail",
        meta: { title: "", activeMenu: "/process/processAudit" },
      },
      {
        path: "consignAudit",
        component: () => import("@/views/process/audit/details/consignAudit"),
        name: "consignAuditDetail",
        meta: { title: "", activeMenu: "/process/processAudit" },
      },
      {
        path: "dispatchArticleAudit",
        component: () =>
          import("@/views/process/audit/details/dispatchArticleAudit"),
        name: "dispatchArticleAudit",
        meta: {
          title: "公司发文抄录审核",
          activeMenu: "/process/processAudit",
        },
      },
      {
        path: "certificateAudit",
        component: () =>
          import("@/views/process/audit/details/certificateAudit"),
        name: "certificateAudit",
        meta: {
          title: "特种作业证书审核",
          activeMenu: "/process/processAudit",
        },
      },
      {
        path: "projectStaffApplyAudit",
        component: () =>
          import("@/views/process/audit/details/projectStaffApplyAudit"),
        name: "projectStaffApplyAudit",
        meta: {
          title: "项目人员申请审核",
          activeMenu: "/process/processAudit",
        },
      },
    ],
  },

  // 合同管理
  {
    path: "/contract/index/addOrEdit",
    component: Layout,
    hidden: false,
    permissions: ["railway:wlContract:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/contract/index/addOrEdit"),
        name: "ContractAddOrEdit",
        meta: { title: "合同管理", activeMenu: "/contract/contractIndex" },
      },
    ],
  },
  // 薪酬管理
  {
    path: "/salary/projectOrgSalary/details",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlProjectOrgSalary:list"],
    children: [
      {
        path: "",
        component: () => import("@/views/salary/projectOrgSalary/details"),
        name: "projectOrgSalaryDetails",
        meta: { title: "薪酬详情", activeMenu: "/salary/projectOrgSalary" },
      },
    ],
  },
  // 公司发文抄录新增
  {
    path: "/documentCenter/dispatchArticle/addOrEdit",
    component: Layout,
    hidden: true,
    permissions: ["railway:wlDispatchArticle:list"],
    children: [
      {
        path: "",
        component: () =>
          import("@/views/documentCenter/dispatchArticle/addOrEdit"),
        name: "dispatchArticleAddOrEdit",
        meta: {
          title: "公司发文抄录",
          activeMenu: "/documentCenter/dispatchArticle",
        },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 保存原始的 push 方法
const originalPush = router.push;
// 重写replace 方法
const originalPlace = router.replace;

// 重写 push 方法
router.push = function push(location) {
  if (location?.meta?.activeMenu)
    sessionStorage.setItem("activeMenu", location.meta.activeMenu);
  else sessionStorage.removeItem("activeMenu");
  return originalPush.call(this, location).catch((err) => {
    if (err.name !== "NavigationDuplicated") {
      throw err;
    }
  });
};
router.replace = function replace(location) {
  if (location?.meta?.activeMenu)
    sessionStorage.setItem("activeMenu", location.meta.activeMenu);
  else sessionStorage.removeItem("activeMenu");
  return originalPlace.call(this, location).catch((err) => {
    if (err.name !== "NavigationDuplicated") {
      throw err;
    }
  });
};
export default router;
