<template>
  <div>
    <!-- 选择按钮 -->
    <el-button @click="openDialog" :type="buttonType" :size="buttonSize">
      {{ buttonText }}
    </el-button>
    <span v-if="showSelected" class="text-sm text-gray-500 ml-2">
      <span v-if="multiple">已选择{{ modelValue.length }}人</span>
      <span v-else-if="modelValue && typeof modelValue === 'string'"
        >已选择
        <span v-if="modelName" style="color: #409eff">{{
          modelName
        }}</span></span
      >
    </span>

    <!-- 用户选择弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1100px"
      append-to-body
    >
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" ref="queryForm">
        <el-form-item label="培训班名" prop="className">
          <el-input
            v-model="queryParams.className"
            placeholder="请输入培训班名"
            clearable
            class="w-[200px]"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="计划月份" prop="planYearMonth">
          <el-date-picker
            v-model="queryParams.planYearMonth"
            type="month"
            placeholder="选择计划月份"
            clearable
            class="w-[200px]"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 用户表格 -->
      <el-table
        v-loading="loading"
        :data="options"
        @selection-change="handleSelectionChange"
        ref="employeeTable"
        row-key="id"
        reserve-selection
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          :selectable="isSelectable"
          v-if="multiple"
        />
        <el-table-column
          type="radio"
          width="50"
          align="center"
          v-else
          @change="handleRadioChange"
        >
          <template #default="scope">
            <el-radio
              v-model="selectedSingle"
              :label="scope.row.id"
              :disabled="disabledSelectData[scope.row.id]"
              @change="() => handleRadioChange(scope.row)"
            >
              &nbsp;
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="培训班名称" align="center" prop="className" />
        <el-table-column label="培训对象" align="center" prop="traineeObject" />
        <el-table-column
          label="培训目的"
          align="center"
          prop="traineePurpose"
        />
        <el-table-column label="计划月份" align="center" prop="planYearMonth" />
        <el-table-column
          label="培训时长(次)"
          align="center"
          prop="trainingDuration"
        />
        <el-table-column
          label="培训人数"
          align="center"
          prop="trainingNumber"
        />
        <el-table-column label="主办单位" align="center" prop="organizer" />
        <el-table-column label="培训地点" align="center" prop="trainingPlace" />
        <el-table-column label="培训分类" align="center" prop="trainingType">
          <template #default="scope">
            <dict-tag
              :options="training_type"
              :value="scope.row.trainingType"
            />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <template #footer>
        <div class="w-full flex justify-center mt-4">
          <el-button type="primary" @click="confirmSelection">确认</el-button>
          <el-button @click="dialogVisible = false" class="cancel-btn"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  onMounted,
  nextTick,
  getCurrentInstance,
} from "vue";
import { listPlan } from "@/api/training/plan";
import { useFormItem } from "element-plus";
const { formItem } = useFormItem();
const { proxy } = getCurrentInstance();

// 字典数据
const { training_type, sys_normal_disable } = proxy.useDict(
  "training_type",
  "sys_normal_disable"
);

const props = defineProps({
  // 选中的值
  modelValue: {
    type: [String, Number, Array],
    default: null,
  },
  // 培训名称
  modelName: {
    type: String,
    default: "",
  },
  // 是否显示已选择人数
  showSelected: {
    type: Boolean,
    default: true,
  },
  // 需要禁用选择的列
  disabledSelectColumns: {
    type: Array,
    default: [],
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: "关联培训计划",
  },
  // 按钮类型
  buttonType: {
    type: String,
    default: "primary",
  },
  // 按钮大小
  buttonSize: {
    type: String,
    default: "default",
  },
  // 弹窗标题
  dialogTitle: {
    type: String,
    default: "选择培训计划",
  },
});

const emit = defineEmits(["update:modelValue", "change"]);
// 禁用选择的数据
const disabledSelectData = ref({});

// 显示状态
const dialogVisible = ref(false);
const loading = ref(false);
const options = ref([]);
const total = ref(0);
const employeeTable = ref(null);

// 当前正在初始化
const isInit = ref(false);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  className: undefined,
  planYearMonth: undefined,
});

// 已选择数据
const selectedValues = ref([]);
const selectedOptions = ref([]);
const selectedSingle = ref(null);

// 缓存所有页已选用户，用于保持选中状态
const selectedMap = ref({});

// 打开弹窗
function openDialog() {
  dialogVisible.value = true;
  proxy.resetForm("queryForm");
  // 初始化已选数据
  initSelectedData();
  // 获取培训列表
  getList();
}

// 初始化已选数据
function initSelectedData() {
  if (props.multiple) {
    // 多选模式
    if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      selectedValues.value = [...props.modelValue];
      // 重置选择映射
      selectedMap.value = {};
      props.modelValue.forEach((id) => {
        selectedMap.value[id] = true;
      });
    } else {
      selectedValues.value = [];
      selectedMap.value = {};
    }
  } else {
    // 单选模式
    selectedSingle.value = props.modelValue;
    if (props.modelValue) {
      selectedValues.value = [props.modelValue];
    } else {
      selectedValues.value = [];
    }
  }
}

// 获取培训列表
function getList() {
  loading.value = true;
  isInit.value = true;
  listPlan({
    ...queryParams.value,
  }).then((res) => {
    options.value = res.rows;
    total.value = res.total;
    loading.value = false;
    // 在数据加载完成后，恢复选中状态
    nextTick(() => {
      setTableSelection();
    });
  });
}
// 是否可选择
const isSelectable = (row) => {
  return !disabledSelectData.value[row.id];
};
// 设置表格选中状态
function setTableSelection() {
  if (!employeeTable.value) return;
  // 先清除所有选择再重新设置，防止出现状态错误
  employeeTable.value.clearSelection();
  if (props.multiple) {
    // 多选模式：根据selectedMap恢复选中状态
    options.value.forEach((row) => {
      if (selectedMap.value[row.id]) {
        employeeTable.value.toggleRowSelection(row, true);
      }
    });
  } else {
    // 单选模式：根据selectedSingle设置选中
    if (selectedSingle.value) {
      selectedValues.value = [selectedSingle.value];
    }
  }
  isInit.value = false;
}

// 搜索按钮操作
function handleQuery() {
  if (isInit.value) return;
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

// 多选模式下的选择变更处理
function handleSelectionChange(selection) {
  if (isInit.value) return;
  if (props.multiple) {
    // 更新当前页的选中状态到Map
    options.value.forEach((row) => {
      const selected = selection.some((item) => item.id === row.id);
      if (selected) {
        selectedMap.value[row.id] = true;
      } else {
        selectedMap.value[row.id] = false;
      }
    });
    // 更新已选值列表
    selectedValues.value = Object.keys(selectedMap.value).filter(
      (key) => selectedMap.value[key]
    );
    selectedOptions.value = selection;
  }
}

// 单选模式的选择处理
function handleRadioChange(row) {
  if (disabledSelectData.value[row.id]) return;
  selectedSingle.value = row.id;
  selectedValues.value = [row.id];
  selectedOptions.value = [row];
}

// 行点击事件
function handleRowClick(row) {
  if (disabledSelectData.value[row.id]) return;
  if (props.multiple) {
    selectedMap.value[row.id] = !selectedMap.value[row.id];
    // 多选模式
    employeeTable.value.toggleRowSelection(row, selectedMap.value[row.id]);
  } else {
    handleRadioChange(row);
  }
}

// 确认选择
function confirmSelection() {
  if (props.multiple) {
    // 多选模式
    emit("update:modelValue", selectedValues.value);
    emit(
      "change",
      selectedValues.value.map((id) => {
        // 查找完整的培训信息
        const employee = options.value.find((item) => item.id === id);
        return employee || { id: id };
      })
    );
  } else {
    // 单选模式
    emit("update:modelValue", selectedSingle.value);
    // 查找培训完整信息
    const selectedEmployee = options.value.find(
      (item) => item.id === selectedSingle.value
    );
    emit("change", selectedEmployee || null);
  }
  formItem?.validate("change"); // 触发表单验证
  dialogVisible.value = false;
}

// 查看培训详情
function handleView(row) {
  // 这里可以实现查看培训详情的逻辑
  console.log("查看培训详情", row);
}
// 创建禁用选择的映射
function createDisabledSelectMap(arrs) {
  if (!Array.isArray(arrs)) {
    arrs = arrs.split(",");
  }
  // 创建映射前清空禁用选择的数据
  disabledSelectData.value = {};
  arrs.forEach((id) => {
    if (props.multiple) {
      // 需要排除自己已经选择的数据
      !selectedMap.value[id] && (disabledSelectData.value[id] = true);
    } else {
      selectedSingle.value != id && (disabledSelectData.value[id] = true);
    }
  });
}
// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    if (props.multiple) {
      if (Array.isArray(val)) {
        selectedValues.value = [...val];
        // 更新选择映射
        selectedMap.value = {};
        val.forEach((id) => {
          selectedMap.value[id] = true;
        });
      } else {
        selectedValues.value = [];
        selectedMap.value = {};
      }
    } else {
      selectedSingle.value = val;
      selectedValues.value = val ? [val] : [];
    }
    createDisabledSelectMap(props.disabledSelectColumns);
    // 如果弹窗可见，更新表格选中状态
    if (dialogVisible.value) {
      nextTick(() => {
        setTableSelection();
      });
    }
  },
  { immediate: true }
);
// 监听disabledSelectColumns变化
watch(
  () => props.disabledSelectColumns,
  (val) => {
    createDisabledSelectMap(val);
  }
);
// 组件挂载时初始化数据
onMounted(() => {
  initSelectedData();
  createDisabledSelectMap(props.disabledSelectColumns);
});
</script>

<style scoped>
div /deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #dfdfdf;
  border-color: #dfdfdf;
}
div /deep/ .el-radio__input.is-disabled .el-radio__inner {
  background-color: #dfdfdf;
  border-color: #dfdfdf;
}
</style>
