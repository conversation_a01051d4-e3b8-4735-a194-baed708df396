<template>
  <el-dialog :title="title" v-model="open" width="400px" append-to-body>
    <el-upload
      ref="uploadRef"
      :limit="1"
      :data="params"
      accept=".xlsx, .xls"
      :headers="headers"
      :action="url"
      :disabled="isUploading"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :auto-upload="false"
      drag
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            v-if="templateUrl"
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="handleDownloadTemplate"
            >下载模板</el-link
          >
        </div>
      </template>
    </el-upload>
    <template #footer>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="close" :disabled="isUploading"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click="submitFileForm"
          :disabled="!hasSelectedFile || isUploading"
          :loading="isUploading"
          >确 定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from "vue";
import { UploadFilled } from "@element-plus/icons-vue";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();
const props = defineProps({
  // 是否显示弹出层
  visible: {
    type: Boolean,
    default: false,
  },
  // 额外的请求参数
  extraParams: {
    type: Object,
    default: () => ({}),
  },
  // 弹出层标题
  title: {
    type: String,
    default: "导入数据",
  },
  // 是否显示更新已有数据选项
  showUpdateSupport: {
    type: Boolean,
    default: true,
  },
  // 导入URL
  importUrl: {
    type: String,
    required: true,
  },
  // 模板下载URL
  templateUrl: {
    type: String,
    default: "",
  },
});
const url = computed(() => {
  return import.meta.env.VITE_APP_BASE_API + props.importUrl;
});
const emit = defineEmits(["update:visible", "success", "close"]);

const params = ref(props.extraParams);

// 是否显示弹出层
const open = ref(false);

// 是否禁用上传
const isUploading = ref(false);

// 是否已选择文件
const hasSelectedFile = ref(false);

// 是否更新已经存在的数据
const updateSupport = ref(false);

// 上传组件ref
const uploadRef = ref(null);

// 设置上传的请求头部
const headers = ref({ Authorization: "Bearer " + getToken() });

watch(
  () => props.visible,
  (newVal) => {
    open.value = newVal;
    if (newVal === false) {
      // 关闭弹窗时重置组件状态
      isUploading.value = false;
      hasSelectedFile.value = false;
      updateSupport.value = false;
      if (uploadRef.value) {
        uploadRef.value.clearFiles();
      }
    }
  }
);

watch(
  () => open.value,
  (newVal) => {
    emit("update:visible", newVal);
  }
);
watch(
  () => props.extraParams,
  (newVal) => {
    params.value = newVal;
  }
);
/**
 * 关闭弹窗
 */
function close() {
  // 如果正在上传，不允许关闭
  if (isUploading.value) {
    return;
  }
  open.value = false;
  emit("close");
}

/**
 * 文件状态改变时的钩子
 */
function handleFileChange(file) {
  hasSelectedFile.value = file && file.status !== "fail";
}

/**
 * 文件被移除时的钩子
 */
function handleFileRemove() {
  hasSelectedFile.value = false;
}

/**
 * 文件上传中处理
 */
function handleFileUploadProgress() {
  isUploading.value = true;
}

/**
 * 文件上传成功处理
 */
function handleFileSuccess(response, file, fileList) {
  isUploading.value = false;
  hasSelectedFile.value = false;
  uploadRef.value.clearFiles();

  if (response.code === 200) {
    emit("success", response);
    close();
  }
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
}

/**
 * 下载模板操作
 */
function handleDownloadTemplate() {
  if (props.templateUrl) {
    proxy.download(
      props.templateUrl,
      {},
      `${props.title}_${new Date().getTime()}.xlsx`
    );
    return;
  }
  emit("downloadTemplate");
}

/**
 * 提交上传文件
 */
function submitFileForm() {
  uploadRef.value.submit();
}
</script>
