<template>
  <el-card class="chart-card">
    <div slot="header" class="card-header">
      <span>人员变动趋势</span>
      <div class="search-filters">
        <el-select
          v-model="params.staffType"
          placeholder="人员类型"
          clearable
          style="width: 200px; margin-right: 10px"
          @change="handleSearch"
        >
          <el-option
            v-for="dict in personnel_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

        <el-date-picker
          v-model="params.yearMonth"
          type="year"
          placeholder="选择年份"
          style="width: 200px"
          :clearable="false"
          format="YYYY"
          value-format="YYYY"
          @change="handleSearch"
        />
      </div>
    </div>
    <div class="chart-container" ref="chartRef"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from "vue";
import { getCurrentInstance } from "vue";
import * as echarts from "echarts";

const { proxy } = getCurrentInstance();
const { personnel_type } = proxy.useDict("personnel_type");

const props = defineProps({
  chartData: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["more", "search"]);

const chartRef = ref(null);
let chartInstance = null;
let resizeObserver = null;

// 搜索参数
const searchParams = ref({
  staffType: "",
  yearMonth: new Date().getFullYear() + "",
});

const initChart = async () => {
  await nextTick();

  if (!chartRef.value) {
    console.warn("Chart container not found");
    return;
  }

  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 确保容器有尺寸
  const container = chartRef.value;
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    setTimeout(() => {
      initChart();
    }, 100);
    return;
  }

  chartInstance = echarts.init(container);
  updateChart();

  // 添加resize监听
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  window.addEventListener("resize", handleResize);

  // 使用ResizeObserver监听容器尺寸变化
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      handleResize();
    });
    resizeObserver.observe(container);
  }
};

const updateChart = () => {
  if (!chartInstance) return;

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["入职人数"],
      top: 10,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: props.chartData.xAxisData || [],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "",
        type: "bar",
        data: props.chartData.entryData || [],
        itemStyle: {
          color: "#5B8FF9",
        },
      },
    ],
  };

  chartInstance.setOption(option, true);

  // 强制resize确保图表正确显示
  setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize();
    }
  }, 100);
};

const handleMore = () => {
  emit("more");
};

const handleSearch = () => {
  emit("search", props.params);
};

watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      updateChart();
    });
  },
  { deep: true }
);

onMounted(() => {
  // 延迟初始化确保DOM完全渲染
  setTimeout(() => {
    initChart();
  }, 200);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  window.removeEventListener("resize", () => {});
});
</script>

<style scoped>
.chart-card {
  height: 400px;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-filters {
  display: flex;
  align-items: center;
}

.chart-container {
  height: 320px;
  width: 100%;
  min-height: 320px;
  position: relative;
}
</style>
