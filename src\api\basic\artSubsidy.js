import request from "@/utils/request";

// 查询专业技术津贴列表
export function listArtSubsidy(query) {
  return request({
    url: "/railway/wlArtSubsidy/list",
    method: "get",
    params: query,
  });
}

// 查询专业技术津贴详细
export function getArtSubsidy(id) {
  return request({
    url: "/railway/wlArtSubsidy/" + id,
    method: "get",
  });
}

// 新增专业技术津贴
export function addArtSubsidy(data) {
  return request({
    url: "/railway/wlArtSubsidy",
    method: "post",
    data: data,
  });
}

// 编辑专业技术津贴
export function updateArtSubsidy(data) {
  return request({
    url: "/railway/wlArtSubsidy",
    method: "put",
    data: data,
  });
}

// 删除专业技术津贴
export function delArtSubsidy(id) {
  return request({
    url: "/railway/wlArtSubsidy/" + id,
    method: "delete",
  });
}
