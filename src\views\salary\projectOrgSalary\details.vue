<template>
  <div class="app-container">
    <!-- 基础信息 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>基础信息</span>
        </div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="时间">{{
          baseInfo.yearMonth
        }}</el-descriptions-item>
        <el-descriptions-item label="单位名称">{{
          baseInfo.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="部门名称">{{
          baseInfo.deptName
        }}</el-descriptions-item>
        <el-descriptions-item label="上传状态">
          <dict-tag :options="salary_upload_status" :value="baseInfo.status" />
        </el-descriptions-item>
        <el-descriptions-item label="发放金额">{{
          baseInfo.totalAmount || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="是否发放工资">{{
          baseInfo.isPaySalary === "1" ? "是" : "否"
        }}</el-descriptions-item>
        <el-descriptions-item label="工资支付书">
          <div v-if="baseInfo.annexList1 && baseInfo.annexList1.length > 0">
            <attachment-display :attachments="baseInfo.annexList1" />
          </div>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="工资凭证" width="200">
          <div v-if="baseInfo.annexList2 && baseInfo.annexList2.length > 0">
            <attachment-display :attachments="baseInfo.annexList2" />
          </div>
          <span v-else>无</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 薪酬表信息 -->
    <el-card class="box-card mt-4">
      <template #header>
        <div class="card-header">
          <span>薪酬表信息</span>
        </div>
      </template>
      <!-- 搜索表单 -->
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名"
            clearable
            class="w-[200px]"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <el-table v-loading="loading" :data="salaryList" class="mt-2" border>
        <el-table-column
          type="index"
          label="序号"
          width="100"
          align="center"
          fixed
        />

        <!-- 基本信息 -->
        <el-table-column label="基本信息" align="center">
          <el-table-column
            label="姓名"
            align="center"
            prop="name"
            width="100"
          />
          <el-table-column
            label="编号"
            align="center"
            prop="code"
            width="100"
          />
          <el-table-column
            label="部门"
            align="center"
            prop="department"
            width="120"
          />
          <el-table-column
            label="岗位"
            align="center"
            prop="position"
            width="120"
          />
          <el-table-column
            label="性别"
            align="center"
            prop="gender"
            width="80"
          />
          <el-table-column
            label="民族"
            align="center"
            prop="ethnicity"
            width="100"
          />
          <el-table-column
            label="身份证号"
            align="center"
            prop="idNumber"
            width="180"
          />
          <el-table-column
            label="银行账号"
            align="center"
            prop="bankAccount"
            width="180"
          />
          <el-table-column
            label="薪酬类别"
            align="center"
            prop="salaryCategory"
            width="120"
          />
        </el-table-column>

        <!-- 工资信息 -->
        <el-table-column label="工资信息" align="center">
          <el-table-column label="基本年薪" align="center">
            <el-table-column
              label="标准"
              align="center"
              prop="baseSalaryStandard"
              width="120"
            />
            <el-table-column
              label="实发"
              align="center"
              prop="baseSalary"
              width="120"
            />
          </el-table-column>
          <el-table-column label="岗位工资" align="center">
            <el-table-column
              label="标准"
              align="center"
              prop="positionSalaryStandard"
              width="120"
            />
            <el-table-column
              label="实发"
              align="center"
              prop="positionSalary"
              width="120"
            />
            <el-table-column
              label="系数"
              align="center"
              prop="positionCoefficient"
              width="100"
            />
          </el-table-column>
          <el-table-column label="绩效工资" align="center">
            <el-table-column
              label="标准"
              align="center"
              prop="performanceSalaryStandard"
              width="120"
            />
            <el-table-column
              label="实发"
              align="center"
              prop="performanceSalary"
              width="120"
            />
            <el-table-column
              label="系数"
              align="center"
              prop="performanceCoefficient"
              width="100"
            />
          </el-table-column>
        </el-table-column>

        <!-- 津贴信息 -->
        <el-table-column label="津贴信息" align="center">
          <el-table-column label="持证津贴" align="center">
            <el-table-column
              label="标准"
              align="center"
              prop="certificationAllowanceStandard"
              width="120"
            />
            <el-table-column
              label="实发"
              align="center"
              prop="certificationAllowance"
              width="120"
            />
          </el-table-column>
          <el-table-column label="技术津贴" align="center">
            <el-table-column
              label="标准"
              align="center"
              prop="technicalAllowanceStandard"
              width="120"
            />
            <el-table-column
              label="实发"
              align="center"
              prop="technicalAllowance"
              width="120"
            />
          </el-table-column>
          <el-table-column label="其他津贴" align="center">
            <el-table-column
              label="电脑补贴"
              align="center"
              prop="computerSubsidy"
              width="120"
            />
            <el-table-column
              label="话费补贴"
              align="center"
              prop="phoneSubsidy"
              width="120"
            />
            <el-table-column
              label="交通补贴"
              align="center"
              prop="transportationSubsidy"
              width="120"
            />
            <el-table-column
              label="其他津贴"
              align="center"
              prop="otherAllowances"
              width="120"
            />
          </el-table-column>
        </el-table-column>

        <!-- 考勤信息 -->
        <el-table-column label="考勤信息" align="center">
          <el-table-column
            label="出勤天数"
            align="center"
            prop="attendanceDays"
            width="100"
          />
          <el-table-column
            label="制度工天"
            align="center"
            prop="systemWorkingDays"
            width="100"
          />
          <el-table-column
            label="法定工天"
            align="center"
            prop="legalWorkingDays"
            width="100"
          />
          <el-table-column
            label="年休工天"
            align="center"
            prop="annualLeaveDays"
            width="100"
          />
          <el-table-column label="病假工天" align="center">
            <el-table-column
              label="长期"
              align="center"
              prop="longTermSickLeave"
              width="100"
            />
            <el-table-column
              label="零星"
              align="center"
              prop="shortTermSickLeave"
              width="100"
            />
          </el-table-column>
          <el-table-column label="其他假期" align="center">
            <el-table-column
              label="婚假"
              align="center"
              prop="marriageLeaveDays"
              width="100"
            />
            <el-table-column
              label="产假"
              align="center"
              prop="maternityLeaveDays"
              width="100"
            />
            <el-table-column
              label="探亲"
              align="center"
              prop="familyVisitDays"
              width="100"
            />
            <el-table-column
              label="丧假"
              align="center"
              prop="bereavementLeaveDays"
              width="100"
            />
          </el-table-column>
        </el-table-column>

        <!-- 保险福利 -->
        <el-table-column label="保险福利" align="center">
          <el-table-column
            label="养老保险"
            align="center"
            prop="pensionInsurance"
            width="120"
          />
          <el-table-column
            label="医疗保险"
            align="center"
            prop="medicalInsurance"
            width="120"
          />
          <el-table-column
            label="失业保险"
            align="center"
            prop="unemploymentInsurance"
            width="120"
          />
          <el-table-column
            label="住房公积金"
            align="center"
            prop="housingFund"
            width="120"
          />
          <el-table-column
            label="企业年金"
            align="center"
            prop="enterpriseAnnuity"
            width="120"
          />
        </el-table-column>

        <!-- 其他费用 -->
        <el-table-column label="其他费用" align="center">
          <el-table-column
            label="水电费"
            align="center"
            prop="utilities"
            width="120"
          />
          <el-table-column
            label="房租"
            align="center"
            prop="rent"
            width="120"
          />
          <el-table-column
            label="物管费"
            align="center"
            prop="propertyFee"
            width="120"
          />
          <el-table-column
            label="备用金"
            align="center"
            prop="pettyCash"
            width="120"
          />
        </el-table-column>
        <!-- 辅助性用工额外信息 -->
        <el-table-column
          v-if="activeTab === '1'"
          label="辅助性用工信息"
          align="center"
        >
          <el-table-column
            label="员工类型"
            align="center"
            prop="staffType"
            width="120"
          />
          <el-table-column
            label="日工资标准"
            align="center"
            prop="dayWageScale"
            width="120"
          />
          <el-table-column
            label="计时工资"
            align="center"
            prop="hourlyWages"
            width="120"
          />
          <el-table-column
            label="生产奖励"
            align="center"
            prop="prodReward"
            width="120"
          />
          <el-table-column
            label="包干工资"
            align="center"
            prop="lumpSalary"
            width="120"
          />
          <el-table-column
            label="合并扣税"
            align="center"
            prop="mergeBuckleTax"
            width="120"
          />
        </el-table-column>
        <!-- 工资汇总 -->
        <el-table-column label="工资汇总" align="center" fixed="right">
          <el-table-column
            label="应发工资"
            align="center"
            prop="grossPay"
            width="120"
          />
          <el-table-column
            label="实发工资"
            align="center"
            prop="netPay"
            width="120"
          />
          <el-table-column
            label="个人所得税"
            align="center"
            prop="inputTax"
            width="120"
          />
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRoute } from "vue-router";
import { listSalaryDetails } from "@/api/salary/projectOrgSalary";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
const { proxy } = getCurrentInstance();
const route = useRoute();

// 字典数据
const { salary_upload_status } = proxy.useDict("salary_upload_status");

// 遮罩层
const loading = ref(false);
// 总条数
const total = ref(0);
// 薪酬表格数据
const salaryList = ref([]);
// 显示搜索条件
const showSearch = ref(true);
// 基础信息
const baseInfo = ref({});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  yearMonth: undefined,
  name: undefined,
  salaryId: route.query.id,
});
// 当前选中的tab
const activeTab = ref(localStorage.getItem("salaryType") || "0");
/** 查询基础信息 */
function getBaseInfo() {
  baseInfo.value = JSON.parse(localStorage.getItem("salaryBaseInfo") || "{}");
}

/** 查询薪酬详情列表 */
function getList() {
  loading.value = true;
  listSalaryDetails(queryParams.value).then((response) => {
    salaryList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

onMounted(() => {
  getBaseInfo();
  getList();
});
</script>

<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style>
