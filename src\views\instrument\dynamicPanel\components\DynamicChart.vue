<template>
  <div class="dynamic-chart-container">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form
        :inline="true"
        :model="queryParams"
        ref="queryForm"
        class="search-form"
      >
        <el-form-item label="建设状态" prop="constructionStatus">
          <el-select
            v-model="queryParams.constructionStatus"
            placeholder="请选择建设状态"
            clearable
            class="w-[200px]"
          >
            <el-option
              v-for="dict in construction_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="机构属性" prop="orgAttribute">
          <el-select
            v-model="queryParams.orgAttribute"
            placeholder="请选择机构属性"
            clearable
            class="w-[200px]"
          >
            <el-option
              v-for="dict in organization_attribute"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="人员状态" prop="positionStatus">
          <el-select
            v-model="queryParams.positionStatus"
            placeholder="请选择人员状态"
            clearable
            class="w-[200px]"
          >
            <el-option
              v-for="dict in position_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <i class="el-icon-search"></i>
            查询
          </el-button>
          <el-button @click="resetQuery">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图表区域 -->
    <div class="chart-grid">
      <!-- 人员变动趋势 - 占整行 -->
      <div class="chart-row">
        <PersonnelChangeChart
          :chartData="chartData.personnelChange"
          :params="personnelChangeParams"
          @search="getPersonnelChange"
        />
      </div>

      <!-- 离职率统计 - 占整行 -->
      <div class="chart-row">
        <TurnoverRateChart
          :chartData="chartData.turnoverRate"
          :params="turnoverRateParams"
          @search="getTurnoverRate"
        />
      </div>

      <!-- 入职机构排名 - 占整行 -->
      <div class="chart-row">
        <OrganizationRankingChart
          :chartData="chartData.organizationRanking"
          :params="organizationRankingParams"
          @search="getOrganizationRanking"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import PersonnelChangeChart from "./PersonnelChangeChart.vue";
import TurnoverRateChart from "./TurnoverRateChart.vue";
import OrganizationRankingChart from "./OrganizationRankingChart.vue";
import {
  getTrend,
  getTurnover,
  getOrgRankingList,
} from "@/api/instrument/dynamicPanel";
const { proxy } = getCurrentInstance();

// 字典数据
const { construction_status, organization_attribute, position_status } =
  proxy.useDict(
    "construction_status",
    "organization_attribute",
    "position_status"
  );

// 查询参数
const queryParams = ref({
  constructionStatus: "",
  orgAttribute: "",
  positionStatus: "",
});
// 人员变动趋势参数
const personnelChangeParams = ref({
  yearMonth: new Date().getFullYear() + "",
  staffType: "",
});

// 离统计参数
const turnoverRateParams = ref({
  yearMonth: proxy.parseTime(new Date(), "{y}-{m}"),
  projectId: "",
});

// 入职机构排名参数
const organizationRankingParams = ref({
  yearMonth: proxy.parseTime(new Date(), "{y}-{m}"),
  projectId: "",
});

// 图表数据
const chartData = ref({
  personnelChange: {
    xAxisData: [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月",
    ],
    entryData: [0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0],
    leaveData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
  },
  turnoverRate: {
    departments: [],
    typeMapList: [],
  },
  organizationRanking: {
    organizations: [],
    data: [],
  },
});

// 查询方法
const handleQuery = () => {
  fetchChartData();
  getTurnoverRate();
  getOrganizationRanking();
};

// 重置查询
const resetQuery = () => {
  queryParams.value = {
    constructionStatus: "",
    orgAttribute: "",
    positionStatus: "",
  };
  fetchChartData();
  getTurnoverRate();
  getOrganizationRanking();
};
// 获取人员变动趋势
function getPersonnelChange() {
  const params = {
    ...personnelChangeParams.value,
    ...queryParams.value,
  };
  getTrend(params).then((res) => {
    chartData.value.personnelChange.entryData = res.data;
  });
}

// 获取离职统计
function getTurnoverRate() {
  const params = {
    ...turnoverRateParams.value,
    ...queryParams.value,
  };
  getTurnover(params).then((res) => {
    chartData.value.turnoverRate.departments = res?.data?.deptMapList || [];
    chartData.value.turnoverRate.typeMapList = res?.data?.typeMapList || [];
  });
}

// 获取入职机构排名
function getOrganizationRanking() {
  const params = {
    ...organizationRankingParams.value,
    ...queryParams.value,
  };
  getOrgRankingList(params).then((res) => {
    console.log(res?.data);
    const names = [];
    const data = [];
    res?.data?.forEach((item) => {
      names.unshift(item.deptName);
      data.unshift(item.staffNumber);
    });
    chartData.value.organizationRanking.organizations = names;

    chartData.value.organizationRanking.data = data;
  });
}

// 获取图表数据
const fetchChartData = () => {
  getPersonnelChange(); // 人员变动趋势
};

// 图表更多操作
const handlePersonnelChangeMore = () => {};

const handleTurnoverRateMore = () => {};

const handleOrganizationRankingMore = () => {};

onMounted(() => {
  fetchChartData();
});
</script>

<style scoped>
.dynamic-chart-container {
  padding: 0;
  background: #f8f9fa;
}

.page-header {
  padding: 24px 24px 0;
  background: #ffffff;
  margin-bottom: 0;
}

.page-title {
  margin: 0 0 8px 0;
  color: #212529;
  font-size: 20px;
  font-weight: 600;
}

.page-desc {
  margin: 0 0 20px 0;
  color: #6c757d;
  font-size: 14px;
}

.search-card {
  margin: 0;
  border: unset;
  box-shadow: unset;
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
  padding: 8px 0;
}

.chart-grid {
  gap: 20px;
  display: flex;
  flex-direction: column;
}

.chart-row {
  width: 100%;
}

.chart-row:last-child {
  margin-bottom: 0;
}

.w-\[200px\] {
  width: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form .el-form-item {
    margin-bottom: 16px;
  }

  .w-\[200px\] {
    width: 100%;
  }

  .chart-grid {
    padding: 16px;
  }

  .page-header {
    padding: 16px 16px 0;
  }
}
</style>
