<template>
  <div class="notice-card" style="width: 100%">
    <div class="flex justify-between items-center">
      <span class="section-title">{{ title }}</span>
      <div class="view-more" @click="$emit('more')">
        <span>查看更多</span>
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
    </div>
    <div class="notice-box" v-if="todoList && todoList.length > 0">
      <div
        class="p-4 bg-[#F5F6FB] flex flex-col rounded-[10px]"
        @click="$emit('click', todoList[0])"
      >
        <div class="flex items-center">
          <img src="@/assets/images/index/12.png" class="w-[50px] h-[50px]" />
          <span class="font-bold">{{ todoList[0]?.instanceName }}</span>
        </div>
        <div class="warning-border mb-2"></div>
        <div class="flex justify-between items-center">
          <div class="flex">
            <img src="@/assets/images/index/13.png" class="w-[16px] h-[16px]" />
            <span class="text-[#999] text-sm ml-2">
              {{ todoList[0]?.updateTime }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="text-[#2674FE] text-sm">立即查看</span>
            <img src="@/assets/images/index/5.png" class="card-arrow ml-2" />
          </div>
        </div>
      </div>

      <div class="flex px-6 flex-col">
        <div
          class="warning-border flex p-2 flex-col todo-card"
          v-for="item in todoList.slice(1, 10)"
          :key="item.id"
          @click="$emit('click', item)"
        >
          <span
            class="font-bold w-full text-ellipsis overflow-hidden whitespace-nowrap"
          >
            {{ item.instanceName }}
          </span>
          <span class="text-[#999999] text-sm mt-2">{{ item.updateTime }}</span>
        </div>
      </div>
    </div>
    <div class="notice-box h-full justify-center" v-else>
      <Empty />
    </div>
  </div>
</template>

<script setup>
import { ArrowRight } from "@element-plus/icons-vue";
import Empty from "@/components/Empty/index.vue";

defineEmits(["click", "more"]);

defineProps({
  title: {
    type: String,
    default: "最新待办事项",
  },
  todoList: {
    type: Array,
    default: () => [],
  },
});
</script>

<style lang="scss" scoped>
.notice-card {
  padding: 16px;
  height: 455px;
  overflow: auto;
  background-color: white;
}

.notice-box {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  max-height: calc(100% - 60px);
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.view-more {
  display: flex;
  align-items: center;
  color: #2674fe;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #ebf7fd;
  }

  i {
    margin-left: 4px;
    font-size: 12px;
  }
}

.warning-border {
  border-bottom: #d1d1d1 1px dotted;
}

.todo-card::before {
  width: 8px;
  height: 8px;
  content: "";
  background: #0570c0;
  border-radius: 50%;
  position: absolute;
  left: -10px;
  top: 15px;
}

.card-arrow {
  width: 16px;
  height: 6px;
}
</style>
