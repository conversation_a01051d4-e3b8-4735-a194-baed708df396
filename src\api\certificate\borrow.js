import request from '@/utils/request';

/**
 * 查询证书借阅列表
 * @param {Object} query 查询参数
 * @returns {Promise}
 */
export function listCertificateBorrow(query) {
  return request({
    url: '/railway/wlCertificateBorrow/list',
    method: 'get',
    params: query,
  });
}

/**
 * 查询证书借阅详细
 * @param {number|string} id 借阅ID
 * @returns {Promise}
 */
export function getCertificateBorrow(id) {
  return request({
    url: '/railway/wlCertificateBorrow/' + id,
    method: 'get',
  });
}

/**
 * 新增证书借阅
 * @param {Object} data 借阅数据
 * @returns {Promise}
 */
export function addCertificateBorrow(data) {
  return request({
    url: '/railway/wlCertificateBorrow',
    method: 'post',
    data: data,
  });
}

/**
 * 修改证书借阅
 * @param {Object} data 借阅数据
 * @returns {Promise}
 */
export function updateCertificateBorrow(data) {
  return request({
    url: '/railway/wlCertificateBorrow',
    method: 'put',
    data: data,
  });
}

/**
 * 删除证书借阅
 * @param {number|string} id 借阅ID
 * @returns {Promise}
 */
export function delCertificateBorrow(id) {
  return request({
    url: '/railway/wlCertificateBorrow/' + id,
    method: 'delete',
  });
}

/**
 * 提交证书借阅申请
 * @param {Object} data 借阅数据
 * @returns {Promise}
 */
export function submitCertificateBorrow(data) {
  return request({
    url: '/railway/wlCertificateBorrow/submit',
    method: 'post',
    data: data,
  });
}

/**
 * 审核证书借阅
 * @param {Object} data 审核数据
 * @returns {Promise}
 */
export function approveCertificateBorrow(data) {
  return request({
    url: '/railway/wlCertificateBorrow/approve',
    method: 'post',
    data: data,
  });
}

/**
 * 归还证书
 * @param {Object} data 归还数据
 * @returns {Promise}
 */
export function returnCertificateBorrow(data) {
  return request({
    url: '/railway/wlCertificateBorrow/return',
    method: 'post',
    data: data,
  });
}

/**
 * 上传附件
 * @param {Object} data 附件数据
 * @returns {Promise}
 */
export function uploadAttachment(data) {
  return request({
    url: '/railway/wlCertificateBorrow/upload',
    method: 'post',
    data: data,
  });
}

/**
 * 下载附件
 * @param {number|string} fileId 文件ID
 * @returns {Promise}
 */
export function downloadAttachment(fileId) {
  return request({
    url: '/railway/wlCertificateBorrow/download/' + fileId,
    method: 'get',
    responseType: 'blob',
  });
}
