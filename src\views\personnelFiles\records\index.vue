<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="人员名称" prop="name">
            <el-input
              class="w-[200px]"
              v-model="queryParams.name"
              placeholder="请输入人员名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="单位名称" prop="projectName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.projectName"
              placeholder="请输入单位名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="岗位名称" prop="postName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.postName"
              placeholder="请输入岗位名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="记录类别" prop="type">
            <el-select
              class="w-[200px]"
              v-model="queryParams.type"
              placeholder="请选择记录类别"
              clearable
            >
              <el-option
                v-for="dict in recordTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="描述类型" prop="description">
            <el-select
              class="w-[200px]"
              v-model="queryParams.description"
              placeholder="请选择描述类型"
              clearable
            >
              <el-option
                v-for="dict in [...description_type_options, ...entry_source]"
                :key="dict.label"
                :label="dict.label"
                :value="dict.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态" prop="status">
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择审核状态"
              clearable
            >
              <el-option
                v-for="dict in audit_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              class="searchButton"
              icon="Search"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              icon="RefreshLeft"
              class="reset-button"
              @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>

          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="wlStaffChangesList"
          :default-sort="{ prop: 'date', order: 'descending' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="所在单位" align="center" prop="projectName" />
          <el-table-column label="岗位" align="center" prop="postName" />
          <el-table-column label="记录类别" align="center" prop="type">
            <template #default="scope">
              <span>{{ getDictLabel(recordTypeOptions, scope.row.type) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="描述类型" align="center" prop="description">
          </el-table-column>

          <el-table-column
            label="更新时间"
            align="center"
            prop="auditTime"
            width="180"
          >
            <template #default="scope">
              <span>{{
                scope.row.auditTime
                  ? parseTime(scope.row.auditTime, "{y}-{m}-{d}")
                  : "--"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="实际减员时间"
            align="center"
            prop="decreTime"
            width="180"
          >
            <template #default="scope">
              <span>{{
                scope.row.decreTime
                  ? parseTime(scope.row.decreTime, "{y}-{m}-{d}")
                  : "--"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="audit_status" :value="scope.row.status" />
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="180"
            :show-overflow-tooltip="false"
          >
            <template #default="scope">
              <div class="flex justify-center">
                <el-button type="text" @click="handleView(scope.row)"
                  >查看</el-button
                >
                <!-- 编辑删除按钮 当类型为减员且状态为拟稿时显示 -->
                <el-button
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  v-if="scope.row.type === 'D' && scope.row.status === '0'"
                  >编辑</el-button
                >
                <el-button
                  link
                  type="danger"
                  @click="handleDelete(scope.row)"
                  v-if="scope.row.type === 'D' && scope.row.status === '0'"
                  >删除</el-button
                >
                <!-- 提审按钮 当状态为拟稿时显示 -->
                <el-button
                  link
                  type="primary"
                  @click="handleSubmit(scope.row)"
                  v-if="scope.row.status === '0'"
                  >提审</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或编辑人员变动记录对话框 -->
        <el-dialog
          class="rounded-lg"
          :title="title"
          v-model="open"
          width="500px"
          append-to-body
          @close="open = false"
          align-center
        >
          <el-form
            ref="wlStaffChangesRef"
            :model="form"
            :rules="rules"
            label-width="110px"
          >
            <el-form-item label="描述类型" prop="description">
              <el-select
                v-model="form.description"
                placeholder="请选择描述类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in description_type_options"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="实际减员时间" prop="decreTime">
              <el-date-picker
                clearable
                style="width: 100%"
                v-model="form.decreTime"
                type="date"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                placeholder="请选择实际减员时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="减员人员" prop="staffIds" v-if="!form.id">
              <user-select
                v-model="form.staffIds"
                :placeholder="'请选择人员'"
                multiple
              />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button class="cancel-btn" @click="cancel">取 消</el-button>
              <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
          </template>
        </el-dialog>
      </el-card>
    </div>
  </div>
</template>

<script setup name="WlStaffChanges">
import {
  getWlStaffChanges,
  listWlStaffChanges,
  addWlStaffChanges,
  updateWlStaffChanges,
  delWlStaffChanges,
  submitWlStaffChanges,
} from "@/api/personnelFiles/records";

const { proxy } = getCurrentInstance();
const { sys_user_sex, description_type_options, audit_status, entry_source } =
  proxy.useDict(
    "sys_user_sex",
    "description_type_options",
    "audit_status",
    "entry_source"
  );

// 定义记录类别字典
const recordTypeOptions = [
  { value: "A", label: "增员" },
  { value: "D", label: "减员" },
];

// 获取字典标签方法
function getDictLabel(dict, value) {
  const item = dict.find((d) => d.value === value);
  return item ? item.label : "";
}

const wlStaffChangesList = ref([]);
const open = ref(false);
const loading = ref(true);
const btnLoading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const names = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    type: null,
    auditTime: null,
    decreTime: null,
    status: null,
    description: null,
  },
  rules: {
    type: [{ required: true, message: "记录类型不能为空", trigger: "change" }],
    status: [
      { required: true, message: "审核状态不能为空", trigger: "change" },
    ],
    description: [
      { required: true, message: "描述类型不能为空", trigger: "change" },
    ],
    createBy: [{ required: true, message: "创建者不能为空", trigger: "blur" }],
    decreTime: [
      { required: true, message: "实际减员时间不能为空", trigger: "blur" },
    ],
    staffIds: [{ required: true, message: "请选择", trigger: "change" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询人员变动记录列表 */
function getList() {
  loading.value = true;
  listWlStaffChanges(queryParams.value).then((response) => {
    wlStaffChangesList.value = response.rows;
    total.value = Number(response.total);
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    type: null,
    auditTime: null,
    decreTime: null,
    status: null,
    remark: null,
    description: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    staffIds: [],
  };
  proxy.resetForm("wlStaffChangesRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  names.value = selection.map((item) => item.name);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  form.value.type = "D"; // 默认设置为增员
  open.value = true;
  title.value = "添加人员变动记录";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getWlStaffChanges(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑人员变动记录";
  });
}

/** 提交审核按钮操作 */
function handleSubmit(row) {
  const _id = row.id || ids.value;
  const templateId = row.type === "A" ? "10001" : "10006";
  const params = {
    bizId: _id,
    templateId: templateId,
    describe: row.description,
  };
  proxy.$modal
    .confirm('是否确认提审姓名为"' + row.name + '"的数据项？')
    .then(function () {
      return submitWlStaffChanges(params);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("提交审核成功");
    })
    .catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["wlStaffChangesRef"].validate((valid) => {
    if (valid) {
      btnLoading.value = true;
      const Fn = form.value.id ? updateWlStaffChanges : addWlStaffChanges;
      Fn(form.value)
        .then((response) => {
          proxy.$modal.msgSuccess("操作成功");
          open.value = false;
          btnLoading.value = false;
          getList();
        })
        .catch(() => {
          btnLoading.value = false;
        });
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  const _names = row.name || names.value;
  proxy.$modal
    .confirm('是否确认删除人员姓名为"' + _names + '"的数据项？')
    .then(function () {
      return delWlStaffChanges(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 查看按钮操作 */
function handleView(row) {
  const templateId = row.type === "A" ? "10001" : "10006";
  proxy.getProcessRouterPath(row, templateId, row.id);
}

getList();
</script>
