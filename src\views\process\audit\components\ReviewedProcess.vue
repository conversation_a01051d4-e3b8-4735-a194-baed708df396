<template>
  <div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="名称" prop="instanceName">
        <el-input
          v-model="queryParams.instanceName"
          placeholder="请输入名称"
          clearable
          class="w-[200px]"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery" class="reset-btn">重置</el-button>
      </el-form-item>
    </el-form>

    <el-card class="box-card box-card-no-radius">
      <el-row :gutter="10" class="mb8">
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="processList">
        <el-table-column type="index" label="序号" width="50" align="center">
          <template #default="scope">
            <span>{{ getIndex(scope.$index) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="名称"
          align="center"
          width="200"
          prop="instanceName"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="类型" align="center" prop="templateName" />
        <el-table-column label="申请人" align="center" prop="applyUserName" />
        <el-table-column label="处理结果" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="audit_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="处理时间" align="center" prop="auditTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleView(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import { parseTime } from "@/utils/welink";
import { listMyReviewedProcess } from "@/api/process/audit";
import routerDict from "../routerDict";
const { proxy } = getCurrentInstance();
// 字典数据
const { audit_status } = proxy.useDict("audit_status");
const router = useRouter();
const route = useRoute();
// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 已处理流程列表
const processList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  instanceName: undefined,
});

/** 查询已处理流程列表 */
function getList() {
  loading.value = true;
  queryParams.value.templateId = route.query.templateId;
  listMyReviewedProcess(queryParams.value).then((response) => {
    processList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 查看按钮操作 */
function handleView(row) {
  const routerPath = routerDict[row.templateId]?.path;
  if (routerPath) {
    router.push({
      path: routerPath,
      query: { instanceId: row.instanceId },
      meta: { activeMenu: location.pathname },
    });
  } else {
    proxy.message.warning("暂未配置审核页面");
  }
}

onMounted(() => {
  getList();
});
</script>
