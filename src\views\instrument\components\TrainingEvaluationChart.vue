<template>
  <el-card class="box-card" style="height: 100%;">
    <div class="card-header">
        <span>{{ chartData.title }}</span>
      </div>
    <div class="chart-container">
      <div ref="chartRedRef" class="chart"></div>
      <div ref="chartRef" class="chart"></div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { LabelLayout } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  LabelLayout,
  CanvasRenderer
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
});

const chartRef = ref(null);
const chartRedRef = ref(null);
let chart = null;
let chartRed = null;

const colors = ['#00FDFF', '#00CFFF', '#0269E9'];

const totalCount = computed(() => {
  return props.chartData.data.reduce((sum, item) => sum + item.value, 0);
});

const getPercentage = (value) => {
  return ((value / totalCount.value) * 100).toFixed(1);
};

const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      left: 'right',
    },
    series: [
      {
        name: '评价等级',
        type: 'pie',
        radius: ['55%', '65%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 6
        },
        label:{
          normal:{
            show: true,
                textStyle: {
                  fontSize: 12
                },
                formatter:"{b}:\n{c}人({d}%)"
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
   
        data: props.chartData.data.map((item, index) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  };
  
  chart.setOption(option);
  
  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
};
const initRedChart = () => {
  if (!chartRedRef.value) return;
  chartRed = echarts.init(chartRedRef.value);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      left: 'right',
    },
    series: [
      {
        name: '评价等级',
        type: 'pie',
        radius: ['45%', '53%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 8
        },
        labelLine: {
          show: false
        },
    
   
        data: [{name:'',value:'50',itemStyle:{color:'#FB7293'}},{name:'',value:'50',itemStyle:{color:'#FB7293'}},{name:'',value:'50',itemStyle:{color:'#FB7293'}},{name:'',value:'50',itemStyle:{color:'#FB7293'}}]
      }
    ]
  };
  
  chartRed.setOption(option);
  
  window.addEventListener('resize', () => {
    chartRed && chartRed.resize();
  });
};

watch(() => props.chartData, () => {
  chart && chart.dispose();
  initChart();
}, { deep: true });

onMounted(() => {
  initRedChart();
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
}

.chart-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 350px;
}

.chart-info {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.total-info {
  display: flex;
  align-items: center;
}

.circle-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 30px;
  position: relative;
}

.circle-count {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.circle-text {
  font-size: 14px;
  color: #666;
}

.chart-stats {
  display: flex;
  flex-direction: column;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.stat-label {
  display: flex;
  align-items: center;
  width: 80px;
}

.color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.stat-value {
  font-size: 14px;
  color: #333;
}

.chart {
  flex: 1;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
}
.card-header{
  min-height: 32px;
}
</style> 