<template>
  <el-table-column
    label="调出单位"
    prop="oldProjectId"
    width="200"
    align="center"
  >
    <template #default="slotScope">
      <span>{{ slotScope.row.oldProjectName }}</span>
    </template>
  </el-table-column>
  <el-table-column label="调入单位" prop="oldDeptId" width="200" align="center">
    <template #default="slotScope">
      <RemoteSelect
        v-if="!viewMode"
        v-model="slotScope.row.projectId"
        v-model:modelName="slotScope.row.projectName"
        url="/system/dept/list"
        labelKey="deptName"
        valueKey="deptId"
        :extraParams="{ parentId: '0' }"
        placeholder="请选择调入单位"
      />
      <span v-else>{{ slotScope.row.projectName }}</span>
    </template>
  </el-table-column>
  <el-table-column label="调出部门" prop="oldDeptId" width="200" align="center">
    <template #default="slotScope">
      <span>{{ slotScope.row.oldDeptName }}</span>
    </template>
  </el-table-column>
  <el-table-column label="调入部门" prop="deptId" width="200" align="center">
    <template #default="slotScope">
      <RemoteSelect
        v-if="!viewMode"
        v-model="slotScope.row.deptId"
        v-model:modelName="slotScope.row.deptName"
        url="/system/dept/list"
        labelKey="deptName"
        valueKey="deptId"
        placeholder="请选择调入部门"
        :extraParams="{ parentId: 9999 }"
      />
      <span v-else>{{ slotScope.row.deptName }}</span>
    </template>
  </el-table-column>

  <el-table-column label="调出岗位" prop="oldPostId" width="200" align="center">
    <template #default="slotScope">
      <span>{{ slotScope.row.oldPostName }}</span>
    </template>
  </el-table-column>

  <el-table-column label="调入岗位" prop="postId" width="200" align="center">
    <template #default="slotScope">
      <RemoteSelect
        v-if="!viewMode"
        v-model="slotScope.row.postId"
        v-model:modelName="slotScope.row.postName"
        url="/system/post/list"
        labelKey="postName"
        valueKey="postId"
        placeholder="请选择调入岗位"
        class="w-full"
      />
      <span v-else>{{ slotScope.row.postName }}</span>
    </template>
  </el-table-column>

  <el-table-column label="职务职别" width="200" align="center" prop="jobLevel">
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.jobLevel"
        placeholder="请选择职务职别"
      >
        <el-option
          v-for="dict in position_level"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag
        v-else
        :options="position_level"
        :value="slotScope.row.jobLevel"
      />
    </template>
  </el-table-column>

  <el-table-column
    label="岗位工资"
    prop="postSalary"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.postSalary"
        placeholder="请输入岗位工资"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.postSalary }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="工龄工资"
    prop="senioritySalary"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.senioritySalary"
        placeholder="请输入工龄工资"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.senioritySalary }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="技术补贴"
    prop="artSubsidy"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.artSubsidy"
        placeholder="请输入技术补贴"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.artSubsidy }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="持证津贴"
    prop="certificateSubsidy"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.certificateSubsidy"
        placeholder="请输入持证津贴"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.certificateSubsidy }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="生活补贴"
    prop="liveSubsidy"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.liveSubsidy"
        placeholder="请输入生活补贴"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.liveSubsidy }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="学龄工资"
    prop="schoolSalary"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.schoolSalary"
        placeholder="请输入学龄工资"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.schoolSalary }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="调动时间"
    prop="transferDate"
    width="170"
    align="center"
  >
    <template #default="slotScope">
      <el-date-picker
        v-if="!viewMode"
        v-model="slotScope.row.transferDate"
        type="date"
        style="width: 100%"
        placeholder="请选择调动时间"
        value-format="YYYY-MM-DD"
      />
      <span v-else>{{ slotScope.row.transferDate }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="管理方式"
    prop="manageType"
    width="200"
    align="center"
  >
    <template #default="slotScope">
      <el-input
        v-if="!viewMode"
        v-model="slotScope.row.manageType"
        placeholder="请输入管理方式"
      />
      <span v-else>{{ slotScope.row.manageType }}</span>
    </template>
  </el-table-column>

  <el-table-column
    label="从事工作"
    prop="engageWork"
    width="200"
    align="center"
  >
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.engageWork"
        placeholder="请选择从事工作"
      >
        <el-option
          v-for="dict in work_type"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag v-else :options="work_type" :value="slotScope.row.engageWork" />
    </template>
  </el-table-column>

  <el-table-column
    label="档案号"
    prop="archiveNumber"
    width="200"
    align="center"
  >
    <template #default="slotScope">
      <span>{{ slotScope.row.archiveNumber }}</span>
    </template>
  </el-table-column>
</template>

<script setup>
import { getCurrentInstance } from 'vue';

defineProps({
  viewMode: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();
const { position_level, work_type } = proxy.useDict(
  'position_level',
  'work_type'
);
</script>
