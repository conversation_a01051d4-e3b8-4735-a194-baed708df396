<template>
  <div>
    <!-- 选择按钮 -->
    <el-button @click="openDialog" :type="buttonType" :size="buttonSize">
      {{ buttonText }}
    </el-button>
    <span v-if="showSelected" class="text-sm text-gray-500 ml-2">
      <span v-if="multiple">已选择{{ modelValue?.length || 0 }}人</span>
      <span v-else-if="modelValue && typeof modelValue === 'string'">{{
        userName ? userName : "已选择"
      }}</span>
    </span>

    <!-- 用户选择弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1100px"
      append-to-body
    >
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" ref="queryForm">
        <el-form-item label="关键字" prop="keyword">
          <el-input
            class="w-[200px]"
            v-model="queryParams.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="社保身份" prop="socialSecurityStatus">
          <el-select
            class="w-[200px]"
            v-model="queryParams.socialSecurityStatus"
            placeholder="请选择社保身份"
            clearable
          >
            <el-option
              v-for="dict in social_security_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="人员类型" prop="personnelType">
          <el-select
            class="w-[200px]"
            v-model="queryParams.personnelType"
            placeholder="请选择人员类型"
            clearable
          >
            <el-option
              v-for="dict in personnel_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 用户表格 -->
      <el-table
        v-loading="loading"
        :data="options"
        @selection-change="handleSelectionChange"
        ref="employeeTable"
        row-key="id"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          :selectable="isSelectable"
          v-if="multiple"
        />
        <el-table-column
          type="radio"
          width="50"
          align="center"
          v-else
          @change="handleRadioChange"
        >
          <template #default="scope">
            <el-radio
              v-model="selectedSingle"
              :label="scope.row.id"
              :disabled="disabledSelectData[scope.row.id]"
              @change="() => handleRadioChange(scope.row)"
            >
              &nbsp;
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column
          label="社保身份"
          align="center"
          prop="socialSecurityStatus"
        >
          <template #default="scope">
            <dict-tag
              :options="social_security_status"
              :value="scope.row.socialSecurityStatus"
            />
          </template>
        </el-table-column>
        <el-table-column label="所在单位" align="center" prop="projectName" />
        <el-table-column label="二级机构" align="center" prop="deptName" />
        <el-table-column label="岗位" align="center" prop="postName" />
        <el-table-column label="性别" align="center" prop="gender">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="人员类型" align="center" prop="staffType">
          <template #default="scope">
            <dict-tag :options="personnel_type" :value="scope.row.staffType" />
          </template>
        </el-table-column>
        <el-table-column label="" align="center" prop="titleCategory">
          <template #default="scope">
            <dict-tag
              :options="title_category"
              :value="scope.row.titleCategory"
            />
          </template>
        </el-table-column>
        <el-table-column label="从事工作" align="center" prop="jobContent">
          <template #default="scope">
            <dict-tag :options="work_type" :value="scope.row.jobContent" />
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <template #footer>
        <div class="w-full flex justify-center mt-4">
          <el-button @click="dialogVisible = false" class="cancel-btn"
            >取消</el-button
          >
          <el-button type="primary" @click="confirmSelection">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  watch,
  defineProps,
  defineEmits,
  onMounted,
  nextTick,
  getCurrentInstance,
} from "vue";
import { listEmployee } from "@/api/personnelFiles/incumbentEmployee";
import { useFormItem } from "element-plus";
const { formItem } = useFormItem();
const { proxy } = getCurrentInstance();

// 字典数据
const {
  sys_user_sex,
  social_security_status,
  personnel_type,
  title_category,
  work_type,
} = proxy.useDict(
  "work_type",
  "title_category",
  "sys_user_sex",
  "social_security_status",
  "personnel_type"
);

const props = defineProps({
  // 选中的值
  modelValue: {
    type: [String, Number, Array],
    default: null,
  },
  modelName: {
    type: [String],
    default: null,
  },
  // 是否显示已选择人数
  showSelected: {
    type: Boolean,
    default: true,
  },
  // 需要禁用选择的列
  disabledSelectColumns: {
    type: Array,
    default: [],
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 按钮文本
  buttonText: {
    type: String,
    default: "选择人员",
  },
  // 按钮类型
  buttonType: {
    type: String,
    default: "primary",
  },
  // 按钮大小
  buttonSize: {
    type: String,
    default: "default",
  },
  // 弹窗标题
  dialogTitle: {
    type: String,
    default: "选择人员",
  },
});
const userName = ref(props.modelName);
const emit = defineEmits(["update:modelValue", "change"]);
// 禁用选择的数据
const disabledSelectData = ref({});

// 显示状态
const dialogVisible = ref(false);
const loading = ref(false);
const options = ref([]);
const total = ref(0);
const employeeTable = ref(null);
// 当前正在初始化
const isInit = ref(false);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  socialSecurityStatus: undefined,
  personnelType: undefined,
});

// 已选择数据
const selectedValues = ref([]);
const selectedOptions = ref([]);
const selectedSingle = ref(null);

// 缓存所有页已选用户，用于保持选中状态
const selectedMap = ref({});

// 打开弹窗
function openDialog() {
  dialogVisible.value = true;
  // 初始化已选数据
  initSelectedData();
  // 获取人员列表
  getList();
}

// 初始化已选数据
function initSelectedData() {
  if (!props.modelValue) return;
  if (props.multiple) {
    // 多选模式
    if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      selectedValues.value = [...props.modelValue];
      // 重置选择映射
      selectedMap.value = {};
      props.modelValue.forEach((id) => {
        selectedMap.value[id] = true;
      });
    } else {
      selectedValues.value = [];
      selectedMap.value = {};
    }
  } else {
    // 单选模式
    selectedSingle.value = props.modelValue;
    if (props.modelValue) {
      selectedValues.value = [props.modelValue];
    } else {
      selectedValues.value = [];
    }
  }
}

// 获取人员列表
function getList() {
  loading.value = true;
  isInit.value = true;
  listEmployee({
    ...queryParams.value,
  }).then((res) => {
    options.value = res.rows;
    total.value = res.total;
    loading.value = false;
    // 在数据加载完成后，恢复选中状态
    nextTick(() => {
      setTableSelection();
    });
  });
}
// 是否可选择
const isSelectable = (row) => {
  return !disabledSelectData.value[row.id];
};
// 设置表格选中状态
function setTableSelection() {
  if (!employeeTable.value) return;
  // 先清除所有选择再重新设置，防止出现状态错误
  employeeTable.value.clearSelection();
  if (props.multiple) {
    // 多选模式：根据selectedMap恢复选中状态
    options.value.forEach((row) => {
      if (selectedMap.value[row.id]) {
        employeeTable.value.toggleRowSelection(row, true);
      }
    });
  } else {
    // 单选模式：根据selectedSingle设置选中
    if (selectedSingle.value) {
      selectedValues.value = [selectedSingle.value];
    }
  }
  isInit.value = false;
}

// 搜索按钮操作
function handleQuery() {
  if (isInit.value) return;
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

// 多选模式下的选择变更处理
function handleSelectionChange(selection) {
  if (isInit.value) return;
  if (props.multiple) {
    // 更新当前页的选中状态到Map
    options.value.forEach((row) => {
      const selected = selection.some((item) => item.id === row.id);
      if (selected) {
        selectedMap.value[row.id] = true;
      } else {
        selectedMap.value[row.id] = false;
      }
    });
    // 更新已选值列表
    selectedValues.value = Object.keys(selectedMap.value).filter(
      (key) => selectedMap.value[key]
    );
    selectedOptions.value = selection;
  }
}

// 单选模式的选择处理
function handleRadioChange(row) {
  if (disabledSelectData.value[row.id]) return;
  selectedSingle.value = row.id;
  selectedValues.value = [row.id];
  selectedOptions.value = [row];
}

// 行点击事件
function handleRowClick(row) {
  if (disabledSelectData.value[row.id]) return;
  if (props.multiple) {
    selectedMap.value[row.id] = !selectedMap.value[row.id];
    // 多选模式
    employeeTable.value.toggleRowSelection(row, selectedMap.value[row.id]);
  } else {
    handleRadioChange(row);
  }
}

// 确认选择
function confirmSelection() {
  if (props.multiple) {
    // 多选模式
    emit("update:modelValue", selectedValues.value);
    emit(
      "change",
      selectedValues.value.map((id) => {
        // 查找完整的人员信息
        const employee = options.value.find((item) => item.id === id);
        return employee || { id: id };
      })
    );
  } else {
    // 单选模式
    emit("update:modelValue", selectedSingle.value);

    // 查找人员完整信息
    const selectedEmployee = options.value.find(
      (item) => item.id === selectedSingle.value
    );
    emit("change", selectedEmployee || null);
    userName.value = selectedEmployee.name;
  }

  dialogVisible.value = false;

  formItem?.validate("change"); // 触发表单验证
}

// 查看人员详情
function handleView(row) {
  // 这里可以实现查看人员详情的逻辑
  console.log("查看人员详情", row);
}
// 创建禁用选择的映射
function createDisabledSelectMap(arrs) {
  if (!Array.isArray(arrs)) {
    arrs = arrs.split(",");
  }
  // 创建映射前清空禁用选择的数据
  disabledSelectData.value = {};
  arrs.forEach((id) => {
    if (props.multiple) {
      // 需要排除自己已经选择的数据
      !selectedMap.value[id] && (disabledSelectData.value[id] = true);
    } else {
      selectedSingle.value != id && (disabledSelectData.value[id] = true);
    }
  });
}
// 监听modelValue变化
watch(
  () => props.modelValue,
  (val) => {
    if (props.multiple) {
      if (Array.isArray(val)) {
        selectedValues.value = [...val];
        // 更新选择映射
        selectedMap.value = {};
        val.forEach((id) => {
          selectedMap.value[id] = true;
        });
      } else {
        selectedValues.value = [];
        selectedMap.value = {};
      }
    } else {
      selectedSingle.value = val;
      selectedValues.value = val ? [val] : [];
    }
    createDisabledSelectMap(props.disabledSelectColumns);
    // 如果弹窗可见，更新表格选中状态
    if (dialogVisible.value) {
      nextTick(() => {
        setTableSelection();
      });
    }
  },
  { immediate: true }
);
// 监听disabledSelectColumns变化
watch(
  () => props.disabledSelectColumns,
  (val) => {
    createDisabledSelectMap(val);
  }
);
watch(
  () => props.modelName,
  (val) => {
    if (val) {
      userName.value = val;
    }
  }
);
// 组件挂载时初始化数据
onMounted(() => {
  initSelectedData();
  createDisabledSelectMap(props.disabledSelectColumns);
});
</script>

<style scoped>
div /deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #dfdfdf;
  border-color: #dfdfdf;
}
div /deep/ .el-radio__input.is-disabled .el-radio__inner {
  background-color: #dfdfdf;
  border-color: #dfdfdf;
}
</style>
