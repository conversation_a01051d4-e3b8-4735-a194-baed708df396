<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="单位名称" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              v-modelName="queryParams.projectName"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择项目"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="人员名称" prop="staffName">
            <el-input
              v-model="queryParams.staffName"
              placeholder="请输入人员名称"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="时间" prop="yearMonth">
            <el-date-picker
              v-model="monthrangeDate"
              type="monthrange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              class="w-[200px]"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-table v-loading="loading" :data="payrollList" border>
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          />
          <el-table-column
            label="编号"
            prop="staffCode"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="姓名"
            prop="name"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="性别"
            prop="gender"
            align="center"
            min-width="80"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column
            label="民族"
            prop="ethnicity"
            align="center"
            min-width="80"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="身份证号"
            prop="idNumber"
            align="center"
            min-width="180"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="银行账号"
            prop="bankAccount"
            align="center"
            min-width="180"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="单位名称"
            prop="projectName"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="部门"
            prop="deptName"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="岗位"
            prop="postName"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <span>{{ scope.row.position || scope.row.postName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="岗位等级"
            prop="positionLevel"
            align="center"
            min-width="100"
            position_level
          >
            <template #default="scope">
              <dict-tag
                :options="position_level"
                :value="scope.row.positionLevel"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="岗位状态"
            prop="positionStatus"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <dict-tag
                :options="position_status"
                :value="scope.row.positionStatus"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="薪酬类别"
            prop="salaryCategory"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="技术职务"
            prop="professionalTitle"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <dict-tag
                :options="professional_title"
                :value="scope.row.professionalTitle"
              />
            </template>
          </el-table-column>

          <el-table-column
            label="技术等级"
            prop="workerSkillLevel"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <dict-tag
                :options="worker_skill_level"
                :value="scope.row.workerSkillLevel"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="技能鉴定"
            prop="workRace"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <dict-tag
                :options="identify_job_types"
                :value="scope.row.workRace"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="鉴定等级"
            prop="workGrade"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          >
            <template #default="scope">
              <dict-tag
                :options="worker_skill_level"
                :value="scope.row.workGrade"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="岗位系数"
            prop="positionCoefficient"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="岗位工资"
            prop="positionSalary"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="岗位工资标准"
            prop="positionSalaryStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="基本年薪"
            prop="baseSalary"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="基本年薪标准"
            prop="baseSalaryStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="效益工资"
            prop="performancePay"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="效益工资标准"
            prop="performanceStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="绩效工资"
            prop="performanceSalary"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="绩效工资标准"
            prop="performanceSalaryStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="绩效工资系数"
            prop="performanceCoefficient"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="绩效年薪"
            prop="performanceBonusTotal"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="年功工资"
            prop="annualMeritPay"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="学龄工资"
            prop="academicAgeStandard"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="工龄工资标准"
            prop="seniorityStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="技术（技能）津贴"
            prop="technicalAllowance"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="技术（技能）津贴标准"
            prop="technicalAllowanceStandard"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="持证津贴"
            prop="certificationAllowance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="持证津贴标准"
            prop="certificationAllowanceStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="驻外生活补贴"
            prop="expatLivingAllowance"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="驻外生活补贴标准"
            prop="expatLivingStandard"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="回族生活补贴"
            prop="huiEthnicityAllowance"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="专家人员津贴"
            prop="expertAllowance"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="专家人员津贴标准"
            prop="expertAllowanceStandard"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="艰苦边远地区生活补贴"
            prop="remoteAllowance"
            align="center"
            min-width="180"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="艰苦边远地区生活补贴标准"
            prop="remoteAllowanceStandard"
            align="center"
            min-width="200"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="流动津贴"
            prop="mobilityAllowance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="流动津贴标准"
            prop="mobilityAllowanceStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="隧道津贴"
            prop="tunnelAllowance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="隧道津贴标准"
            prop="tunnelAllowanceStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="工班长津贴"
            prop="foremanAllowance"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="工班长津贴标准"
            prop="foremanAllowanceStandard"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="台车长、盾构/TBM主司机津贴"
            prop="tbmDriverAllowance"
            align="center"
            min-width="200"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="台车长、盾构/TBM主司机津贴标准"
            prop="tbmDriverAllowanceStandard"
            align="center"
            min-width="250"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="政治指导员委派津贴"
            prop="politicalInstructorAllowance"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="政治指导员委派津贴标准"
            prop="politicalInstructorAllowanceStandard"
            align="center"
            min-width="200"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="兼职团组书记津贴"
            prop="partTimeSecretaryAllowance"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="兼职团组书记津贴标准"
            prop="partTimeSecretaryAllowanceStandard"
            align="center"
            min-width="180"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="中夜津贴"
            prop="nightShiftAllowance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="房租补贴标准"
            prop="housingSubsidyStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="交通补贴标准"
            prop="transportationSubsidy"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="电脑补贴"
            prop="computerSubsidy"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="话费补贴"
            prop="phoneSubsidy"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="防暑降温标准"
            prop="heatReliefStandard"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="见习生活补贴"
            prop="traineeLivingAllowance"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="见习生活补贴标准"
            prop="traineeLivingStandard"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="非在岗人员生活费"
            prop="inactiveLivingAllowance"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="年度绩效考核奖"
            prop="annualPerformanceBonus"
            align="center"
            min-width="150"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="激励考核奖"
            prop="incentiveBonus"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="生产奖金"
            prop="productionBonus"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="一次性奖励"
            prop="oneTimeBonus"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="特殊奖励"
            prop="specialAwards"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="加班工资"
            prop="overtimePay"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="周末加班"
            prop="weekendOvertime"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="制度工天"
            prop="systemWorkingDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="法定工天"
            prop="legalWorkingDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="出勤天数"
            prop="attendanceDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="中班工天"
            prop="midShiftDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="夜班工天"
            prop="nightShiftDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="进洞工天"
            prop="tunnelWorkingDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="节日工天"
            prop="holidayWorkingDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="年休工天"
            prop="annualLeaveDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="婚假工天"
            prop="marriageLeaveDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="丧假工天"
            prop="bereavementLeaveDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="产假工天"
            prop="maternityLeaveDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="探亲工天"
            prop="familyVisitDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="看护假工天"
            prop="nursingLeaveDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="工伤工天"
            prop="injuryLeaveDays"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="零星病假工天"
            prop="shortTermSickLeave"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="长期病假工天"
            prop="longTermSickLeave"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="病假时段"
            prop="sickLeavePeriod"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="个人所得税"
            prop="inputTax"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="个税预扣实缴"
            prop="prepaidTax"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="个税预交"
            prop="prepaidTaxFlag"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="养老保险"
            prop="pensionInsurance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="医疗保险"
            prop="medicalInsurance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="失业保险"
            prop="unemploymentInsurance"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="大额医疗保险"
            prop="criticalIllnessInsurance"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="住房公积金"
            prop="housingFund"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="企业年金"
            prop="enterpriseAnnuity"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="房租"
            prop="rent"
            align="center"
            min-width="80"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="物管费"
            prop="propertyFee"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="水电费"
            prop="utilities"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="女工费"
            prop="femaleWorkerFee"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="备用金"
            prop="pettyCash"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="合并扣税款项"
            prop="mergedTaxItems"
            align="center"
            min-width="120"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="其他应扣"
            prop="otherDeductions"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="其他收入"
            prop="otherIncome"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="其他津贴"
            prop="otherAllowances"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="应发工资"
            prop="grossPay"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
          <el-table-column
            label="实发工资"
            prop="netPay"
            align="center"
            min-width="100"
            :formatter="defaultFormatter"
          />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { listPayroll } from "@/api/salary/payroll";
import RemoteSelect from "@/components/RemoteSelect";
import { parseTime } from "@/utils/welink";
const { proxy } = getCurrentInstance();
const {
  professional_title,
  position_level,
  sys_user_sex,
  position_status,
  worker_skill_level,
  identify_job_types,
} = proxy.useDict(
  "professional_title",
  "position_level",
  "sys_user_sex",
  "position_status",
  "worker_skill_level",
  "identify_job_types"
);
// 遮罩层
const loading = ref(false);
// 总条数
const total = ref(0);
// 工资名册表格数据
const payrollList = ref([]);
// 显示搜索条件
const showSearch = ref(true);
const monthrangeDate = ref([]);
// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  staffName: undefined,
});

// 默认格式化函数，处理空值显示为--
const defaultFormatter = (row, column) => {
  const value = row[column.property];
  return value !== undefined && value !== null && value !== "" ? value : "--";
};

/** 查询工资名册列表 */
function getList() {
  loading.value = true;
  const [beginMonth, endMonth] = monthrangeDate.value;
  listPayroll({ ...queryParams.value, beginMonth, endMonth }).then(
    (response) => {
      payrollList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    }
  );
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  monthrangeDate.value = [];
  queryParams.value.projectName = undefined;
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "/railway/payroll/export",
    {
      ...queryParams.value,
    },
    `工资名册数据_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style>
