<template>
  <div class="attachment-display">
    <!-- 简洁模式：在表格中显示 -->
    <div v-if="mode === 'compact'" class="compact-view">
      <el-popover
        :width="350"
        trigger="hover"
        placement="top-start"
        :disabled="!attachments || attachments?.length === 0"
      >
        <template #reference>
          <div
            class="attachment-summary justify-center"
            v-if="attachments && attachments?.length > 0"
          >
            <el-icon class="attachment-icon"><paperclip /></el-icon>
            <span class="attachment-count">
              {{ attachments.length }}个附件
            </span>
          </div>
          <span v-else class="no-attachment">无附件</span>
        </template>

        <div class="attachment-popover">
          <div class="popover-header">
            <span class="header-title">附件列表</span>
            <span class="file-count">共{{ attachments?.length }}个文件</span>
          </div>
          <!-- 使用与list模式相同的结构 -->
          <div class="attachment-list-simple w-full">
            <div
              v-for="(attachment, index) in attachments"
              :key="index"
              class="attachment-item-simple flex justify-between"
            >
              <div class="flex items-center">
                <el-icon class="file-icon-small mr-2">
                  <component :is="getFileIcon(attachment.name)" />
                </el-icon>
                <span class="file-name-simple" :title="attachment.name">
                  {{ getFileName(attachment.name) }}
                </span>
              </div>
              <!-- 这里加form是为了表单下设置全局禁用这里不受影响 -->
              <el-form>
                <div class="actions-simple">
                  <preview-file :url="attachment.path || attachment.url">
                    <el-button type="primary" link size="small ">
                      预览
                    </el-button>
                  </preview-file>

                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleDownload(attachment)"
                  >
                    下载
                  </el-button>
                </div>
              </el-form>
            </div>
          </div>
        </div>
      </el-popover>
    </div>

    <!-- 展开模式：在详情页中显示 -->
    <div v-else-if="mode === 'expanded'" class="expanded-view">
      <div v-if="!attachments || attachments.length === 0" class="no-files">
        <el-empty description="暂无附件" :image-size="60" />
      </div>
      <div v-else class="attachment-grid">
        <div
          v-for="(attachment, index) in attachments"
          :key="index"
          class="attachment-card"
        >
          <div class="card-header">
            <el-icon class="file-icon-large">
              <component :is="getFileIcon(attachment.name)" />
            </el-icon>
          </div>
          <div class="card-body">
            <div class="file-name" :title="attachment.name">
              {{ getFileName(attachment.name) }}
            </div>
            <div class="file-info">
              <span class="file-extension">{{
                getFileExtension(attachment.name)
              }}</span>
              <span v-if="attachment.size" class="file-size">
                {{ formatFileSize(attachment.size) }}
              </span>
            </div>
          </div>
          <el-form>
            <div class="card-actions">
              <preview-file :url="attachment.path || attachment.url">
                <el-button type="primary" link size="small"> 预览 </el-button>
              </preview-file>

              <el-button
                type="default"
                size="small"
                @click="handleDownload(attachment)"
              >
                <el-icon><download /></el-icon>
                下载
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 列表模式：简单的列表显示 -->
    <div v-else class="list-view">
      <div
        v-if="!attachments || attachments.length === 0"
        class="no-attachment"
      >
        无附件
      </div>
      <div v-else class="attachment-list-simple">
        <div
          v-for="(attachment, index) in attachments"
          :key="index"
          class="attachment-item-simple"
        >
          <el-icon class="file-icon-small">
            <component :is="getFileIcon(attachment.name)" />
          </el-icon>
          <span class="file-name-simple">
            {{ getFileName(attachment.name) }}
          </span>
          <el-form>
            <div class="actions-simple">
              <preview-file :url="attachment.path || attachment.url">
                <el-button type="primary" link size="small"> 预览 </el-button>
              </preview-file>

              <el-button
                type="primary"
                link
                size="small"
                @click="handleDownload(attachment)"
              >
                下载
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { downloadFile } from "@/utils/welink";
import {
  Paperclip,
  Download,
  Document,
  Picture,
  VideoPlay,
  Headset,
  Files,
  FolderOpened,
} from "@element-plus/icons-vue";

const props = defineProps({
  // 附件列表
  attachments: {
    type: Array,
    default: () => [],
  },
  // 显示模式：compact(紧凑) | expanded(展开) | list(列表)
  mode: {
    type: String,
    default: "compact",
    validator: (value) => ["compact", "expanded", "list"].includes(value),
  },
  // 最大显示数量（仅在compact模式下有效）
  maxCount: {
    type: Number,
    default: 3,
  },
});

const emit = defineEmits(["preview", "download"]);

const previewUrl = ref("");
const previewRef = ref(null);

// 获取文件名（去掉路径）
const getFileName = (name) => {
  if (!name) return "未知文件";
  const fileName = name.split("/").pop() || name;
  return fileName.length > 20 ? fileName.substring(0, 20) + "..." : fileName;
};

// 获取文件扩展名
const getFileExtension = (name) => {
  if (!name) return "";
  const ext = name.split(".").pop();
  return ext ? ext.toUpperCase() : "";
};

// 获取文件图标
const getFileIcon = (fileName) => {
  if (!fileName) return Document;

  const ext = fileName.split(".").pop()?.toLowerCase();

  const iconMap = {
    // 图片文件
    jpg: Picture,
    jpeg: Picture,
    png: Picture,
    gif: Picture,
    bmp: Picture,
    svg: Picture,
    webp: Picture,

    // 文档文件
    doc: Document,
    docx: Document,
    pdf: Document,
    txt: Document,
    rtf: Document,

    // 表格文件
    xls: Files,
    xlsx: Files,
    csv: Files,

    // 演示文件
    ppt: Files,
    pptx: Files,

    // 视频文件
    mp4: VideoPlay,
    avi: VideoPlay,
    mov: VideoPlay,
    wmv: VideoPlay,
    flv: VideoPlay,
    mkv: VideoPlay,

    // 音频文件
    mp3: Headset,
    wav: Headset,
    flac: Headset,
    aac: Headset,

    // 压缩文件
    zip: FolderOpened,
    rar: FolderOpened,
    "7z": FolderOpened,
    tar: FolderOpened,
    gz: FolderOpened,
  };

  return iconMap[ext] || Document;
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return "";

  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
};

// 处理下载
const handleDownload = (attachment) => {
  downloadFile(attachment.path || attachment.url, attachment.name);
  emit("download", attachment);
};
</script>

<style lang="scss" scoped>
.attachment-display {
  .compact-view {
    .attachment-summary {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      color: #606266;
      font-size: 14px;

      .attachment-icon {
        color: #409eff;
        font-size: 16px;
      }

      .attachment-count {
        color: #409eff;
        font-weight: 500;
      }

      .no-attachment {
        color: #c0c4cc;
      }

      &:hover {
        color: #409eff;
      }
    }
  }

  .attachment-popover {
    .popover-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;

      .header-title {
        font-weight: 600;
        color: #303133;
      }

      .file-count {
        font-size: 12px;
        color: #909399;
      }
    }

    .attachment-list-simple {
      max-height: 300px;
      overflow-y: auto;

      .attachment-item-simple {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 0;
        border-bottom: 1px solid #f5f7fa;

        &:last-child {
          border-bottom: none;
        }

        .file-icon-small {
          color: #409eff;
          font-size: 14px;
          flex-shrink: 0;
        }

        .file-name-simple {
          flex: 1;
          font-size: 14px;
          color: #303133;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 150px;

          &:hover {
            color: #409eff;
          }
        }

        .actions-simple {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .expanded-view {
    .no-files {
      text-align: center;
      padding: 20px;
    }

    .attachment-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;
    }

    .attachment-card {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      transition: all 0.3s;
      background: #fff;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
      }

      .card-header {
        margin-bottom: 12px;

        .file-icon-large {
          font-size: 32px;
          color: #409eff;
        }
      }

      .card-body {
        margin-bottom: 16px;

        .file-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 8px;
          word-break: break-all;
          line-height: 1.4;
        }

        .file-info {
          display: flex;
          justify-content: center;
          gap: 8px;
          font-size: 12px;
          color: #909399;

          .file-extension {
            background: #f5f7fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
      }
    }
  }

  .list-view {
    .attachment-list-simple {
      .attachment-item-simple {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 0;
        border-bottom: 1px solid #f5f7fa;

        &:last-child {
          border-bottom: none;
        }

        .file-icon-small {
          color: #409eff;
          font-size: 14px;
          flex-shrink: 0;
        }

        .file-name-simple {
          flex: 1;
          font-size: 14px;
          color: #303133;
          cursor: pointer;

          &:hover {
            color: #409eff;
          }
        }

        .actions-simple {
          display: flex;
          gap: 8px;
        }
      }
    }

    .no-attachment {
      color: #c0c4cc;
      font-size: 14px;
      text-align: center;
      padding: 12px 0;
    }
  }
}

// 滚动条样式
:deep(.attachment-list-simple::-webkit-scrollbar) {
  width: 4px;
}

:deep(.attachment-list-simple::-webkit-scrollbar-track) {
  background: #f5f7fa;
  border-radius: 2px;
}

:deep(.attachment-list-simple::-webkit-scrollbar-thumb) {
  background: #c0c4cc;
  border-radius: 2px;
}

:deep(.attachment-list-simple::-webkit-scrollbar-thumb:hover) {
  background: #909399;
}
</style>

<style lang="scss">
/* 全局样式 - 用于 el-popover 内容 */
.el-popover.el-popper {
  .attachment-popover {
    .popover-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;

      .header-title {
        font-weight: 600;
        color: #303133;
      }

      .file-count {
        font-size: 12px;
        color: #909399;
      }
    }

    .attachment-list-simple {
      max-height: 300px;
      overflow-y: auto;

      .attachment-item-simple {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 0;
        border-bottom: 1px solid #f5f7fa;

        &:last-child {
          border-bottom: none;
        }

        .file-icon-small {
          color: #409eff;
          font-size: 14px;
          flex-shrink: 0;
        }

        .file-name-simple {
          flex: 1;
          font-size: 14px;
          color: #303133;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;

          &:hover {
            color: #409eff;
          }
        }

        .actions-simple {
          display: flex;
          gap: 8px;
        }
      }

      // 滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c0c4cc;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #909399;
      }
    }
  }
}
</style>
