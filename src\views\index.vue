<template>
  <div class="app-container">
    <el-row :gutter="16" class="main-row text-[#222222]">
      <el-col :span="24" class="mb-2">
        <QueryFormCard @search="getQueryViewF" />
      </el-col>
      <el-col :xs="24" :sm="11" :md="11" class="mb-2">
        <!-- 人员动态、劳资信息、培训情况统计卡片 -->
        <StatCard
          v-for="(stat, index) in statisticCards"
          :key="index"
          :title="stat.title"
          :stats="stat.stats"
          :routeName="stat.routeName"
          @click="handleClickCard(stat.routePath)"
        />
      </el-col>

      <el-col :xs="24" :sm="13" :md="13" class="mb-2">
        <!-- 主要待办卡片 -->
        <MainCard :card-data="mainCard" @click="handleClickCard" />

        <!-- 小待办卡片网格 -->
        <el-row class="w-full flex flex-wrap" :gutter="10">
          <el-col
            v-for="(card, index) in backlogCards"
            :key="index"
            :span="8"
            class="mb-4"
          >
            <BacklogCard :card-data="card" @click="handleClickCard" />
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-row :gutter="16" class="main-row">
      <!-- 待办事项区域 -->
      <el-col :xs="24" :sm="11" :md="11" class="mb-4">
        <TodoCard
          :todo-list="todoList"
          @click="todoData.onClick"
          @more="router.push({ name: todoData.morePathName })"
        />
      </el-col>

      <!-- 通知消息区域 -->
      <el-col :xs="24" :sm="13" :md="13" class="mb-4">
        <NoticeCard
          :notice-list="noticeList"
          @click="handleViewNotice"
          @more="router.push({ name: noticeData.morePathName })"
        />
      </el-col>
    </el-row>

    <!-- 预警信息 -->
    <WarningCard
      :warning-types="warningTypes"
      :active-type="activeWarningType"
      :warning-list="warningList"
      :loading="warningLoading"
      @more="router.push({ name: warningData[activeWarningType].morePathName })"
      @change-type="handleChangeWarningType"
      @view="handleViewWarning"
    />

    <!-- 通知详情对话框 -->
    <el-dialog
      title="通知详情"
      v-model="noticeDialogVisible"
      width="600px"
      append-to-body
    >
      <div v-if="currentNotice">
        <div class="notify-detail-item">
          <span class="label">通知主题：</span>
          <span>{{ currentNotice.todoSubject }}</span>
        </div>

        <div class="notify-detail-item">
          <span class="label">更新时间：</span>
          <span>{{ currentNotice.updateTime }}</span>
        </div>
        <div class="notify-detail-item">
          <span class="label">更新人：</span>
          <span>{{
            currentNotice.updateBy || currentNotice.createBy || '--'
          }}</span>
        </div>
        <div class="notify-detail-item">
          <span class="label">状态：</span>
          <span>{{ currentNotice.status === '1' ? '已处理' : '待处理' }}</span>
        </div>
        <div class="notify-detail-content">
          <div class="content-title">详细内容：</div>
          <div
            class="content-body"
            v-html="currentNotice.content || '--'"
          ></div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="noticeDialogVisible = false"
            >关 闭</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { listMyPendingProcess } from '@/api/process/audit';
import routerDict from '@/views/process/audit/routerDict';
import useUserStore from '@/store/modules/user';
import {
  listFlowNotify,
  readFlowNotify,
} from '@/api/notificationCenter/notice';
import { parseTime } from '@/utils/welink';
import { listOverEmploymentWarning } from '@/api/personnelFiles/overEmploymentWarning';
import { listProjectOrgSalaryWarn } from '@/api/salary/projectOrgSalary';
import {
  listSpecialWarning,
  listEnrollWarning,
} from '@/api/certificate/warning';
import { listRetirementWarning } from '@/api/personnelFiles/retirementWarning';
import { listContractWarning } from '@/api/contract/warning';
import { getBacklogData, getQueryView } from '@/api/index';
import { indexImages } from '@/utils/importImages';

// 导入组件
import StatCard from '@/components/Workbenches/StatCard.vue';
import MainCard from '@/components/Workbenches/MainCard.vue';
import BacklogCard from '@/components/Workbenches/BacklogCard.vue';
import TodoCard from '@/components/Workbenches/TodoCard.vue';
import NoticeCard from '@/components/Workbenches/NoticeCard.vue';
import WarningCard from '@/components/Workbenches/WarningCard.vue';
import QueryFormCard from '@/components/Workbenches/QueryFormCard.vue';

const router = useRouter();

// 基础数据
const backlogData = ref();
const todoList = ref([]);
const noticeList = ref([]);
const warningList = ref([]);
const activeWarningType = ref('retire');
const warningLoading = ref(false);
const noticeDialogVisible = ref(false);
const currentNotice = ref(null);

// 统计卡片数据配置
const statisticCards = ref([
  {
    title: '人员动态',
    routeName: 'IncumbentEmployee',
    stats: [
      {
        label: '员工规模',
        value: '',
        icon: indexImages.img14,
        bgColor: '#FFF2F3',
      },
      {
        label: '在职人员',
        value: '',
        icon: indexImages.img15,
        bgColor: '#E3ECFF',
      },
      {
        label: '富余人员',
        value: '',
        icon: indexImages.img16,
        bgColor: '#EFEAFD',
      },
      {
        label: '其他人员',
        value: '',
        icon: indexImages.img17,
        bgColor: '#DBF5E9',
      },
    ],
  },
  {
    title: '劳资信息',
    stats: [
      {
        label: '工资拖欠次数',
        value: '',
        icon: indexImages.img18,
        bgColor: '#FBF2FF',
        routeName: 'WageArrears',
      },
      {
        label: '工资总额',
        value: '',
        icon: indexImages.img19,
        bgColor: '#DCFBFC',
        routeName: 'SalaryPayBook',
      },
    ],
  },
  {
    title: '培训情况',
    stats: [
      {
        label: '委培',
        value: '',
        icon: indexImages.img20,
        bgColor: '#E6F7E7',
        routeName: 'Consign',
      },
      {
        label: '自培',
        value: '',
        icon: indexImages.img21,
        bgColor: '#FEEFFB',
        routeName: 'SelfTraining',
      },
    ],
  },
]);

// 待办数据字典
const backlogDict = {
  A: '10008',
  B: '10009',
  C: '10006',
  D: '10007',
  E: '10005',
  F: '10004',
  G: '10003',
  H: '10010',
  I: '10011',
};

// 主要待办卡片
const mainCard = computed(() => ({
  title: '人员基本信息变动审核',
  count: backlogData.value?.A || 0,
  image: indexImages.img1,
  icon: indexImages.img2,
  id: backlogDict.A,
}));

// 小待办卡片配置
const backlogCards = computed(() => [
  {
    title: '项目人员需求审核',
    count: backlogData.value?.B || 0,
    image: indexImages.img3,
    id: backlogDict.B,
  },
  {
    title: '公司发文抄录',
    count: backlogData.value?.D || 0,
    image: indexImages.img4,
    id: backlogDict.D,
  },
  {
    title: '退休人员申请审核',
    count: backlogData.value?.C || 0,
    image: indexImages.img4,
    id: backlogDict.C,
  },
  {
    title: '授课津贴审核',
    count: backlogData.value?.E || 0,
    image: indexImages.img7,
    id: backlogDict.E,
  },
  {
    title: '送培审核',
    count: backlogData.value?.F || 0,
    image: indexImages.img8,
    id: backlogDict.F,
  },
  {
    title: '培训报销审核',
    count: backlogData.value?.F || 0,
    // count: backlogData.value?.G || 0,
    image: indexImages.img9,
    // id: backlogDict.G,
    id: backlogDict.F,
  },
  {
    title: '劳资报表',
    count: backlogData.value?.H || 0,
    image: indexImages.img10,
    id: backlogDict.H,
    route: () => {
      router.push({ name: 'ProjectOrgSalary', query: { status: 0 } });
    },
  },
  {
    title: '特种作业证书',
    count: backlogData.value?.I || 0,
    image: indexImages.img11,
    id: backlogDict.I,
    route: () => {
      router.push({
        name: 'CertificateSpecial',
        query: { status: 'HAVEN_T_UPLOADED' },
      });
    },
  },
]);

// 待办数据配置
const todoData = {
  morePathName: 'ProcessAudit',
  onClick: (item) => {
    const routerPath = routerDict[item.templateId]?.path;
    if (routerPath) {
      router.push({
        path: routerPath,
        query: { instanceId: item.instanceId },
      });
    }
  },
};

// 通知数据配置
const noticeData = {
  morePathName: 'NotificationCenterNotice',
};

// 预警类型配置
const warningTypes = [
  // { label: "超员预警", value: "overstaff" },
  { label: '退休预警', value: 'retire' },
  { label: '合同预警', value: 'contract' },
  { label: '薪酬预警', value: 'salary' },
  { label: '特种作业证书预警', value: 'certificate' },
  { label: '注册岗位证书预警', value: 'register' },
];

// 预警数据配置
const warningData = {
  overstaff: {
    title: '超员预警',
    func: listOverEmploymentWarning,
    params: {
      yearMonth: parseTime(new Date(), '{y}-{m}'),
    },
    morePathName: 'OverEmploymentWarning',
    pathName: '',
    getStr: (item) => {
      return `${item.leaderUserName}负责的${item.projectName}项目需求员工超员，请尽快处理!`;
    },
    getQuery: (item) => {
      return { id: item.id, view: true };
    },
  },
  retire: {
    title: '退休预警',
    getTitle: (item) => {
      if (item.warnMonth <= 0) return '已到期';
      return `临期${item.warnMonth}个月预警`;
    },
    func: listRetirementWarning,
    params: {},
    morePathName: 'RetirementWarning',
    pathName: 'IncumbentEmployeeAddOrEdit',
    getStatusColor: (item) => {
      if (item.warnMonth <= 0) return 'info';
      if (item.warnMonth === 1) return 'danger';
      if (item.warnMonth >= 2) return 'warning';
    },
    getStr: (item) => {
      if (item.warnMonth <= 0) {
        return `${item.name || item.staffName}已到期，请尽快处理!`;
      }
      return `${item.name || item.staffName}即将退休，请尽快处理!`;
    },
    getQuery: (item) => {
      return { id: item.id, type: 'check' };
    },
  },
  contract: {
    title: '合同预警',
    func: listContractWarning,
    params: {},
    morePathName: 'ContractWarning',
    pathName: 'ContractAddOrEdit',
    getStr: (item) => {
      return `${item.name || item.staffName}的合同即将到期，请尽快处理!`;
    },
    getQuery: (item) => {
      return { id: item.id, view: true };
    },
  },
  salary: {
    title: '薪酬预警',
    func: listProjectOrgSalaryWarn,
    params: {},
    morePathName: 'ProjectOrgSalaryWarning',
    pathName: 'projectOrgSalaryDetails',
    getStr: (item) => {
      return `${item.leaderUserName}负责的${item.projectName}项目${item.yearMonth}的工资支付书未上传，请尽快处理!`;
    },
    getQuery: (item) => {
      return { id: item.id };
    },
  },
  certificate: {
    aliasName: '证书预警',
    title: '特种作业证书预警',
    func: listSpecialWarning,
    params: {},
    morePathName: 'CertificateWarning',
    pathName: 'CertificateSpecialForm',
    getStr: (item) => {
      return `${item.name || item.staffName}证书即将到期，请尽快处理!`;
    },
    getQuery: (item) => {
      return { id: item.id, view: true };
    },
  },
  register: {
    aliasName: '证书预警',
    title: '注册岗位证书预警',
    func: listEnrollWarning,
    params: {},
    morePathName: 'CertificateWarning',
    pathName: 'CertificateEnrollForm',
    getStr: (item) => {
      return `${item.name || item.staffName}证书即将到期，请尽快处理!`;
    },
    getQuery: (item) => {
      return { id: item.id, view: true };
    },
  },
};

// 查看通知详情
const handleViewNotice = (item) => {
  currentNotice.value = item;
  noticeDialogVisible.value = true;
  if (item.status !== '2') {
    readFlowNotify(item.todoId).then(() => {
      item.status = '2';
    });
  }
};

// 获取待办数据
function getTodoData() {
  listMyPendingProcess().then((res) => {
    todoList.value = res.rows;
  });
}

// 获取通知消息数据
function getNoticeData() {
  listFlowNotify({
    todoType: 2,
    status: 1,
    userId: useUserStore().id,
  }).then((res) => {
    noticeList.value = res.rows;
  });
}

// 获取预警数据
function getWarningData() {
  warningLoading.value = true;
  warningList.value = [];

  const currentWarningData = warningData[activeWarningType.value];
  currentWarningData
    .func(currentWarningData.params)
    .then((res) => {
      warningList.value = res.rows.map((item) => {
        return {
          title: currentWarningData.getStr(item),
          time: item.createTime,
          tagLabel:
            currentWarningData.getTitle?.(item) ||
            currentWarningData.aliasName ||
            currentWarningData.title,
          tagType: currentWarningData.getStatusColor?.(item) || 'danger',
          raw: item,
        };
      });
    })
    .finally(() => {
      warningLoading.value = false;
    });
}

// 处理预警类型切换
const handleChangeWarningType = (type) => {
  if (warningLoading.value) return;
  activeWarningType.value = type;
  getWarningData();
};

// 查看预警详情
const handleViewWarning = (item) => {
  const currentWarningData = warningData[activeWarningType.value];
  let query = {};
  if (currentWarningData?.getQuery) {
    query = currentWarningData?.getQuery(item.raw) || {};
  }

  router.push({
    name: currentWarningData.pathName,
    query: query,
  });
};

// 获取待办数据
function getBacklogDataF() {
  getBacklogData().then((res) => {
    backlogData.value = res.data;
  });
}
// 获取人员动态、劳资信息、培训情况
function getQueryViewF(params = {}) {
  getQueryView(params).then((res) => {
    const { data } = res;
    // 人员动态数据
    const personnelStats = statisticCards.value[0].stats;
    personnelStats[0].value = data.yggm || 0; // 员工规模
    personnelStats[1].value = data.zzry || 0; // 在职人员
    personnelStats[2].value = data.fyry || 0; // 富余人员
    personnelStats[3].value = data.qtry || 0; // 其他人员

    // 劳资信息数据
    const salaryStats = statisticCards.value[1].stats;
    salaryStats[0].value = data.gztqcs || 0; // 工资拖欠次数
    salaryStats[1].value = data.gztqze || 0; // 工资总额

    // 培训情况数据
    const trainingStats = statisticCards.value[2].stats;
    trainingStats[0].value = data.wpcs || 0; // 委培
    trainingStats[1].value = data.zpcs || 0; // 自培
  });
}

// 点击卡片
function handleClickCard(cardData) {
  if (cardData.route) {
    cardData.route();
    return;
  }
  router.push({
    name: 'ProcessAudit',
    query: {
      templateId: cardData.id,
    },
  });
}

onMounted(() => {
  getTodoData();
  getNoticeData();
  getWarningData();
  getBacklogDataF();
  getQueryViewF();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: #f0f1f3;
}

.main-row {
  margin: 0 -8px;
  flex: 1;
}

// 通知详情样式
.notify-detail-item {
  margin-bottom: 15px;
  font-size: 14px;
  display: flex;
}

.notify-detail-item .label {
  font-weight: bold;
  width: 100px;
}

.notify-detail-content {
  margin-top: 20px;
}

.notify-detail-content .content-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.notify-detail-content .content-body {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  min-height: 100px;
}
</style>
