<template>
  <div class="personnel-info-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="年月" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          placeholder="请选择年月"
          value-format="YYYY-MM"
          class="w-[200px]"
          clearable
        />
      </el-form-item>
      <el-form-item label="建设状态" prop="constructionStatus">
        <el-select
          v-model="queryParams.constructionStatus"
          placeholder="请选择建设状态"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in construction_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="机构属性" prop="orgAttribute">
        <el-select
          v-model="queryParams.orgAttribute"
          placeholder="请选择机构属性"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in organization_attribute"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员状态" prop="positionStatus">
        <el-select
          v-model="queryParams.positionStatus"
          placeholder="请选择人员状态"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in position_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery" class="reset-btn">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计数据展示 -->
    <div class="statistics-container">
      <div class="statistics-grid">
        <div class="stat-item">
          <div class="stat-title">项目数量</div>
          <div class="stat-content">
            <span class="stat-value">{{ statisticsData.projectNumber }}</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-title">总人数</div>
          <div class="stat-content">
            <span class="stat-value">{{ statisticsData.staffNumber }}</span>
            <span class="stat-label">人</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-title">职工</div>
          <div class="stat-content">
            <span class="stat-value">{{ statisticsData.zgNumber }}</span>
            <span class="stat-label">人</span>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-title">天诚</div>
          <div class="stat-content">
            <span class="stat-value">{{ statisticsData.tcjyNumber }}</span>
            <span class="stat-label">人</span>
          </div>
        </div>
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" class="custom-btn" @click="handleExport">
          导出人员信息
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="personnelList" border>
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          <span>{{ getIndex(scope.$index) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="所在单位"
        align="center"
        prop="projectName"
        min-width="120"
      />
      <el-table-column label="姓名" align="center" prop="name" width="100" />
      <el-table-column label="性别" align="center" prop="gender" width="80">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column
        label="二级机构"
        align="center"
        prop="deptName"
        min-width="120"
      />
      <el-table-column
        label="专业技术职务"
        align="center"
        prop="professionalTitle"
        min-width="130"
      >
        <template #default="scope">
          <dict-tag
            :options="professional_title"
            :value="scope.row.professionalTitle"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="岗位"
        align="center"
        prop="postName"
        min-width="100"
      />
      <el-table-column
        label="从事工作"
        align="center"
        prop="jobContent"
        min-width="100"
      >
        <template #default="scope">
          <dict-tag :options="work_type" :value="scope.row.jobContent" />
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center" prop="age" width="80" />
      <el-table-column
        label="政治面貌"
        align="center"
        prop="politicalStatus"
        min-width="100"
      >
        <template #default="scope">
          <dict-tag
            :options="political_status"
            :value="scope.row.politicalStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="人员类型"
        align="center"
        prop="staffType"
        min-width="100"
      >
        <template #default="scope">
          <dict-tag :options="personnel_type" :value="scope.row.staffType" />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import {
  listPersonnelInfo,
  getPersonnelStatistics,
} from '@/api/instrument/dynamicPanel';

const { proxy } = getCurrentInstance();

const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const personnelList = ref([]);

const {
  sys_user_sex,
  construction_status,
  organization_attribute,
  position_status,
  personnel_type,
  professional_title,
  work_type,
  political_status,
} = proxy.useDict(
  'sys_user_sex',
  'construction_status',
  'organization_attribute',
  'position_status',
  'personnel_type',
  'professional_title',
  'work_type',
  'political_status'
);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  yearMonth: undefined,
  staffType: 0,
  constructionStatus: undefined,
  orgAttribute: undefined,
  positionStatus: undefined,
});

const statisticsData = ref({
  projectNumber: 0,
  staffNumber: 0,
  zgNumber: 0,
  tcjyNumber: 0,
});

function getList() {
  loading.value = true;
  listPersonnelInfo(queryParams.value).then((response) => {
    personnelList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function getStatistics() {
  getPersonnelStatistics(queryParams.value).then((response) => {
    statisticsData.value = response.data;
  });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
  getStatistics();
}

function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

function handleExport() {
  proxy.download(
    '/dynamics/export',
    { ...queryParams.value },
    `人员信息_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
  getStatistics();
});
</script>

<style scoped>
.personnel-info-container {
  padding: 0;
}

.statistics-container {
  padding: 16px 0;
}

.statistics-grid {
  display: flex;
  gap: 12px;
}

.stat-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  flex: 1;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.stat-content {
  font-size: 12px;
  color: #333;
}

.stat-value {
  font-weight: 700;
  color: #2f7bff;
  font-size: 20px;
  margin-right: 4px;
}

.stat-label {
  color: #999;
  font-size: 12px;
}
</style>
