<template>
  <div class="bg-[#f3f7fc]">
    <el-card class="custom-card">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="项目" prop="projectId">
          <RemoteSelect
            class="w-[200px]"
            v-model="queryParams.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择项目"
          ></RemoteSelect>
        </el-form-item>
        <el-form-item label="姓名" prop="staffName">
          <el-input
            class="w-[200px]"
            v-model="queryParams.staffName"
            placeholder="请输入姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="岗位" prop="postId">
          <RemoteSelect
            v-model="queryParams.postId"
            url="/system/post/list"
            labelKey="postName"
            valueKey="postId"
            placeholder="请选择岗位"
            class="w-[200px]"
          ></RemoteSelect>
        </el-form-item>
        <el-form-item label="人员类型" prop="staffType">
          <el-select
            class="w-[200px]"
            v-model="queryParams.staffType"
            placeholder="请选择人员类型"
            clearable
          >
            <el-option
              v-for="dict in personnel_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作合格证编号" prop="certificateCode">
          <el-input
            class="w-[200px]"
            v-model="queryParams.certificateCode"
            placeholder="请输入操作合格证编号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <!-- 移除证书状态 -->
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery" class="reset-btn">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <el-row :gutter="10" class="mb8">
        <!-- 只保留导出按钮 -->
        <el-col :span="1.5">
          <el-button class="custom-btn" @click="handleExport">导出</el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="certificateList">
        <el-table-column type="index" label="序号" width="50" align="center">
          <template #default="scope">
            <span>{{ getIndex(scope.$index) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" align="center" prop="projectName" />
        <el-table-column label="姓名" align="center" prop="staffName" />
        <el-table-column label="性别" align="center" prop="gender">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="人员类型" align="center" prop="staffType">
          <template #default="scope">
            <dict-tag :options="personnel_type" :value="scope.row.staffType" />
          </template>
        </el-table-column>
        <el-table-column label="岗位" align="center" prop="postName" />
        <el-table-column label="年龄" align="center" prop="age" />
        <el-table-column
          label="身份证号码"
          align="center"
          prop="idNumber"
          width="160"
        />
        <el-table-column label="工作年限" align="center" prop="workYears" />
        <el-table-column
          label="操作合格证编号"
          width="160"
          align="center"
          prop="certificateCode"
        />
        <el-table-column label="工种" align="center" prop="workType">
          <template #default="scope">
            <dict-tag
              :options="identify_job_types"
              :value="scope.row.workType"
            />
          </template>
        </el-table-column>
        <el-table-column label="发证机关" align="center" prop="issueOrgan" />
        <el-table-column
          label="证书有效期"
          align="center"
          prop="effectiveDate"
          width="150"
        >
          <template #default="scope">
            <span>{{ scope.row.effectiveDate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="进场时间" align="center" prop="approachDate">
          <template #default="scope">
            <span>{{ scope.row.approachDate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="SpecialOperationCertificateLedger">
// 修改组件名称
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { parseTime } from '@/utils/welink';
import {
  listExitSpecialCertificate,
  delSpecialCertificateDetail,
} from '@/api/certificate/special';
const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const {
  sys_user_sex,
  personnel_type,
  certificate_category,
  identify_job_types, // 虽然表单移除了，但列表可能还需要显示
} = proxy.useDict(
  'sys_user_sex',
  'personnel_type',
  'certificate_category',
  'identify_job_types'
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 证书表格数据
const certificateList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectName: undefined,
  staffName: undefined,
  postName: undefined,
  staffType: undefined,
  certificateCategory: undefined,
  certificateCode: undefined,
});

/** 查询特种作业证书列表 */
function getList() {
  loading.value = true;

  listExitSpecialCertificate(queryParams.value)
    .then((response) => {
      certificateList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 查看按钮操作 */
function handleView(row) {
  // 查看"退出人员特种作业证书"的详情页
  router.push({
    path: '/certificate/certificateSpecial/form',
    query: { id: row.id, view: true },
    meta: { activeMenu: location.pathname },
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(`确认要删除"${row.staffName}"的特种作业证书信息吗？`)
    .then(function () {
      return delSpecialCertificateDetail(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  // 注意：导出接口和参数需要修改
  proxy.download(
    'railway/wlCertificateSpecial/retiree/export', // 假设的导出API路径
    {
      ...queryParams.value,
    },
    `退出人员特种作业证书信息_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
<style scoped>
.custom-card {
  border: unset;
  background-color: #fff;
  box-shadow: unset;
  border-radius: 0 0 10px 10px;
}
</style>
