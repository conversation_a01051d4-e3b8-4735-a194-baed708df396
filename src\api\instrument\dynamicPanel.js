import request from "@/utils/request";

// 汇总信息相关接口
export function listSummaryInfo(query) {
  return request({
    url: "/dynamics/getSummary",
    method: "get",
    params: query,
  });
}

// 汇总信息统计
export function getStatisticsData(query) {
  return request({
    url: "/dynamics/getCompare",
    method: "get",
    params: query,
  });
}

// 人员信息相关接口
export function listPersonnelInfo(query) {
  return request({
    url: "/dynamics/infoByType",
    method: "get",
    params: query,
  });
}

export function getPersonnelStatistics(query) {
  return request({
    url: "/dynamics/infoNumbere",
    method: "get",
    params: query,
  });
}
// 人员变动趋势
export function getTrend(query) {
  return request({
    url: "/dynamics/getTrend",
    method: "get",
    params: query,
  });
}
// 获取离职统计
export function getTurnover(query) {
  return request({
    url: "/dynamics/getTurnover",
    method: "get",
    params: query,
  });
}
// 获取入职机构排名
export function getOrgRankingList(query) {
  return request({
    url: "/dynamics/getOrgRank",
    method: "get",
    params: query,
  });
}
