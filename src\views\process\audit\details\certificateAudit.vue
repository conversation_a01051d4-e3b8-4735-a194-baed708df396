<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :inline="true"
          :disabled="disabled"
        >
          <div class="w-full">
            <p class="relative font-bold">人员信息</p>
          </div>

          <el-form-item label="员工姓名" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="form.staffName"
              placeholder="请输入员工姓名"
              disabled
            />
          </el-form-item>

          <el-form-item label="性别" prop="gender">
            <el-select
              v-model="form.gender"
              placeholder="请选择性别"
              class="w-[200px]"
              disabled
            >
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="人员类型" prop="staffType">
            <el-select
              v-model="form.staffType"
              placeholder="请选择人员类型"
              class="w-[200px]"
              disabled
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="所属单位" prop="unit">
            <el-input
              v-model="form.unit"
              placeholder="请输入所属单位"
              class="w-[200px]"
              disabled
            />
          </el-form-item>

          <el-form-item label="身份证号" prop="idNumber">
            <el-input
              class="w-[200px]"
              v-model="form.idNumber"
              placeholder="请输入身份证号"
              disabled
            />
          </el-form-item>

          <el-form-item label="工作年限" prop="workYears">
            <el-input-number
              :controls="false"
              v-model="form.workYears"
              :min="0"
              :max="100"
              placeholder="请输入工作年限"
              class="w-[200px]"
              disabled
            />
          </el-form-item>
          <el-form-item label="年龄" prop="age">
            <el-input-number
              :controls="false"
              v-model="form.age"
              :min="0"
              :max="100"
              placeholder="请输入年龄"
              class="w-[200px]"
              disabled
            />
          </el-form-item>
          <div class="w-full">
            <p class="relative font-bold">证书信息</p>
          </div>
          <el-form-item label="操作合格证编号" prop="certificateCode">
            <el-input
              v-model="form.certificateCode"
              class="w-[200px]"
              placeholder="请输入操作合格证编号"
            />
          </el-form-item>

          <el-form-item label="技能鉴定工种" prop="workType">
            <el-select
              v-model="form.workType"
              placeholder="请选择技能鉴定工种"
              class="w-[200px]"
            >
              <el-option
                v-for="dict in identify_job_types"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="发证机关" prop="issueOrgan">
            <el-input
              class="w-[200px]"
              v-model="form.issueOrgan"
              placeholder="请输入发证机关"
            />
          </el-form-item>

          <el-form-item label="证书有效期" prop="effectiveDate">
            <el-date-picker
              v-model="form.effectiveDate"
              type="date"
              placeholder="选择证书有效期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-[200px]"
            />
          </el-form-item>

          <el-form-item label="进场时间" prop="approachDate">
            <el-date-picker
              v-model="form.approachDate"
              type="date"
              placeholder="选择进场时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
          <div class="w-full">
            <el-form-item
              label="证书扫描件"
              prop="wlAnnexes"
              class="w-full flex items-center"
            >
              <attachment-display :attachments="form.wlAnnexes" />
            </el-form-item>
          </div>
          <div class="w-full">
            <el-form-item label="备注" prop="remark" class="w-full">
              <el-input
                type="textarea"
                rows="4"
                v-model="form.remark"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </div>
        </el-form>

        <!-- 审核组件 -->
        <ProcessComponent :nodeInfo="nodeInfo" class="mt-4" />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  getSpecialCertificateDetail,
  addSpecialCertificateDetail,
  updateSpecialCertificateDetail,
} from "@/api/certificate/special";
import ProcessComponent from "@/components/ProcessComponent";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
import { getBusinessIdByInstanceId } from "@/api/process/audit";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 节点信息
const nodeInfo = ref({});
// 加载状态
const loading = ref(false);

// 字典数据
const { sys_user_sex, personnel_type, audit_status, identify_job_types } =
  proxy.useDict(
    "sys_user_sex",
    "personnel_type",
    "audit_status",
    "identify_job_types"
  );

// 表单参数
const formRef = ref();
const form = ref({
  id: undefined,
  staffName: undefined,
  staffId: undefined,
  gender: undefined,
  age: undefined,
  idNumber: undefined,
  workYears: undefined,
  staffType: undefined,
  certificateCode: undefined,
  workType: undefined,
  issueOrgan: undefined,
  effectiveDate: undefined,
  approachDate: undefined,
  projectId: undefined,
  projectName: undefined,
  status: "0", // 默认为未审核状态
  staffStatus: "0", // 默认为在职状态
  unit: undefined,
  wlAnnexes: [],
  remark: undefined,
});

// 是否禁用表单
const disabled = ref(false);

// 表单校验规则
const rules = ref({
  staffName: [{ required: true, message: "员工姓名不能为空", trigger: "blur" }],
  gender: [{ required: true, message: "性别不能为空", trigger: "change" }],
  staffType: [
    { required: true, message: "人员类型不能为空", trigger: "change" },
  ],
  age: [{ required: true, message: "年龄不能为空", trigger: "blur" }],
  idNumber: [
    { required: true, message: "身份证号不能为空", trigger: "blur" },
    {
      pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
      message: "请输入正确的身份证号码",
      trigger: "blur",
    },
  ],
  certificateCode: [
    { required: true, message: "操作合格证编号不能为空", trigger: "blur" },
  ],
  workType: [
    { required: true, message: "技能鉴定工种不能为空", trigger: "change" },
  ],
  issueOrgan: [
    { required: true, message: "发证机关不能为空", trigger: "blur" },
  ],
  effectiveDate: [
    { required: true, message: "证书有效期不能为空", trigger: "blur" },
  ],
  approachDate: [
    { required: true, message: "进场时间不能为空", trigger: "blur" },
  ],
  projectId: [
    { required: true, message: "所在单位不能为空", trigger: "change" },
  ],
  unit: [{ required: true, message: "所属单位不能为空", trigger: "blur" }],
  workYears: [{ required: true, message: "工作年限不能为空", trigger: "blur" }],
  wlAnnexes: [
    { required: true, message: "证书扫描件不能为空", trigger: "change" },
  ],
  remark: [{ required: true, message: "备注不能为空", trigger: "blur" }],
});

/** 取消按钮 */
function cancel() {
  router.go(-1);
}
// 人员选择
function handleUserChange(val) {
  form.value.staffName = val.name;
  form.value.gender = val.gender;
  form.value.staffType = val.staffType;
  form.value.unit = val.projectName;
  form.value.idNumber = val.idNumber;
  form.value.age = val.age;
  form.value.workYears = val.workYears;
}
/** 表单提交 */
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      const data = {
        ...form.value,
        projectId: route.query.projectId,
      };
      if (data.id) {
        updateSpecialCertificateDetail(data)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            loading.value = false;
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        addSpecialCertificateDetail(data)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            loading.value = false;
            cancel();
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}

/** 初始化表单数据 */
async function initFormData() {
  const viewMode = route.query.view === "true";
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  if (instanceId) {
    const res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
    nodeInfo.value = res.data;
  }
  if (id) {
    // 查看或编辑模式
    disabled.value = viewMode;
    getSpecialCertificateDetail(id).then((response) => {
      Object.assign(form.value, response.data);
    });
  } else {
    // 新增模式
    disabled.value = false;
  }
}

onMounted(() => {
  initFormData();
});
</script>

<style scoped>
.w-full {
  width: 100%;
}
div /deep/ .el-input-number .el-input__inner {
  text-align: left !important;
}
.form-footer {
  margin-top: 16px;
}
.mt-4 {
  margin-top: 16px;
}
</style>
