<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @downloadTemplate="handleDownloadTemplate"
    />
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="所在单位" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择单位"
              clearable
              class="w-[200px]"
              @change="handleQueryProjectChange"
            />
          </el-form-item>
          <el-form-item label="所在部门" prop="deptId">
            <RemoteSelect
              v-model="queryParams.deptId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              placeholder="请选择部门"
              clearable
              class="w-[200px]"
              :extraParams="{ parentId: 9999 }"
            />
          </el-form-item>
          <el-form-item label="岗位" prop="postName">
            <el-input
              v-model="queryParams.postName"
              placeholder="请输入岗位"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="请假类型" prop="leaveType">
            <el-select
              v-model="queryParams.leaveType"
              placeholder="请选择请假类型"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in leave_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="beginTime">
            <el-date-picker
              v-model="queryParams.beginTime"
              type="datetime"
              placeholder="请选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="queryParams.endTime"
              type="datetime"
              placeholder="请选择结束时间"
              clearable
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="leaveList">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          />
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="所在单位" align="center" prop="projectName" />
          <el-table-column label="岗位名称" align="center" prop="postName" />
          <el-table-column label="请假类型" align="center" prop="leaveType">
            <template #default="scope">
              <dict-tag :options="leave_type" :value="scope.row.leaveType" />
            </template>
          </el-table-column>
          <el-table-column
            label="开始时间"
            align="center"
            prop="beginTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.beginTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="结束时间"
            align="center"
            prop="endTime"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.endTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="请假时长"
            align="center"
            prop="duration"
            width="130"
          />

          <el-table-column label="附件" align="center" prop="wlAnnexes">
            <template #default="scope">
              <attachment-display :attachments="scope.row.wlAnnexes" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="200"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或修改假单对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="leaveFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20" v-if="!disabled">
          <el-col :span="24">
            <el-form-item label="姓名" prop="name">
              <div class="flex items-center w-full">
                <el-input
                  v-model="form.name"
                  placeholder="请选择人员"
                  class="mr-2 w-[70%]"
                  disabled
                />
                <user-select
                  v-model="form.staffId"
                  @change="handleStaffChange"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单位" prop="projectName">
              <el-input v-model="form.projectName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门">
              <el-input v-model="form.deptName" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位" prop="postName">
              <el-input v-model="form.postName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请假类型" prop="leaveType">
              <el-select
                :disabled="disabled"
                v-model="form.leaveType"
                placeholder="请选择请假类型"
                class="w-full"
              >
                <el-option
                  v-for="dict in leave_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker
                :disabled="disabled"
                v-model="form.beginTime"
                type="date"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                :disabled="disabled"
                v-model="form.endTime"
                type="date"
                placeholder="请选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请假原因" prop="remark">
              <el-input
                :disabled="disabled"
                v-model="form.remark"
                type="textarea"
                placeholder="请输入请假原因"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="附件"
              prop="wlAnnexes"
              class="flex items-center"
            >
              <file-upload
                v-if="!disabled"
                :disabled="disabled"
                v-model="form.wlAnnexes"
                :limit="10"
                :fileType="['pdf', 'docx', 'doc']"
                :fileSize="60"
              />
              <attachment-display v-else :attachments="form.wlAnnexes" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" class="cancel-btn">取 消</el-button>
          <el-button v-if="!disabled" type="primary" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { parseTime } from '@/utils/welink';
import {
  listLeave,
  getLeave,
  addLeave,
  updateLeave,
  delLeave,
} from '@/api/salary/leave';
import RemoteSelect from '@/components/RemoteSelect';
import FileUpload from '@/components/FileUpload';
import { calculateDuration } from '@/utils/welink';
import AttachmentDisplay from '@/components/AttachmentDisplay/index.vue';
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 假单表格数据
const leaveList = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入假单数据');
const importUrl = '/railway/wlLeave/import';
const templateUrl = '/railway/wlLeave/importTemplate';
const disabled = ref(false);
// 字典数据
const { leave_type } = proxy.useDict('leave_type');

// 查询表单部门参数
const queryOrgParams = ref({});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  projectId: undefined,
  deptId: undefined,
  postName: undefined,
  leaveType: undefined,
  beginTime: undefined,
  endTime: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  name: undefined,
  projectId: undefined,
  deptId: undefined,
  postId: undefined,
  postName: undefined,
  leaveType: undefined,
  beginTime: undefined,
  endTime: undefined,
  remark: undefined,
  wlAnnexes: [],
});

// 表单校验规则
const rules = ref({
  wlAnnexes: [{ required: true, message: '请上传附件', trigger: 'blur' }],
  name: [{ required: true, message: '请选择人员', trigger: 'blur' }],
  projectId: [{ required: true, message: '请选择单位', trigger: 'change' }],
  deptId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  postId: [{ required: true, message: '请选择岗位', trigger: 'change' }],
  leaveType: [{ required: true, message: '请选择请假类型', trigger: 'change' }],
  beginTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' },
  ],
  remark: [{ required: true, message: '请输入请假原因', trigger: 'blur' }],
});

// 验证结束时间必须大于开始时间
function validateEndTime(rule, value, callback) {
  if (form.value.beginTime && value) {
    const beginTime = new Date(form.value.beginTime).getTime();
    const endTime = new Date(value).getTime();
    if (endTime <= beginTime) {
      callback(new Error('结束时间必须大于开始时间'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}

const leaveFormRef = ref();

/** 查询假单列表 */
function getList() {
  loading.value = true;
  listLeave(queryParams.value).then((response) => {
    response.rows.map((item) => {
      item.duration = calculateDuration(item.beginTime, item.endTime);
    });
    leaveList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    name: undefined,
    projectId: undefined,
    deptId: undefined,
    postId: undefined,
    postName: undefined,
    leaveType: undefined,
    beginTime: undefined,
    endTime: undefined,
    remark: undefined,
    wlAnnexes: [],
  };

  proxy.resetForm('leaveFormRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 查询表单项目变更操作 */
function handleQueryProjectChange(project) {
  if (project && project.deptId) {
    queryParams.value.deptId = undefined; // 重置部门ID
    queryOrgParams.value = { parentId: project.deptId }; // 更新部门查询参数
  } else {
    queryParams.value.projectId = undefined;
    queryParams.value.deptId = undefined;
    queryOrgParams.value = {};
  }
}

/** 重置按钮操作 */
function resetQuery() {
  queryOrgParams.value = {}; // 重置部门查询参数
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  disabled.value = false;
  open.value = true;
  title.value = '添加假单';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  disabled.value = false;
  getLeave(id).then((response) => {
    Object.assign(form.value, response.data);
    open.value = true;
    title.value = '修改假单';
  });
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  const id = row.id;
  disabled.value = true;
  getLeave(id).then((response) => {
    Object.assign(form.value, response.data);
    open.value = true;
    title.value = '查看假单';
  });
}

/** 提交按钮 */
function submitForm() {
  leaveFormRef.value.validate((valid) => {
    if (valid) {
      if (form.value.id) {
        updateLeave(form.value).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        });
      } else {
        addLeave(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除该假单？').then(() => {
    delLeave(row.id).then(() => {
      proxy.$modal.msgSuccess('删除成功');
      getList();
    });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    '/railway/wlLeave/export',
    {
      ...queryParams.value,
    },
    `假单数据_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true;
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  getList();
}

/** 下载附件 */
function handleDownload(annex) {
  proxy.download(annex.url);
}

/** 项目单位变更操作 */
function handleProjectChange(project) {
  if (project && project.id) {
    form.value.deptId = undefined; // 重置部门ID
  } else {
    form.value.projectId = undefined;
    form.value.deptId = undefined;
  }
}

/** 人员选择变更操作 */
function handleStaffChange(staff) {
  form.value.postName = staff.postName;
  form.value.deptName = staff.deptName;
  form.value.projectName = staff.projectName;
  form.value.name = staff.name;
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.download-link {
  color: #409eff;
  cursor: pointer;
  margin-right: 8px;
  text-decoration: underline;
}
</style>
