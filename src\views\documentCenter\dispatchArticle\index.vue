<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="发文抄录类型" prop="type">
            <el-select
              v-model="queryParams.type"
              placeholder="发文抄录类型"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in dispatch_article_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="审核状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in audit_status.filter(
                  (item) => item.label !== '拟稿'
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="queryParams.title"
              placeholder="请输入标题"
              clearable
              class="w-[200px]"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="dispatchArticleList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发文抄录标题" align="center" prop="title" />
          <el-table-column label="发文抄录类型" align="center" prop="type">
            <template #default="scope">
              <dict-tag
                :options="dispatch_article_type"
                :value="scope.row.type"
              />
            </template>
          </el-table-column>
          <el-table-column label="文件号" align="center" prop="code" />
          <el-table-column label="发文抄录状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="audit_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="180"
          />
          <el-table-column
            label="操作"
            align="center"
            width="100"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import { listDispatchArticle } from "@/api/documentCenter/dispatchArticle";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const { dispatch_article_type, audit_status } = proxy.useDict(
  "dispatch_article_type",
  "audit_status"
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 发文抄录表格数据
const dispatchArticleList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  title: undefined,
  type: undefined,
  status: undefined,
});

/** 查询发文抄录列表 */
function getList() {
  loading.value = true;
  listDispatchArticle(queryParams.value).then((response) => {
    dispatchArticleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  router.push({
    path: "/documentCenter/dispatchArticle/addOrEdit",
    query: { mode: "add" },
  });
}

/** 查看按钮操作 */
function handleView(row) {
  proxy.getProcessRouterPath(row, "10007", row.id);
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "/railway/wlDispatchArticle/export",
    {
      ...queryParams.value,
    },
    `发文抄录_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
