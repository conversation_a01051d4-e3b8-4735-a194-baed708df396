<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">{{ title }}</span>
          </div>
        </template>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
          <el-divider content-position="left">项目基本信息</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目" prop="projectId">
                <RemoteSelect
                  v-model="form.projectId"
                  url="/system/dept/list"
                  labelKey="deptName"
                  valueKey="deptId"
                  responsePath="data"
                  placeholder="请选择项目"
                  :disabled="type === 'check'"
                  @change="handleProjectChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目负责人" prop="leaderUserName">
                <el-input
                  v-model="form.leaderUserName"
                  placeholder="请输入项目负责人"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="办公室负责人" prop="officeLeader">
                <el-input
                  v-model="form.officeLeader"
                  placeholder="请输入办公室负责人"
                  :disabled="type === 'check'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="phone">
                <el-input
                  v-model="form.phone"
                  placeholder="请输入联系方式"
                  :disabled="type === 'check'"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">项目人员需求详细信息</el-divider>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="mb-4 flex justify-end" v-if="type !== 'check'">
                <el-button type="primary" @click="handleAddUser"
                  >新增需求</el-button
                >
              </div>
              <el-table :data="form.staffList" border>
                <el-table-column
                  label="序号"
                  type="index"
                  width="80"
                  align="center"
                />
                <el-table-column
                  label="需求岗位"
                  align="center"
                  prop="postName"
                >
                </el-table-column>
                <el-table-column
                  label="岗位工资"
                  align="center"
                  prop="postSalary"
                />
                <el-table-column
                  label="需求人数"
                  align="center"
                  prop="peopleNumber"
                />
                <el-table-column label="性别" align="center" prop="postSex">
                  <template #default="scope">
                    <dict-tag
                      :options="sys_user_sex"
                      :value="scope.row.postSex"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="最迟进场时间"
                  align="center"
                  prop="entryTime"
                />
                <el-table-column
                  label="职业性格"
                  align="center"
                  prop="careerCharacter"
                >
                  <template #default="scope">
                    <dict-tag
                      :options="professional_personality"
                      :value="scope.row.careerCharacter"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="个人性格"
                  align="center"
                  prop="personCharacter"
                >
                  <template #default="scope">
                    <dict-tag
                      :options="personality_type"
                      :value="scope.row.personCharacter"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="业务能力评价"
                  align="center"
                  prop="abilityEvaluate"
                >
                  <template #default="scope">
                    <dict-tag
                      :options="business_ability_evaluation"
                      :value="scope.row.abilityEvaluate"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="工作状态评价"
                  align="center"
                  prop="statusEvaluate"
                >
                  <template #default="scope">
                    <dict-tag
                      :options="work_status_evaluation"
                      :value="scope.row.statusEvaluate"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="需求原因简述"
                  align="center"
                  prop="resume"
                />
                <el-table-column label="备注" align="center" prop="remark">
                  <template #default="scope">
                    {{ scope.row.remark || "--" }}
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                  width="100"
                  v-if="type !== 'check'"
                >
                  <template #default="scope">
                    <el-button
                      type="text"
                      @click="handleDeleteUser(scope.$index)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="mt-4">
            <el-col :span="24">
              <div>
                <el-button
                  type="primary"
                  @click="submitForm"
                  v-if="type !== 'check'"
                  :loading="loading"
                  >提交</el-button
                >
              </div>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 添加需求对话框 -->
    <el-dialog
      :title="userDialogTitle"
      v-model="userDialogVisible"
      width="700px"
      append-to-body
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="120px"
      >
        <!-- 第一行：需求岗位、需求人数 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="需求岗位" prop="postId">
              <RemoteSelect
                v-model="userForm.postId"
                url="/system/post/list"
                labelKey="postName"
                valueKey="postId"
                placeholder="请选择岗位"
                class="w-[200px]"
                @change="handlePostChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求人数" prop="peopleNumber">
              <el-input-number
                :controls="false"
                v-model="userForm.peopleNumber"
                :min="1"
                :precision="0"
                class="w-full"
                placeholder="请输入需求人数"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：岗位类型、岗位工资 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位类型" prop="salaryType">
              <el-select
                v-model="userForm.salaryType"
                placeholder="请选择岗位类型"
                class="w-full"
                @change="handleSalaryTypeChange"
              >
                <el-option label="自定义" :value="0" />
                <el-option label="岗位等级制" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="岗位工资"
              :prop="!userForm.salaryType && 'postSalary'"
            >
              <el-input-number
                :controls="false"
                v-model="userForm.postSalary"
                :step="100"
                :min="0"
                class="w-full"
                placeholder="请输入岗位工资"
                :disabled="userForm.salaryType === 1"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：性别、最迟进场时间 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="postSex">
              <el-select
                v-model="userForm.postSex"
                placeholder="请选择性别"
                class="w-full"
              >
                <el-option
                  v-for="dict in sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最迟进场时间" prop="entryTime">
              <el-date-picker
                v-model="userForm.entryTime"
                type="date"
                placeholder="请选择最迟进场时间"
                class="w-full"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：个人性格、职业性格 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="个人性格" prop="personCharacter">
              <el-select
                v-model="userForm.personCharacter"
                placeholder="请选择个人性格"
                class="w-full"
              >
                <el-option
                  v-for="dict in personality_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职业性格" prop="careerCharacter">
              <el-select
                v-model="userForm.careerCharacter"
                placeholder="请选择职业性格"
                class="w-full"
              >
                <el-option
                  v-for="dict in professional_personality"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第五行：业务能力评价、工作状态评价 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务能力评价" prop="abilityEvaluate">
              <el-select
                v-model="userForm.abilityEvaluate"
                placeholder="请选择业务能力评价"
                class="w-full"
              >
                <el-option
                  v-for="dict in business_ability_evaluation"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作状态评价" prop="statusEvaluate">
              <el-select
                v-model="userForm.statusEvaluate"
                placeholder="请选择工作状态评价"
                class="w-full"
              >
                <el-option
                  v-for="dict in work_status_evaluation"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第六行：需求原因简述 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="需求原因简述">
              <el-input
                v-model="userForm.resume"
                placeholder="请输入需求原因"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="userForm.remark"
                placeholder="请输入备注"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitUserForm" class="confirm-btn"
            >确 定</el-button
          >
          <el-button @click="userDialogVisible = false" class="cancel-btn"
            >取 消</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, reactive, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import RemoteSelect from "@/components/RemoteSelect";
import {
  getProjectStaffApply,
  addProjectStaffApply,
  updateProjectStaffApply,
} from "@/api/personnelFiles/projectStaffApply";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 字典数据
const {
  sys_user_sex,
  professional_personality,
  personality_type,
  business_ability_evaluation,
  work_status_evaluation,
} = proxy.useDict(
  "sys_user_sex",
  "professional_personality",
  "personality_type",
  "business_ability_evaluation",
  "work_status_evaluation"
);

// 标题
const title = ref("");
// 类型（新增/编辑/查看）
const type = ref("");
// 是否提交中
const loading = ref(false);
// 岗位列表
const postList = ref([]);

// 表单参数
const formRef = ref(null);
const form = ref({
  id: undefined,
  projectId: undefined,
  projectName: undefined,
  leaderUserName: undefined,
  officeLeader: undefined,
  phone: undefined,
  status: "0", // 默认草稿状态
  staffList: [],
});

// 表单校验
const rules = ref({
  projectId: [{ required: true, message: "项目不能为空", trigger: "change" }],
  leaderUserName: [
    { required: true, message: "项目负责人不能为空", trigger: "blur" },
  ],
  officeLeader: [
    { required: true, message: "办公室负责人不能为空", trigger: "blur" },
  ],
  phone: [
    { required: true, message: "联系方式不能为空", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
});

// 用户弹窗参数
const userDialogTitle = ref("新增需求");
const userDialogVisible = ref(false);
const userFormRef = ref(null);
const userForm = ref({
  postId: undefined,
  postName: undefined,
  postSalary: undefined,
  peopleNumber: 1,
  postSex: undefined,
  entryTime: undefined,
  careerCharacter: undefined,
  personCharacter: undefined,
  abilityEvaluate: undefined,
  statusEvaluate: undefined,
  resume: undefined,
  salaryType: 0,
});

// 用户表单校验
const userRules = ref({
  postId: [{ required: true, message: "岗位不能为空", trigger: "change" }],
  salaryType: [
    { required: true, message: "岗位类型不能为空", trigger: "change" },
  ],
  postSalary: [
    { required: true, message: "岗位工资不能为空", trigger: "blur" },
  ],
  peopleNumber: [
    { required: true, message: "需求人数不能为空", trigger: "blur" },
  ],
  postSex: [{ required: true, message: "性别不能为空", trigger: "change" }],
  entryTime: [
    { required: true, message: "最迟进场时间不能为空", trigger: "change" },
  ],
  careerCharacter: [
    { required: true, message: "职业性格不能为空", trigger: "change" },
  ],
  personCharacter: [
    { required: true, message: "个人性格不能为空", trigger: "change" },
  ],
  abilityEvaluate: [
    { required: true, message: "业务能力评价不能为空", trigger: "change" },
  ],
  statusEvaluate: [
    { required: true, message: "工作状态评价不能为空", trigger: "change" },
  ],
  resume: [{ required: true, message: "需求原因不能为空", trigger: "blur" }],
});

/** 根据岗位ID获取岗位名称 */
function getPostName(postId) {
  const post = postList.value.find((item) => item.id === postId);
  return post ? post.postName : "";
}

/** 项目变更事件 */
function handleProjectChange(val) {
  if (val) {
    form.value.projectName = val.projectName;
    form.value.leaderUserName = val.leaderUserName;
    form.value.phone = val.phone;
  }
}

/** 岗位变更事件 */
function handlePostChange(val) {
  if (val) {
    userForm.value.postName = val.postName;
  }
}

/** 新增需求按钮操作 */
function handleAddUser() {
  // 重置表单
  userForm.value = {
    postId: undefined,
    postName: undefined,
    postSalary: undefined,
    peopleNumber: 1,
    postSex: undefined,
    entryTime: undefined,
    careerCharacter: undefined,
    personCharacter: undefined,
    abilityEvaluate: undefined,
    statusEvaluate: undefined,
    resume: undefined,
    salaryType: 0, // 默认为自定义类型
  };
  // 打开弹窗
  userDialogVisible.value = true;
}

/** 删除需求按钮操作 */
function handleDeleteUser(index) {
  form.value.staffList.splice(index, 1);
}

/** 提交需求表单 */
function submitUserForm() {
  userFormRef.value.validate((valid) => {
    if (valid) {
      // 添加用户需求到列表
      const user = JSON.parse(JSON.stringify(userForm.value));
      form.value.staffList.push(user);
      userDialogVisible.value = false;
    }
  });
}

/** 取消按钮 */
function cancel() {
  router.push("/personnelFiles/projectStaffApply");
}

/** 提交表单 */
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      // 验证是否有需求
      if (form.value.staffList.length === 0) {
        proxy.$modal.msgError("请至少添加一条需求");
        return;
      }

      loading.value = true;

      // 提交表单
      if (form.value.id) {
        updateProjectStaffApply(form.value)
          .then(() => {
            proxy.$modal.msgSuccess("修改成功");
            router.push("/personnelFiles/projectStaffApply");
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        addProjectStaffApply(form.value)
          .then(() => {
            proxy.$modal.msgSuccess("新增成功");
            router.push("/personnelFiles/projectStaffApply");
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}

/** 岗位类型变更事件 */
function handleSalaryTypeChange(val) {}

onMounted(() => {
  const id = route.query.id;
  type.value = route.query.type || "add";

  if (id) {
    title.value =
      type.value === "check" ? "查看项目人员申请" : "编辑项目人员申请";
    // 获取详情
    getProjectStaffApply(id).then((res) => {
      form.value = res.data;
      if (!form.value.staffList) {
        form.value.staffList = [];
      }
    });
  } else {
    title.value = "新增项目人员申请";
  }
});
</script>

<style scoped>
div /deep/ .el-input-number .el-input__inner {
  text-align: left !important;
}
</style>
