<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      :extraParams="{ projectId: form.projectId }"
      @success="getList"
    />
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <div class="w-full">
            <span class="font-bold relative">基础信息</span>
          </div>
          <el-form-item label="年月时间" prop="approachDate">
            {{ queryParams.approachDate }}
            <!-- <el-date-picker
              class="w-[200px]"
              v-model="queryParams.approachDate"
              type="month"
              placeholder="选择年月"
              format="YYYY-MM"
              value-format="YYYY-MM"
              clearable
              disabled
            /> -->
          </el-form-item>
          <el-form-item label="项目" prop="projectId">
            {{ queryParams.projectName }}
            <!-- <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              v-model:modelName="queryParams.projectName"
              disabled
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              
              :extraParams="{ parentId: '0' }"
              placeholder="请选择项目"
            ></RemoteSelect> -->
          </el-form-item>
          <div class="w-full">
            <span class="font-bold relative">特种证书信息</span>
          </div>
          <el-form-item label="姓名" prop="staffName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.staffName"
              placeholder="请输入姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="岗位" prop="postId">
            <RemoteSelect
              v-model="queryParams.postId"
              url="/system/post/list"
              labelKey="postName"
              valueKey="postId"
              placeholder="请选择岗位"
              class="w-[200px]"
            ></RemoteSelect>
          </el-form-item>

          <el-form-item label="人员类型" prop="staffType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.staffType"
              placeholder="请选择人员类型"
              clearable
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="操作合格证编号" prop="certificateCode">
            <el-input
              class="w-[200px]"
              v-model="queryParams.certificateCode"
              placeholder="请输入操作合格证编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" prop="status">
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择审核状态"
              clearable
            >
              <el-option
                v-for="dict in audit_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="certificateList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column label="姓名" align="center" prop="staffName" />
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column label="岗位" align="center" prop="postName" />
          <el-table-column label="年龄" align="center" prop="age" />
          <el-table-column
            label="身份证号码"
            align="center"
            prop="idNumber"
            width="160"
          />
          <el-table-column label="工作年限" align="center" prop="workYears" />
          <el-table-column
            label="操作合格证编号"
            width="160"
            align="center"
            prop="certificateCode"
          />
          <el-table-column label="工种" align="center" prop="workType">
            <template #default="scope">
              <dict-tag
                :options="identify_job_types"
                :value="scope.row.workType"
              />
            </template>
          </el-table-column>
          <el-table-column label="发证机关" align="center" prop="issueOrgan" />
          <el-table-column
            label="证书有效期"
            align="center"
            prop="effectiveDate"
            width="150px"
          >
            <template #default="scope">
              <span>{{ scope.row.effectiveDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="进场时间" align="center" prop="approachDate">
            <template #default="scope">
              <span>{{ scope.row.approachDate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="audit_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="240"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                type="text"
                @click="handleSubmitAudit(scope.row)"
                v-if="scope.row.status === '0'"
                >提审</el-button
              >
              <el-button
                type="text"
                @click="handleUpdate(scope.row)"
                v-if="scope.row.status === '0'"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-if="scope.row.status === '0'"
                >删除</el-button
              >
              <el-button
                type="text"
                @click="handleVerify(scope.row)"
                v-if="scope.row.status === '2' && !scope.row.takeStatus"
                >核销</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import ImportExcel from '@/components/ImportExcel/index.vue';
import {
  listSpecialCertificateDetail,
  delSpecialCertificateDetail,
  launchProcess,
} from '@/api/certificate/special';
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

// 字典数据
const { sys_user_sex, personnel_type, audit_status, identify_job_types } =
  proxy.useDict(
    'sys_user_sex',
    'personnel_type',
    'audit_status',
    'identify_job_types'
  );
const form = ref({});
// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 特种作业证书人员明细表格数据
const certificateList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  approachDate: undefined,
  projectId: undefined,
  staffName: undefined,
  postName: undefined,
  staffType: undefined,
  certificateCode: undefined,
  status: undefined,
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref('导入特种作业证书');
const importUrl = '/railway/wlCertificateSpecial/importData';
const templateUrl = '/railway/wlCertificateSpecial/importTemplate';

/** 查询特种作业证书人员明细列表 */
function getList() {
  loading.value = true;
  listSpecialCertificateDetail({
    ...queryParams.value,
  }).then((response) => {
    certificateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  router.push({
    path: '/certificate/certificateSpecial/form',
    query: { projectId: route.query.projectId },
  });
}

/** 查看按钮操作 */
function handleView(row) {
  proxy.getProcessRouterPath(
    row,
    '10002',
    row.id,
    '/certificate/certificateSpecial'
  );
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  router.push({
    path: '/certificate/certificateSpecial/form',
    query: { id: row.id, projectId: route.query.id },
  });
}

/** 提审按钮操作 */
function handleSubmitAudit(row) {
  proxy.$modal
    .confirm(`确认要提交审核"${row.staffName}"的证书吗？`)
    .then(function () {
      return launchProcess({
        bizId: row.id,
        templateId: '10002',
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('提交审核成功');
    })
    .catch(() => {});
}

/** 核销按钮操作 */
function handleVerify(row) {
  proxy.$modal
    .confirm(`确认要核销"${row.staffName}"的证书吗？`)
    .then(function () {
      return launchProcess({
        bizId: row.id,
        templateId: '10003',
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('核销成功');
    })
    .catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(`确认要删除"${row.staffName}"的证书吗？`)
    .then(function () {
      return delSpecialCertificateDetail(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导入按钮操作 */
function handleImport() {
  form.value.projectId = route.query.projectId;
  importOpen.value = true;
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'railway/wlCertificateSpecial/export',
    {
      ...queryParams.value,
    },
    `特种作业证书_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  queryParams.value.approachDate = route.query.serviceTime;
  queryParams.value.projectName = route.query.projectName;
  queryParams.value.projectId = route.query.projectId;
  getList();
});
</script>

<style scoped>
.font-bold {
  font-weight: 700;
}
.w-full {
  width: 100%;
}
</style>
