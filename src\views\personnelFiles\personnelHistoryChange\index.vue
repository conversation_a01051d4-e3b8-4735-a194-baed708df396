<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              class="w-[200px]"
              v-model="queryParams.keyword"
              placeholder="请输入关键字"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="所在单位:" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              placeholder="请选择单位名称"
              clearable
              class="w-[200px]"
              :extraParams="{ parentId: '0' }"
              @change="handleProjectChange"
            />
          </el-form-item>
          <el-form-item label="社保身份" prop="socialSecurityStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.socialSecurityStatus"
              placeholder="请选择社保身份"
              clearable
            >
              <el-option
                v-for="dict in social_security_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="人员类型" prop="personnelType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.personnelType"
              placeholder="请选择人员类型"
              clearable
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="从事工作" prop="engageWork">
            <el-select
              class="w-[200px]"
              v-model="queryParams.engageWork"
              placeholder="请选择从事工作"
              clearable
            >
              <el-option
                v-for="dict in work_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="工程类型" prop="projectType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.projectType"
              placeholder="请选择工程类型"
              clearable
            >
              <el-option
                v-for="dict in project_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间区间" prop="dateRange">
            <el-date-picker
              v-model="queryParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              class="w-[240px]"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>

          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="historyChangeList"
          ref="tableRef"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            :reserve-selection="true"
            type="selection"
            width="50"
            align="center"
          />
          <el-table-column
            label="档案号"
            prop="archiveNumber"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.archiveNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="姓名"
            prop="staffName"
            width="100"
            align="center"
          />
          <el-table-column
            label="身份证号"
            prop="idNumber"
            width="250"
            align="center"
          />
          <el-table-column label="性别" prop="gender" align="center">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="年龄" prop="age" align="center" />
          <el-table-column label="员工类型" prop="staffType" align="center">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="调出单位"
            prop="oldProjectName"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.oldProjectName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调入单位"
            prop="projectName"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.projectName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调出部门"
            prop="oldDeptName"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.oldDeptName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调入部门"
            prop="deptName"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调出岗位"
            prop="oldPostName"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.oldPostName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调入岗位"
            prop="postName"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.postName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="职务职别"
            width="200"
            align="center"
            prop="jobLevel"
          >
            <template #default="slotScope">
              <dict-tag
                :options="position_level"
                :value="slotScope.row.jobLevel"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="调动时间"
            prop="transferDate"
            width="170"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.transferDate }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="调动结束时间"
            prop="endTransferDate"
            width="170"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.endTransferDate || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="管理方式"
            prop="manageType"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <span>{{ slotScope.row.manageType }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="从事工作"
            prop="engageWork"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <dict-tag
                :options="work_type"
                :value="slotScope.row.engageWork"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="工程类型"
            prop="projectType"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <dict-tag
                :options="project_type"
                :value="slotScope.row.projectType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="社保身份"
            prop="socialSecurityStatus"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <dict-tag
                :options="social_security_status"
                :value="slotScope.row.socialSecurityStatus"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="最高学历"
            prop="highestEducation"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <dict-tag
                :options="education_type"
                :value="slotScope.row.highestEducation"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="职称分类"
            prop="titleCategory"
            width="200"
            align="center"
          >
            <template #default="slotScope">
              <dict-tag
                :options="title_category"
                :value="slotScope.row.titleCategory"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="180"
            :show-overflow-tooltip="false"
          >
            <template #default="scope">
              <div class="flex justify-center">
                <el-button type="text" @click="handleView(scope.row)"
                  >查看</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { listPersonnelHistoryChange } from '@/api/personnelFiles/personnelHistoryChange';

const { proxy } = getCurrentInstance();
const router = useRouter();

const {
  position_level,
  sys_user_sex,
  social_security_status,
  personnel_type,
  education_type,
  title_category,
  work_type,
  project_type,
} = proxy.useDict(
  'position_level',
  'sys_user_sex',
  'social_security_status',
  'personnel_type',
  'education_type',
  'title_category',
  'work_type',
  'project_type'
);

const tableRef = ref(null);
// 已经勾选的数据ID
const selectionIds = ref([]);
// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 人员历史变动表格数据
const historyChangeList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  dateRange: undefined,
  socialSecurityStatus: undefined,
  personnelType: undefined,
  jobContent: undefined,
  titleCategory: undefined,
});

/** 查询人员历史变动列表 */
function getList() {
  loading.value = true;

  // 处理日期范围
  const params = { ...queryParams.value };
  if (params.dateRange && params.dateRange.length === 2) {
    params.startDate = params.dateRange[0];
    params.endDate = params.dateRange[1];
  }
  delete params.dateRange; // 移除前端用的dateRange字段

  listPersonnelHistoryChange(params)
    .then((res) => {
      historyChangeList.value = res.rows;
      total.value = res.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  queryParams.value.dateRange = undefined;
  proxy.$refs['tableRef'].clearSelection();
  handleQuery();
}

// 多选模式下的选择变更处理
function handleSelectionChange(selection) {
  selectionIds.value = selection.map((item) => item.id);
}
/** 导出按钮操作 */
function handleExport() {
  // 处理日期范围用于导出
  const exportParams = { ...queryParams.value };
  if (exportParams.dateRange && exportParams.dateRange.length === 2) {
    exportParams.startDate = exportParams.dateRange[0];
    exportParams.endDate = exportParams.dateRange[1];
  }
  delete exportParams.dateRange;

  proxy.download(
    'railway/wlStaffInfo/historyChangesList/export',
    exportParams,
    `人员历史变动数据_${new Date().getTime()}.xlsx`
  );
}
function handleView(row) {
  router.push({
    name: 'IncumbentEmployeeAddOrEdit',
    query: { id: row.staffId, activeTab: 'resume', type: 'check' },
  });
}
onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.main-box-card {
  background: #fff;
}

.box-card {
  margin-bottom: 16px;
  border-radius: 8px;
}

.box-card-no-radius {
  border-radius: 0 0 8px 8px;
  margin-bottom: 0;
}

.mb8 {
  margin-bottom: 8px;
}

.reset-btn {
  background: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.reset-btn:hover {
  color: #2f7bff;
  border-color: #2f7bff;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[240px\] {
  width: 240px;
}
</style>
