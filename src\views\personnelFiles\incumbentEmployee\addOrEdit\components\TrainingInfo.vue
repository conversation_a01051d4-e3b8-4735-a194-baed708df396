<template>
  <div class="training-info">
    <div class="flex justify-between mb-4">
      <div class="font-bold text-xl">培训信息</div>
      <div>
        <!-- <el-button type="primary" @click="handleAdd">新增培训</el-button> -->
        <el-button type="primary" @click="handleExport">培训信息导出</el-button>
      </div>
    </div>

    <div v-if="!isEdit">
      <div v-if="trainingList.length === 0" class="info-content">
        <Empty text="暂无培训信息" />
      </div>
      <div v-else v-for="(item, index) in trainingList" :key="index">
        <div class="indexTitle">
          <span>培训{{ numberToChinese(index + 1) }}</span>
        </div>
        <div class="info-content">
          <div class="training-block">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">培训类别：</span>
                  <span>{{ item.trainingType == 0 ? "自培" : "委培" }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">培训班名：</span>
                  <span>{{ item.className || "--" }}</span>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">开始时间：</span>
                  <span>{{ item.beginTime || "--" }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">结束时间：</span>
                  <span>{{ item.endTime || "--" }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">培训形式：</span>
                  <span>{{
                    getDictLabel(training_form, item.trainingMode) || "--"
                  }}</span>
                </div>
              </el-col>
              <el-col v-if="item.trainingType == 0" :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">人员类型：</span>
                  <span>{{
                    getDictLabel(personnel_type, item.type) || "--"
                  }}</span>
                </div>
              </el-col>
              <el-col v-if="item.trainingType == 1" :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">培训成绩：</span>
                  <span>{{ item.score || "--" }}</span>
                </div>
              </el-col>
              <el-col v-if="item.trainingType == 1" :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">培训费：</span>
                  <span>{{ item.trainingFee || "--" }}</span>
                </div>
              </el-col>
              <el-col v-if="item.trainingType == 1" :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">培训效果评估：</span>
                  <span>{{
                    getDictLabel(evaluation_level, item.type) || "--"
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24">
                <div class="info-item">
                  <span class="label">附件：</span>
                  <div v-if="item.wlAnnexes && item.wlAnnexes.length > 0">
                    <span v-for="(item, index) in item.wlAnnexes" :key="index">
                      <div class="elLink" @click="downloadFile(item.path)">
                        {{ index + 1 }}、{{ item.name }}
                      </div>
                    </span>
                  </div>
                  <span v-else>--</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24">
                <div class="info-item">
                  <span class="label">培训内容：</span>
                  <span>{{ item.content || "--" }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑表单 -->
    <div v-if="isEdit">
      <div v-for="(item, index) in trainingList" :key="index">
        <div class="indexTitle">
          <span>培训{{ numberToChinese(index + 1) }}</span>
          <el-button type="primary" link @click="handleAdd">新增</el-button>
          <el-button
            v-if="trainingList.length > 1"
            type="danger"
            link
            @click="handleDelete(item, index)"
            >删除</el-button
          >
          <el-button type="success" link @click="handleSave(item, index)"
            >保存</el-button
          >
        </div>
        <el-form
          :ref="(el) => (formRefs[index] = el)"
          :model="item"
          :rules="rules"
          label-width="100px"
          class="training-form"
        >
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="培训类别" prop="trainingType">
                <el-radio-group
                  v-model="item.trainingType"
                  @change="changeRadio(item, index)"
                >
                  <el-radio :value="0">自培</el-radio>
                  <el-radio :value="1">委培</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="培训班名" prop="selfId">
                <sinceTrainSelect
                  v-model="item.selfId"
                  :modelName="item.className"
                  :type="item.trainingType"
                  :disabledSelectColumns="disabledSelect"
                  @change="changeTrainingId($event, item)"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="开始时间" prop="beginTime">
                <el-date-picker
                  v-model="item.beginTime"
                  type="date"
                  placeholder="请选择开始时间"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker
                  v-model="item.endTime"
                  type="date"
                  placeholder="请选择结束时间"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  disabled
                />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="培训形式" prop="trainingMode">
                <el-select
                  v-model="item.trainingMode"
                  placeholder="请选择培训形式"
                  style="width: 100%"
                  disabled
                >
                  <el-option
                    v-for="dict in training_form"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="item.trainingType == 0" :xs="24" :sm="12" :md="8">
              <el-form-item label="人员类型" prop="type">
                <el-select
                  class="w-[200px]"
                  v-model="item.type"
                  placeholder="请选择人员类型"
                  clearable
                >
                  <el-option
                    v-for="dict in personnel_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="item.trainingType == 1" :xs="24" :sm="12" :md="8">
              <el-form-item label="培训成绩" prop="score">
                <el-input-number
                  v-model="item.score"
                  :precision="0"
                  :step="1"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="item.trainingType == 1" :xs="24" :sm="12" :md="8">
              <el-form-item label="培训费" prop="trainingFee">
                <el-input-number
                  v-model="item.trainingFee"
                  :precision="2"
                  :step="0.01"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col v-if="item.trainingType == 1" :xs="24" :sm="12" :md="8">
              <el-form-item label="培训效果评估" prop="evaluateGrade">
                <el-select
                  v-model="item.evaluateGrade"
                  placeholder="请选择培训效果评估等级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in evaluation_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="Number(dict.value)"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24">
              <el-form-item label="附件" prop="score">
                <div v-if="item.wlAnnexes && item.wlAnnexes.length != 0">
                  <div v-for="(e, index) in item.wlAnnexes" :key="index">
                    <div class="elLink" @click="downloadFile(e.path)">
                      {{ index + 1 }}、{{ e.name }}
                    </div>
                  </div>
                </div>
                <div v-else>--</div>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24">
              <el-form-item label="培训内容" prop="">
                <el-input
                  v-model="item.content"
                  type="textarea"
                  disabled
                  :rows="4"
                  placeholder="请输入培训内容"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import {
  listWlStaffInfoTraining,
  addWlStaffInfoEvaluateCost,
  addWlStaffInfoTraining,
  putWlStaffInfoEditTrainingStaff,
  putWlStaffInfoEditEvaluateCost,
  delWlStaffTrainingStaff,
  delWlStaffEvaluateCost,
} from "@/api/personnelFiles/incumbentEmployee";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import sinceTrainSelect from "@/components/sinceTrainSelect/index.vue";
import { get } from "@vueuse/core";

const { proxy } = getCurrentInstance();
const route = useRoute();

const trainingList = ref([]);
const isEdit = route.query.type === "check" ? false : true;
const formRefs = ref([]);

// 查询参数
const queryParams = ref({
  // pageNum: 1,
  // pageSize: 10,
  staffId: route.query.id,
});

// 表单校验规则
const rules = ref({
  beginTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  trainingType: [
    { required: true, message: "请选择培训类别", trigger: "change" },
  ],
  type: [{ required: true, message: "请选择人员类型", trigger: "change" }],
  className: [{ required: true, message: "请输入培训班级", trigger: "blur" }],
  content: [{ required: true, message: "请输入培训内容", trigger: "blur" }],
  trainingMode: [
    { required: true, message: "请选择培训形式", trigger: "change" },
  ],
  selfId: [{ required: true, message: "请选择培训班名", trigger: "change" }],
});
// 已经关联的培训班名,用于禁用培训班名
const disabledSelect = computed(() => {
  return trainingList.value.map((item) => item.selfId);
});

// 字典数据
const { personnel_type, training_form, evaluation_level } = proxy.useDict(
  "personnel_type",
  "training_form",
  "evaluation_level"
);
// 培训班名改变
function changeTrainingId(event, item) {
  item.className = event.className;
  item.beginTime = event.beginTime;
  item.endTime = event.endTime;
  item.trainingMode = event.trainingMode;
  item.content = event.content;
  item.wlAnnexes = event.wlAnnexes;
  item.content = event.content;
  item.score = event.score;
  item.trainingFee = event.trainingFee;
  item.evaluateGrade = event.evaluateGrade;
}
// 数字转中文
function numberToChinese(num) {
  const unit = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  const digit = ["", "十", "百", "千", "万", "亿"];

  const numStr = num.toString();
  const len = numStr.length;
  let chinese = "";

  for (let i = 0; i < len; i++) {
    const currentNum = parseInt(numStr[i]);
    chinese += unit[currentNum] + digit[len - i - 1];
  }
  return chinese;
}
// 查看附件
function downloadFile(path) {
  if (path) {
    window.open(path);
  }
}

// 获取字典标签
function getDictLabel(dict, value) {
  const item = dict?.find((d) => d.value === value);
  return item ? item.label : "";
}

/** 获取培训列表 */
function getList() {
  listWlStaffInfoTraining(queryParams.value).then((response) => {
    if (response.code === 200) {
      trainingList.value = response.data || [];
      if (trainingList.value.length === 0 && isEdit) {
        handleAdd();
      }
      if (trainingList.value.length > 0) {
        trainingList.value.forEach((e) => {
          e.selfId = e.trainingId;
        });
      }
    }
  });
}
const newTraining = {
  beginTime: null,
  endTime: null,
  type: null,
  trainingMode: null,
  className: null,
  trainingFee: 0,
  evaluateGrade: null,
  score: 0,
  content: null,
  staffId: route.query.id,
  trainingType: 0,
  wlAnnexes: [],
  id: null,
  selfId: null,
};
/** 新增按钮操作 */
function handleAdd() {
  trainingList.value.push(newTraining);
}
/* 清空操作 */
function changeRadio(item, index) {
  let val = JSON.parse(JSON.stringify(newTraining));
  val.trainingType = item.trainingType;
  trainingList.value[index] = val;
  formRefs.value[index]?.resetFields();
}

/** 删除按钮操作 */
function handleDelete(item, index) {
  if (item.id) {
    ElMessageBox.confirm("是否确认删除该培训信息？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        if (item.trainingType == 1) {
          delWlStaffEvaluateCost(item.id).then((response) => {
            if (response.code === 200) {
              ElMessage.success("删除成功");
              getList();
            }
          });
        } else {
          delWlStaffTrainingStaff(item.id, item.staffId).then((response) => {
            if (response.code === 200) {
              ElMessage.success("删除成功");
              getList();
            }
          });
        }
      })
      .catch(() => {});
  } else {
    trainingList.value.splice(index, 1);
    if (trainingList.value.length === 0) {
      handleAdd();
    }
  }
}

/** 保存按钮操作 */
function handleSave(item, index) {
  const formRef = formRefs.value[index];
  formRef?.validate((valid) => {
    if (valid) {
      let submitFunction = null;
      if (item.trainingType == 0) {
        // 自培
        submitFunction = item.id
          ? putWlStaffInfoEditTrainingStaff
          : addWlStaffInfoTraining;
      } else if (item.trainingType == 1) {
        submitFunction = item.id
          ? putWlStaffInfoEditEvaluateCost
          : addWlStaffInfoEvaluateCost;
      }
      submitFunction(item).then((response) => {
        if (response.code === 200) {
          ElMessage.success(item.id ? "修改成功" : "新增成功");
          getList();
        }
      });
    }
  });
}

/** 导出按钮操作 */
function handleExport() {
  if (route.query.id) {
    proxy.download(
      "/railway/wlStaffInfo/training/export",
      {
        ...queryParams.value,
      },
      `员工培训信息_${new Date().getTime()}.xlsx`
    );
  }
}

onMounted(() => {
  if (route.query.id) {
    getList();
  }
});
</script>

<style scoped lang="scss">
.training-info {
  padding: 20px;
}

.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
  margin-bottom: 20px;
}

.training-block {
  position: relative;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 100px;
    text-align: right;
    color: #606266;
    padding-right: 10px;
  }
}

.operation-buttons {
  position: absolute;
  top: 0;
  right: 0;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}

.indexTitle {
  margin: 20px 0;
  span {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #2674fe;
    background-color: #e6efff;
    padding: 10px 20px;
    border-radius: 4px;
    margin-right: 20px;
  }
}

.training-form {
  padding: 20px;
}

.form-buttons {
  text-align: center;
  margin-top: 20px;
}
.elLink {
  cursor: pointer;
}
.elLink:hover {
  color: #409eff;
}
</style>
