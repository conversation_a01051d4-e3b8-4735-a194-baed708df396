import request from "@/utils/request";

// 查询合同类型列表
export function listContractType(query) {
  return request({
    url: "/railway/wlType/list",
    method: "get",
    params: { ...query, type: "HT" },
  });
}

// 查询合同类型详细
export function getContractType(id) {
  return request({
    url: "/railway/wlType/" + id,
    method: "get",
  });
}

// 新增合同类型
export function addContractType(data) {
  return request({
    url: "/railway/wlType",
    method: "post",
    data: { ...data, type: "HT" },
  });
}

// 编辑合同类型
export function updateContractType(data) {
  return request({
    url: "/railway/wlType",
    method: "put",
    data: { ...data, type: "HT" },
  });
}

// 删除合同类型
export function delContractType(id) {
  return request({
    url: "/railway/wlType/" + id,
    method: "delete",
  });
}

// 下载合同类型附件
export function downloadContractTypeFile(fileName) {
  return request({
    url: "/railway/wlType/download",
    method: "get",
    params: { fileName },
    responseType: "blob",
  });
}
