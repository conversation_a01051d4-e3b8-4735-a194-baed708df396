<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="岗位名称" prop="postName">
            <el-input
              v-model="queryParams.postName"
              placeholder="请输入岗位名称"
              clearable
              class="w-[200px]"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="岗位状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="postList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="岗位名称" align="center" prop="postName" />
          <el-table-column label="岗位系数" align="center" prop="postFactor" />
          <el-table-column
            label="岗位工资标准（元）"
            align="center"
            prop="postSalary"
          />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="100"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑岗位对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="postForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="岗位名称" prop="postName">
          <el-input v-model="form.postName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位系数" prop="postFactor">
          <el-input-number
            v-model="form.postFactor"
            :precision="2"
            :step="0.1"
            :min="0"
            placeholder="请输入岗位系数"
          />
        </el-form-item>
        <el-form-item label="岗位工资标准" prop="postSalary">
          <el-input-number
            v-model="form.postSalary"
            :precision="2"
            :step="100"
            :min="0"
            placeholder="请输入岗位工资标准"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  listPost,
  getPost,
  addPost,
  updatePost,
  delPost,
} from "@/api/basic/post";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 岗位表格数据
const postList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  postName: undefined,
  status: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  postName: undefined,
  postFactor: 1.0,
  postSalary: 0.0,
  status: "0",
});

// 表单校验
const rules = ref({
  postName: [{ required: true, message: "岗位名称不能为空", trigger: "blur" }],
  postFactor: [
    { required: true, message: "岗位系数不能为空", trigger: "blur" },
  ],
  postSalary: [
    { required: true, message: "岗位工资标准不能为空", trigger: "blur" },
  ],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
});

/** 查询岗位列表 */
function getList() {
  loading.value = true;
  listPost(queryParams.value).then((response) => {
    postList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    postName: undefined,
    postFactor: 1.0,
    postSalary: 0.0,
    status: "0",
  };
  proxy.resetForm("postForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加岗位";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getPost(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑岗位";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["postForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updatePost(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addPost(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

onMounted(() => {
  getList();
});
</script>
