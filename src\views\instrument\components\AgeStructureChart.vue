<template>
  <el-card class="box-card" style="height: 100%;">
    <div class="card-header">
        <span>{{ chartData.title }}</span>
    </div>
    <div class="pyramid-chart-container">
      <div ref="chartRef" class="chart"></div>
      <div class="legend-container">
        <div class="legend-item" v-for="(item, index) in chartData.data" :key="index">
          <div class="color-block" :style="{ borderColor: colors[index % colors.length] }"></div>
          <span class="age-group">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts/core';
import { FunnelChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  Funnel<PERSON><PERSON>,
  Canvas<PERSON>enderer
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
});

const chartRef = ref(null);
let chart = null;

const colors = [
  '#32C5E9',  // 24岁以下
  '#9FE6B8',  // 24-30岁
  '#9D96F5',  // 31-40岁
  '#E690D1',  // 41-50岁
  '#96BFFF',  // 51-56岁
  '#FEDB65'   // 56岁以上
];

const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  
  // 对数据进行排序，按照 percent 从小到大排序
  // const sortedData = [...props.chartData.data].sort((a, b) => a.percent - b.percent);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}人 ({d}%)'
    },
    series: [
      {
        name: '年龄结构',
        type: 'funnel',
        sort: 'ascending', // 保持排序后的顺序
        gap: 2,
        left: '10%',
        right: '35%',
        top: '10%',
        bottom: '5%',
        minSize: '0%',
        maxSize: '100%',
        label: {
          show: true,
          position: 'right',
          formatter: function(params) {
            return params.value + '人 ' + params.percent + '%';
          },
          fontSize: 12,
          color: '#000'
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          borderWidth: 0
        },
        data: props.chartData.data.map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: colors[index]
          }
        }))
      }
    ]
  };
  
  chart.setOption(option);
  
  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
};

watch(() => props.chartData, () => {
  chart && chart.dispose();
  initChart();
}, { deep: true });

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.pyramid-chart-container {
  display: flex;
  height: 300px;
}

.chart {
  flex: 3;
  height: 100%;
}

.legend-container {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 8px 0 10px 0;
  border-bottom: 1px dashed #C9CDD6;
}

.color-block {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #ccc;


}

.age-group {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.percentage {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}
.card-header{
  min-height: 32px;
}
</style> 