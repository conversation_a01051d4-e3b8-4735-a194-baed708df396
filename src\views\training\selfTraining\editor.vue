<template>
  <div class="app-container">
    <el-card class="box-card">
      <div class="card-header">
        <span class="card-title">自培班次基本信息</span>
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="mt-4"
        :disabled="isReadOnly"
      >
        <!-- 第一行 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="培训单位" prop="projectId">
              <RemoteSelect
                v-model="form.projectId"
                :multiple="true"
                url="/system/dept/list"
                labelKey="deptName"
                valueKey="deptId"
                :extraParams="{ parentId: '0' }"
                placeholder="请选择培训单位"
              ></RemoteSelect>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训班名" prop="className">
              <el-input v-model="form.className" placeholder="请输入培训班名" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训形式" prop="trainingMode">
              <el-select
                v-model="form.trainingMode"
                placeholder="请选择培训形式"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in training_form"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="培训开始时间" prop="beginTime">
              <el-date-picker
                v-model="form.beginTime"
                type="date"
                placeholder="选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培训结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="date"
                placeholder="选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="岗前培训课时" prop="beforeHour">
              <el-input-number
                :controls="false"
                v-model="form.beforeHour"
                :min="0"
                placeholder="请输入数值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="安全培训课时" prop="secureHour">
              <el-input-number
                :controls="false"
                v-model="form.secureHour"
                :min="0"
                placeholder="请输入数值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="技术培训课时" prop="artHour">
              <el-input-number
                :controls="false"
                v-model="form.artHour"
                :min="0"
                placeholder="请输入数值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="其他培训课时" prop="otherHour">
              <el-input-number
                :controls="false"
                v-model="form.otherHour"
                :min="0"
                placeholder="请输入数值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学习课时" prop="totalHours">
              <el-input-number
                :controls="false"
                v-model="form.totalHours"
                :min="0"
                disabled
                placeholder="请输入数值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="管理人员" prop="manager">
              <user-select
                v-model="form.manager"
                :disabledSelectColumns="disabledSelectColumns"
                :multiple="true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="技术人员" prop="techStaff">
              <user-select
                v-model="form.techStaff"
                :disabledSelectColumns="disabledSelectColumns"
                :multiple="true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工人" prop="worker">
              <user-select
                v-model="form.worker"
                :disabledSelectColumns="disabledSelectColumns"
                :multiple="true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="劳务人员" prop="laborStaff">
              <user-select
                v-model="form.laborStaff"
                :disabledSelectColumns="disabledSelectColumns"
                :multiple="true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="入学人数" prop="enrollmentCount">
              <el-input-number
                :controls="false"
                v-model="form.enrollmentCount"
                :min="0"
                placeholder="请输入学人数"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="结业人数" prop="graduaNumber">
              <el-input-number
                :controls="false"
                v-model="form.graduaNumber"
                :min="0"
                placeholder="请输入结业人数"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核方式" prop="assessWay">
              <el-checkbox-group v-model="form.assessWay">
                <el-checkbox
                  v-for="(item, index) in assessment_method"
                  :key="index"
                  :value="item.value"
                  >{{ item.label }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 关联培训计划 -->
        <div class="card-header">
          <span class="card-title">关联培训计划</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="培训计划" prop="planId">
              <trainSelect
                v-model="form.planId"
                :modelName="form.planName"
                @change="
                  (e) => {
                    form.planName = e.className;
                  }
                "
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 职教师授课课时明细表 -->
      <div class="card-header">
        <span class="card-title"
          >兼职教师授课津贴报批<span
            v-if="calculate"
            style="color: red; font-size: 14px; margin-left: 10px"
            >*{{ calculate }}</span
          ></span
        >
      </div>
      <el-button
        style="margin-bottom: 10px"
        class="custom-btn"
        type="primary"
        @click="handleAddTeacher"
        :disabled="isReadOnly"
        >新增</el-button
      >

      <el-table :data="teachers" border style="width: 100%">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="培训类型" align="center" prop="trainingType">
          <template #default="scope">
            <dict-tag
              :options="training_type"
              :value="scope.row.trainingType"
            />
          </template>
        </el-table-column>
        <el-table-column label="授课教师姓名" align="center" prop="staffName" />
        <el-table-column label="职务" align="center" prop="position">
          <template #default="scope">
            <dict-tag
              :options="professional_title"
              :value="scope.row.position"
            />
          </template>
        </el-table-column>
        <el-table-column label="职称" align="center" prop="jobTitle">
          <template #default="scope">
            <dict-tag :options="title_category" :value="scope.row.jobTitle" />
          </template>
        </el-table-column>
        <el-table-column
          label="授课内容"
          align="center"
          prop="lectureContent"
        />
        <el-table-column label="参考资料" align="center" prop="reference" />
        <el-table-column label="资料是否有自编" align="center" prop="isSelf">
          <template #default="scope">
            <span>{{ scope.row.isSelf === '1' ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="课时数" align="center" prop="classHour" />
        <el-table-column label="津贴标准" align="center" prop="artSubsidy" />
        <el-table-column label="金额" align="center" prop="artSubsidyTotal" />
        <el-table-column label="操作" align="center" width="80">
          <template #default="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDeleteTeacher(scope.$index)"
              :disabled="isReadOnly"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="total-row">
        <span>合计：{{ totalAmount }} 元</span>
      </div>

      <!-- 上传附件 -->
      <div class="card-header">
        <span class="card-title">上传附件</span>
      </div>
      <div style="width: 30%">
        <file-upload
          v-if="!isReadOnly"
          v-model="form.wlAnnexes"
          :fileType="fileType"
          :fileSize="60"
          :limit="10"
          :disabled="isReadOnly"
        ></file-upload>

        <attachment-display v-else :attachments="form.wlAnnexes" />
      </div>
      <!-- 按钮组 -->
      <div class="button-container">
        <el-button
          type="primary"
          @click="submitForm"
          :loading="btnLoading"
          v-if="!isReadOnly"
          >保 存</el-button
        >
      </div>
    </el-card>

    <!-- 兼职教师授课信息新增弹窗 -->
    <el-dialog
      v-model="teacherDialogVisible"
      title="兼职教师授课津贴新增"
      width="1000px"
      :close-on-click-modal="false"
      :show-close="false"
      class="teacher-dialog"
      append-to-body
    >
      <el-form
        ref="teacherFormRef"
        :model="teacherForm"
        :rules="teacherRules"
        label-width="120px"
        label-position="right"
        class="teacher-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="培训类型" prop="trainingType">
              <el-select
                v-model="teacherForm.trainingType"
                placeholder="请选择培训类型"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in training_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授课教师姓名" prop="staffName">
              <div
                class="input-with-select"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                "
              >
                <el-input
                  style="width: 75%"
                  v-model="teacherForm.staffName"
                  placeholder="请输入授课教师姓名"
                />
                <user-select
                  style="width: 20%"
                  v-model="teacherForm.staffId"
                  :showSelected="false"
                  @change="selectTeacher"
                  buttonText="选择"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职务" prop="position">
              <el-select
                v-model="teacherForm.position"
                placeholder="请选择职务"
              >
                <el-option
                  v-for="dict in professional_title"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称" prop="jobTitle">
              <el-select
                v-model="teacherForm.jobTitle"
                placeholder="请选择职称"
              >
                <el-option
                  v-for="dict in title_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授课课时" prop="classHour">
              <el-input-number
                :controls="false"
                v-model="teacherForm.classHour"
                :min="0"
                placeholder="请输入授课课时"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="津贴标准" prop="artSubsidy">
              <el-input-number
                :controls="false"
                v-model="teacherForm.artSubsidy"
                :min="0"
                :precision="2"
                placeholder="请输入津贴标准"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金额" prop="artSubsidyTotal">
              <el-input-number
                :controls="false"
                v-model="teacherForm.artSubsidyTotal"
                :min="0"
                :precision="2"
                disabled
                placeholder="请输入金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="资料是否自编" prop="isSelf">
              <el-radio-group v-model="teacherForm.isSelf">
                <el-radio value="1">是</el-radio>
                <el-radio value="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="参考资料名称" prop="reference">
              <el-input
                v-model="teacherForm.reference"
                placeholder="请输入参考资料名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="授课内容" prop="lectureContent">
              <el-input
                v-model="teacherForm.lectureContent"
                type="textarea"
                :rows="4"
                placeholder="请输入授课内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24"> </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="teacher-dialog-footer">
          <el-button plain @click="CancelDialog">取消</el-button>
          <el-button type="primary" plain @click="submitTeacherForm"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  reactive,
  getCurrentInstance,
  onMounted,
  nextTick,
  watch,
} from 'vue';
import trainSelect from '@/components/trainSelect/index.vue';
import { useRoute, useRouter } from 'vue-router';
import {
  getWlTrainingSelf,
  addWlTrainingSelf,
  updateWlTrainingSelf,
} from '@/api/training/selfTraining';
import AttachmentDisplay from '@/components/AttachmentDisplay/index.vue';
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

// 字典数据
const {
  training_form,
  training_type,
  assessment_method,
  professional_title,
  title_category,
} = proxy.useDict(
  'training_form',
  'training_type',
  'training_status',
  'assessment_method',
  'professional_title',
  'title_category'
);

const viewType = route.query.type;

// 计算属性：根据viewType判断是否为只读模式
const isReadOnly = computed(() => {
  return viewType === 'look';
});

// 表单参数
const formRef = ref(null);
const form = ref({
  id: undefined,
  projectName: undefined,
  projectId: [],
  className: '',
  trainingMode: '',
  beginTime: '',
  endTime: '',
  secureHour: 0,
  safetyTraining: 0,
  artHour: 0,
  otherTraining: 0,
  manager: '',
  techStaff: '',
  worker: '',
  laborStaff: '',
  businessStaff: '',
  enrollmentCount: 0,
  graduaNumber: 0,
  assessWay: [],
  trainingPlanId: '',
  planName: '',
  fileIds: '',
  remark: '',
  status: '0',
  beforeHour: 0,

  otherHour: 0,
  trainingStaffs: [], //员工关系 {staffId：员工ID，type：0 管理人员 1技术人员 2 工人 3 劳务人员}
  teachers: [], //职教师授课课时明细表
  wlAnnexes: [], // 附件
});
let checkTotalHours = (rule, value, callback) => {
  if (Number(value) < 0) {
    callback(new Error('数值不能小于0'));
  }
  callback();
};
let checkGraduaNumber = (rule, value, callback) => {
  if (form.value.enrollmentCount < form.value.graduaNumber) {
    callback(new Error('结业人数不能大于入学人数'));
  } else {
    callback();
  }
};
// 表单校验规则
const rules = ref({
  projectName: [
    { required: true, message: '培训单位不能为空', trigger: 'change' },
  ],
  className: [{ required: true, message: '培训班名不能为空', trigger: 'blur' }],
  trainingMode: [
    { required: true, message: '培训形式不能为空', trigger: 'change' },
  ],
  beginTime: [
    { required: true, message: '开始时间不能为空', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '结束时间不能为空', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' },
  ],
  enrollmentCount: [
    {
      required: true,
      message: '入学人数不能为空',
      trigger: ['blur', 'change'],
    },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  assessWay: [
    { required: true, message: '考核方式不能为空', trigger: 'change' },
  ],
  totalHours: [
    {
      required: true,
      message: '总学习课时不能为空',
      trigger: ['blur', 'change'],
    },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  projectId: [
    {
      required: true,
      message: '培训单位不能为空',
      trigger: ['blur', 'change'],
    },
  ],
  graduaNumber: [
    {
      required: true,
      validator: checkGraduaNumber,
      trigger: ['blur', 'change'],
    },
  ],
});

// 验证结束时间必须大于开始时间
function validateEndTime(rule, value, callback) {
  if (form.value.beginTime && value) {
    if (new Date(value) <= new Date(form.value.beginTime)) {
      callback(new Error('结束时间必须大于开始时间'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}

// 职教师授课明细
const teachers = ref([]);

// 上传文件列表
const fileList = ref([]);
const btnLoading = ref(false);
// 上传文件类型
const fileType = ref([
  'png',
  'jpg',
  'docx',
  'xlsx',
  'pptx',
  'pdf',
  'rar',
  'zip',
]);

// 计算总金额
const totalAmount = computed(() => {
  return teachers.value
    .reduce((sum, item) => {
      return sum + (Number(item.artSubsidyTotal) || 0);
    }, 0)
    .toFixed(2);
});
// 禁用选择列
const disabledSelectColumns = computed(() => {
  let columns = [];
  let arr = [];
  let items = [];
  let trainingStaffs = [];
  if (form.value.manager) {
    columns = form.value.manager.map((item) => item);
    arr = [...new Set([...arr, ...columns])];
    items = form.value.manager.map((item) => ({ staffId: item, type: 0 }));
    trainingStaffs = [...trainingStaffs, ...items];
  }
  if (form.value.techStaff) {
    columns = form.value.techStaff.map((item) => item);
    arr = [...new Set([...arr, ...columns])];
    items = form.value.techStaff.map((item) => ({ staffId: item, type: 1 }));
    trainingStaffs = [...trainingStaffs, ...items];
  }
  if (form.value.worker) {
    columns = form.value.worker.map((item) => item);
    arr = [...new Set([...arr, ...columns])];
    items = form.value.worker.map((item) => ({ staffId: item, type: 2 }));
    trainingStaffs = [...trainingStaffs, ...items];
  }
  if (form.value.laborStaff) {
    columns = form.value.laborStaff.map((item) => item);
    arr = [...new Set([...arr, ...columns])];
    items = form.value.laborStaff.map((item) => ({ staffId: item, type: 3 }));
    trainingStaffs = [...trainingStaffs, ...items];
  }
  form.value.trainingStaffs = trainingStaffs; // 将培训人员整合在一起
  return arr;
});
watch(
  form,
  (newVal) => {
    let enrollmentCount = 0;
    if (newVal.manager) {
      enrollmentCount += newVal.manager.length;
    }
    if (newVal.techStaff) {
      enrollmentCount += newVal.techStaff.length;
    }
    if (newVal.worker) {
      enrollmentCount += newVal.worker.length;
    }
    if (newVal.laborStaff) {
      enrollmentCount += newVal.laborStaff.length;
    }
    form.value.enrollmentCount = enrollmentCount;

    let total = 0;
    // 汇总各类培训课时
    total += Number(form.value.beforeHour || 0); // 岗前培训
    total += Number(form.value.secureHour || 0); // 安全培训
    total += Number(form.value.artHour || 0); // 技术培训
    total += Number(form.value.otherHour || 0); // 其他培训

    form.value.totalHours = total;
  },
  { deep: true }
);
// 教师弹窗相关数据
const teacherDialogVisible = ref(false);
const teacherFormRef = ref(null);
const teacherForm = ref({
  trainingType: null,
  staffName: '',
  position: '',
  jobTitle: '',
  classHour: '',
  artSubsidy: '',
  artSubsidyTotal: '',
  isSelf: '0',
  reference: '',
  lectureContent: '',
});

// 教师表单校验规则
const teacherRules = ref({
  trainingType: [
    { required: true, message: '培训类型不能为空', trigger: 'change' },
  ],
  staffName: [
    {
      required: true,
      message: '授课教师姓名不能为空',
      trigger: ['blur', 'change'],
    },
  ],
  classHour: [
    { required: true, message: '授课课时不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  isSelf: [{ required: true, message: '请选择是否自编', trigger: 'change' }],
  lectureContent: [
    { required: true, message: '授课内容不能为空', trigger: 'blur' },
  ],
  jobTitle: [
    { required: true, message: '职称不能为空', trigger: ['blur', 'change'] },
  ],
  position: [
    { required: true, message: '职务不能为空', trigger: ['blur', 'change'] },
  ],
  artSubsidy: [
    { required: true, message: '津贴标准不能为空', trigger: 'blur' },
    { validator: checkTotalHours, trigger: ['blur', 'change'] },
  ],
  reference: [
    { required: true, message: '参考资料名称不能为空', trigger: 'blur' },
  ],
});

// 新增教师行
const handleAddTeacher = () => {
  // 重置表单
  teacherForm.value = {
    trainingType: null,
    staffName: '',
    position: '',
    jobTitle: '',
    classHour: 0,
    artSubsidy: 0,
    artSubsidyTotal: '',
    isSelf: '0',
    reference: '',
    lectureContent: '',
  };
  // 显示弹窗
  teacherDialogVisible.value = true;
};

watch(
  teacherForm,
  (newVal) => {
    let artSubsidyTotal = newVal.classHour * newVal.artSubsidy;
    teacherForm.value.artSubsidyTotal = artSubsidyTotal;
  },
  { deep: true }
);
// 选择教师
const selectTeacher = (e) => {
  teacherForm.value.staffName = e.name;
  if (e.professionalTitle) {
    teacherForm.value.position = e.professionalTitle;
  }
  if (e.titleCategory) {
    teacherForm.value.jobTitle = e.titleCategory;
  }
};
// 取消教师弹窗
const CancelDialog = () => {
  teacherFormRef.value.resetFields();
  teacherDialogVisible.value = false;
};
// 提交教师表单
const submitTeacherForm = () => {
  console.log(teacherForm.value);
  teacherFormRef.value.validate((valid) => {
    if (valid) {
      // 添加到表格中
      teachers.value.push({ ...teacherForm.value });
      // 关闭弹窗
      CancelDialog();
    }
  });
};

// 删除教师行
const handleDeleteTeacher = (index) => {
  teachers.value.splice(index, 1);
};
// 文件上传处理
const handleFileUpload = (options) => {
  const formData = new FormData();
  formData.append('file', options.file);
  proxy.upload('/common/upload', formData).then((res) => {
    fileList.value.push({
      name: res.fileName,
      url: res.fileName,
      fileId: res.fileId,
    });
    // 更新表单中的文件ID列表
    updateFileIds();
  });
};

// 文件删除处理
const handleFileRemove = (file) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    // 更新表单中的文件ID列表
    updateFileIds();
  }
};

// 超出文件数量限制
const handleExceed = () => {
  proxy.$message.warning('最多只能上传5个文件');
};

// 更新表单中的文件ID列表
const updateFileIds = () => {
  form.value.fileIds = fileList.value.map((file) => file.fileId).join(',');
};
// 计算教师表里面的时间是否与基本信息对齐
const calculate = computed(() => {
  let data = teachers.value;
  let beforeHour = Number(form.value.beforeHour || 0);
  let secureHour = Number(form.value.secureHour || 0);
  let artHour = Number(form.value.artHour || 0);
  let otherHour = Number(form.value.otherHour || 0);
  let beforeData = data.filter((item) => item.trainingType == 0);
  let secureData = data.filter((item) => item.trainingType == 1);
  let artData = data.filter((item) => item.trainingType == 2);
  let otherData = data.filter((item) => item.trainingType == 3);
  if (beforeHour || beforeHour === 0) {
    // 填写时间后 计算课时是否对等
    let classHour = 0;
    beforeData.forEach((item) => {
      classHour += Number(item.classHour || 0);
    });
    if (classHour != beforeHour) {
      return '岗前培训课时与兼职教师对应课时数不匹配';
    }
  }
  if (secureHour || secureHour === 0) {
    // 填写时间后 计算课时是否对等
    let classHour = 0;
    secureData.forEach((item) => {
      classHour += Number(item.classHour || 0);
    });
    if (classHour != secureHour) {
      return '安全培训课时与兼职教师对应课时数不匹配';
    }
  }
  if (artHour || artHour === 0) {
    // 填写时间后 计算课时是否对等
    let classHour = 0;
    artData.forEach((item) => {
      classHour += Number(item.classHour || 0);
    });
    if (classHour != artHour) {
      return '技术培训课时与兼职教师对应课时数不匹配';
    }
  }
  if (otherHour || otherHour === 0) {
    // 填写时间后 计算课时是否对等
    let classHour = 0;
    otherData.forEach((item) => {
      classHour += Number(item.classHour || 0);
    });
    if (classHour != otherHour) {
      return '其他培训课时与兼职教师对应课时数不匹配';
    }
  }
  return '';
});
// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (calculate.value != '') {
        // 表格验证未通过
        proxy.$modal.msgError(calculate.value);
        return;
      }
      // 项目数组
      form.value.projectSelves = form.value.projectId.map((item) => ({
        projectId: item,
      }));

      // 将教师列表转换为JSON字符串保存到表单中
      const submitForm = Object.assign({}, form.value);
      // 如果需要，将考核方式数组转为字符串
      if (Array.isArray(submitForm.assessWay)) {
        submitForm.assessWay = submitForm.assessWay.join(',');
      }
      submitForm.teachers = teachers.value;
      submitForm.beginTime = form.value.beginTime + ' 00:00:00';
      submitForm.endTime = form.value.endTime + ' 23:59:59';
      // 附件处理
      submitForm.wlAnnexes = form.value.wlAnnexes.map((e) => {
        let value = {
          name: e.name,
          path: e.url,
        };
        return value;
      });
      btnLoading.value = true;
      // 后端不需要这个参数
      delete submitForm.projectId;
      if (submitForm.id) {
        // 修改
        updateWlTrainingSelf(submitForm).then((res) => {
          proxy.$modal.msgSuccess('修改成功');
          cancel();
          btnLoading.value = false;
        });
      } else {
        // 新增
        addWlTrainingSelf(submitForm)
          .then((res) => {
            proxy.$modal.msgSuccess('新增成功');
            cancel();
            btnLoading.value = false;
          })
          .catch(() => {
            btnLoading.value = false;
          });
      }
    }
  });
};

// 取消操作
const cancel = () => {
  router.push('/training/selfTraining');
};
// 获取详情
const getDetail = (id) => {
  getWlTrainingSelf(id).then((res) => {
    form.value = res.data;
    // 处理考核方式字符串转数组
    if (typeof res.data.assessWay === 'string' && res.data.assessWay) {
      form.value.assessWay = res.data.assessWay.split(',');
    } else {
      form.value.assessWay = [];
    }
    // 解析教师列表JSON
    if (res.data.teachers) {
      try {
        teachers.value = res.data.teachers;
      } catch (e) {
        teachers.value = [];
      }
    }
    // 解析项目
    if (res.data.projectSelves) {
      form.value.projectId = res.data.projectSelves.map(
        (item) => item.projectId
      );
    }

    // 解析培训人员
    if (res.data.trainingStaffs) {
      form.value.manager = form.value.trainingStaffs
        .filter((item) => item.type == 0)
        .map((item) => item.staffId);
      form.value.techStaff = form.value.trainingStaffs
        .filter((item) => item.type == 1)
        .map((item) => item.staffId);
      form.value.worker = form.value.trainingStaffs
        .filter((item) => item.type == 2)
        .map((item) => item.staffId);
      form.value.laborStaff = form.value.trainingStaffs
        .filter((item) => item.type == 3)
        .map((item) => item.staffId);
    }
    // 解析文件列表
    if (res.data.wlAnnexes) {
      form.value.wlAnnexes = res.data.wlAnnexes.map((e) => {
        let value = {
          name: e.name,
          url: e.path,
        };
        return value;
      });
    } else {
      form.value.wlAnnexes = [];
    }
  });
};

// 页面加载时，如果有ID参数，则加载详情
onMounted(() => {
  const id = route.query.id;
  if (id) {
    getDetail(id);
  }
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .box-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      margin-top: 30px;

      .card-title {
        font-size: 16px;
        font-weight: bold;
        position: relative;
        padding-left: 10px;
      }
    }
  }

  .total-row {
    text-align: right;
    padding: 10px 0;
    font-weight: 400;
    font-size: 16px;
    background-color: #f5f6fb;
    padding-right: 50px;
  }

  .button-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 10px 0;
  }

  // 教师选择按钮样式
  .input-with-select {
    position: relative;

    .select-text {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #409eff;
      cursor: pointer;
      font-size: 14px;
    }

    .el-input {
      width: 100%;
    }
  }
}

.teacher-dialog {
  :deep(.el-dialog__header) {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    margin-right: 0;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  .teacher-form {
    .required-label {
      position: relative;

      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
  }

  .teacher-dialog-footer {
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .el-button {
      min-width: 80px;
      margin: 0 10px;
    }
  }
}
</style>
