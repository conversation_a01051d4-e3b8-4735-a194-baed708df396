<template>
  <div
    class="page-search-container"
    :class="{ collapsed: isCollapsed }"
    :style="{
      top: top,
    }"
  >
    <div v-show="!isCollapsed" class="search-expanded">
      <el-input
        ref="searchInputRef"
        v-model="searchText"
        placeholder="输入搜索内容"
        @keydown.enter="findNext"
        @keydown.esc="toggleCollapse"
        @input="handleSearch"
        size="default"
        class="search-input"
      >
        <template #prefix>
          <el-icon class="search-icon"><Search /></el-icon>
        </template>
        <template #append>
          <div class="search-actions">
            <span
              class="match-count"
              :class="{ 'no-match': matchCount === 0 }"
              >{{
                matchCount > 0 ? `${currentMatchIndex}/${matchCount}` : "无匹配"
              }}</span
            >
            <el-button
              :disabled="matchCount === 0"
              @click="findPrev"
              class="nav-button"
            >
              <el-icon><ArrowUp /></el-icon>
            </el-button>
            <el-button
              :disabled="matchCount === 0"
              @click="findNext"
              class="nav-button"
            >
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <el-button @click="toggleCollapse" class="close-button">
              <el-icon><Fold /></el-icon>
            </el-button>
          </div>
        </template>
      </el-input>
    </div>

    <div v-show="isCollapsed" class="search-collapsed" @click="toggleCollapse">
      <el-button class="expand-button" type="primary" circle>
        <el-icon><Search /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import {
  Search,
  ArrowUp,
  ArrowDown,
  Close,
  Fold,
} from "@element-plus/icons-vue";

const props = defineProps({
  // 要搜索的容器选择器，默认为body
  container: {
    type: String,
    default: "body",
  },
  top: {
    default: "50px",
  },
  // 要搜索的元素选择器，默认为所有可见文本节点
  selector: {
    type: String,
    default: "*",
  },
  // 搜索结果高亮颜色
  highlightColor: {
    type: String,
    default: "#FFEB3B",
  },
  // 当前选中高亮颜色
  activeHighlightColor: {
    type: String,
    default: "#FFA000",
  },
  // 搜索延迟时间(ms)
  searchDelay: {
    type: Number,
    default: 300,
  },
  // 默认是否收起
  defaultCollapsed: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["search", "close", "collapse", "expand"]);

// 状态变量
const searchText = ref("");
const matchCount = ref(0);
const currentMatchIndex = ref(0);
const searchResults = ref([]);
const highlightElements = ref([]);
const searchInputRef = ref(null);
const isCollapsed = ref(props.defaultCollapsed);
let searchTimeout = null;

// 切换收纳状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  if (!isCollapsed.value) {
    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    });
    emit("expand");
  } else {
    emit("collapse");
  }
};

// 关闭搜索框
const close = () => {
  clearHighlights();
  searchText.value = "";
  matchCount.value = 0;
  currentMatchIndex.value = 0;
  emit("close");
};

// 清除所有高亮
const clearHighlights = () => {
  highlightElements.value.forEach((el) => {
    const parent = el.parentNode;
    if (parent) {
      // 将高亮元素替换回原始文本
      parent.replaceChild(document.createTextNode(el.textContent), el);
      // 合并相邻的文本节点
      parent.normalize();
    }
  });
  highlightElements.value = [];
};

// 执行搜索
const handleSearch = () => {
  if (!searchText.value) {
    clearHighlights();
    matchCount.value = 0;
    currentMatchIndex.value = 0;
    return;
  }

  // 添加防抖
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  searchTimeout = setTimeout(() => {
    nextTick(() => {
      performSearch();
    });
  }, props.searchDelay);
};

// 执行搜索逻辑
const performSearch = () => {
  clearHighlights();

  if (!searchText.value.trim()) {
    matchCount.value = 0;
    currentMatchIndex.value = 0;
    return;
  }

  const container = document.querySelector(props.container);
  if (!container) return;

  const searchRegex = new RegExp(escapeRegExp(searchText.value), "gi");
  searchResults.value = [];
  highlightElements.value = [];

  // 递归搜索文本节点
  searchTextNodes(container, searchRegex);

  matchCount.value = highlightElements.value.length;
  currentMatchIndex.value = matchCount.value > 0 ? 1 : 0;

  if (matchCount.value > 0) {
    highlightCurrentMatch();
  }

  emit("search", {
    text: searchText.value,
    count: matchCount.value,
  });
};

// 转义正则表达式特殊字符
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

// 递归搜索文本节点
const searchTextNodes = (node, regex) => {
  // 如果节点不存在，直接返回
  if (!node) return;

  if (node.nodeType === 3) {
    // 文本节点
    const content = node.nodeValue;
    // 重置正则表达式的lastIndex
    regex.lastIndex = 0;
    if (content && content.trim() && regex.test(content)) {
      highlightTextNode(node, regex);
    }
  } else if (
    node.nodeType === 1 &&
    node.nodeName !== "SCRIPT" &&
    node.nodeName !== "STYLE" &&
    node.nodeName !== "NOSCRIPT" &&
    node.nodeName !== "IFRAME" &&
    !node.classList.contains("page-search-highlight")
  ) {
    // 元素节点
    try {
      // 检查元素是否可见
      const style = window.getComputedStyle(node);
      if (
        style.display !== "none" &&
        style.visibility !== "hidden" &&
        style.opacity !== "0"
      ) {
        // 创建一个副本以避免在迭代过程中修改集合
        const childNodes = Array.from(node.childNodes);
        for (let i = 0; i < childNodes.length; i++) {
          searchTextNodes(childNodes[i], regex);
        }
      }
    } catch (e) {
      console.error("Error checking node visibility:", e);
    }
  }
};

// 高亮文本节点中的匹配内容
const highlightTextNode = (textNode, regex) => {
  const content = textNode.nodeValue;
  const parent = textNode.parentNode;

  if (!parent) return;

  // 重置正则表达式的lastIndex
  regex.lastIndex = 0;

  let match;
  let lastIndex = 0;
  const fragment = document.createDocumentFragment();

  while ((match = regex.exec(content)) !== null) {
    // 添加匹配前的文本
    if (match.index > lastIndex) {
      fragment.appendChild(
        document.createTextNode(content.substring(lastIndex, match.index))
      );
    }

    // 创建高亮元素
    const highlightEl = document.createElement("span");
    highlightEl.className = "page-search-highlight";
    highlightEl.style.backgroundColor = props.highlightColor;
    highlightEl.style.borderRadius = "2px";
    highlightEl.style.padding = "0 2px";
    highlightEl.textContent = match[0];

    fragment.appendChild(highlightEl);
    highlightElements.value.push(highlightEl);

    lastIndex = regex.lastIndex;
  }

  // 添加剩余文本
  if (lastIndex < content.length) {
    fragment.appendChild(document.createTextNode(content.substring(lastIndex)));
  }

  // 替换原始文本节点
  try {
    parent.replaceChild(fragment, textNode);
  } catch (e) {
    console.error("Error replacing text node:", e);
  }
};

// 高亮当前匹配项
const highlightCurrentMatch = () => {
  // 恢复所有高亮为默认颜色
  highlightElements.value.forEach((el) => {
    el.style.backgroundColor = props.highlightColor;
    el.style.color = "inherit";
  });

  // 高亮当前匹配项
  const currentEl = highlightElements.value[currentMatchIndex.value - 1];
  if (currentEl) {
    currentEl.style.backgroundColor = props.activeHighlightColor;
    currentEl.style.color = "#ffffff";
    currentEl.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }
};

// 查找下一个匹配项
const findNext = () => {
  if (matchCount.value === 0) return;

  currentMatchIndex.value = (currentMatchIndex.value % matchCount.value) + 1;
  highlightCurrentMatch();
};

// 查找上一个匹配项
const findPrev = () => {
  if (matchCount.value === 0) return;

  currentMatchIndex.value =
    currentMatchIndex.value <= 1
      ? matchCount.value
      : currentMatchIndex.value - 1;

  highlightCurrentMatch();
};

// 键盘快捷键处理
const handleKeyDown = (e) => {
  // F3键查找下一个
  if (e.key === "F3") {
    e.preventDefault();
    findNext();
  }

  // Shift+F3键查找上一个
  if (e.shiftKey && e.key === "F3") {
    e.preventDefault();
    findPrev();
  }
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("keydown", handleKeyDown);
  nextTick(() => {
    if (!isCollapsed.value && searchInputRef.value) {
      searchInputRef.value.focus();
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("keydown", handleKeyDown);
  clearHighlights();
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
});

// 暴露方法给父组件
defineExpose({
  focus: () => {
    if (searchInputRef.value) {
      searchInputRef.value.focus();
    }
  },
  clear: close,
  findNext,
  findPrev,
  collapse: () => {
    isCollapsed.value = true;
    emit("collapse");
  },
  expand: () => {
    isCollapsed.value = false;
    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus();
      }
    });
    emit("expand");
  },
  toggleCollapse,
});
</script>

<style scoped>
.page-search-container {
  position: relative;
  position: fixed;

  right: 50px;
  z-index: 999;
}

.search-expanded {
  width: 320px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 5px;
  background-color: #f5f7fa;
}

.search-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.expand-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.expand-button:hover {
  transform: scale(1.05);
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) inset !important;
  padding-right: 0;
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
}

.search-input :deep(.el-input__wrapper):focus-within {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
}

.search-icon {
  color: var(--el-color-primary);
  margin-right: 4px;
}

.search-actions {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  height: 100%;
  border-left: 1px solid #e4e7ed;
}

.match-count {
  margin: 0 8px;
  font-size: 13px;
  color: #606266;
  min-width: 50px;
  text-align: center;
  font-weight: 500;
}

.match-count.no-match {
  color: #f56c6c;
}

:deep(.el-input-group__append) {
  padding: 0;
  overflow: hidden;
  background-color: #f5f7fa;
}

.nav-button {
  color: var(--el-color-primary);
  border: none;
  padding: 8px;
  margin: 0;
  height: 32px;
  width: 32px;
  border-radius: 0;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.nav-button:hover {
  background-color: #e6e8eb;
}

.nav-button:disabled {
  color: #c0c4cc;
  background-color: transparent;
  cursor: not-allowed;
}

.close-button {
  color: #909399;
  border: none;
  padding: 8px;
  margin: 0;
  height: 32px;
  width: 32px;
  border-radius: 0;
  background-color: transparent;
  border-left: 1px solid #dcdfe6;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: #f56c6c;
  background-color: #e6e8eb;
}

:deep(.el-button .el-icon) {
  font-size: 14px;
}

/* 动画效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
</style>
