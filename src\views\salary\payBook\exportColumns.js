export default [
  { label: "编号", property: "code" },
  { label: "姓名", property: "name" },
  { label: "部门", property: "department" },
  { label: "身份证号", property: "idNumber" },
  { label: "银行账号", property: "bankAccount" },
  { label: "性别", property: "gender" },
  { label: "民族", property: "ethnicity" },
  { label: "岗位", property: "position" },
  { label: "薪酬类别", property: "salaryCategory" },
  { label: "工龄工资标准", property: "seniorityStandard" },
  { label: "学龄工资标准", property: "academicAgeStandard" },
  { label: "年功工资（公式计算值）", property: "annualMeritPay" },
  { label: "岗位系数", property: "positionCoefficient" },
  { label: "岗位工资标准", property: "positionSalaryStandard" },
  { label: "岗位工资", property: "positionSalary" },
  { label: "效益工资标准", property: "performanceStandard" },
  { label: "效益工资", property: "performancePay" },
  { label: "加班工资", property: "overtimePay" },
  { label: "技术（技能）津贴标准", property: "technicalAllowanceStandard" },
  { label: "技术（技能）津贴", property: "technicalAllowance" },
  { label: "持证津贴标准", property: "certificationAllowanceStandard" },
  { label: "持证津贴", property: "certificationAllowance" },
  { label: "流动津贴标准", property: "mobilityAllowanceStandard" },
  { label: "流动津贴", property: "mobilityAllowance" },
  { label: "中夜津贴", property: "nightShiftAllowance" },
  { label: "驻外生活补贴标准", property: "expatLivingStandard" },
  { label: "驻外生活补贴", property: "expatLivingAllowance" },
  { label: "见习生活补贴标准", property: "traineeLivingStandard" },
  { label: "见习生活补贴", property: "traineeLivingAllowance" },
  { label: "隧道津贴标准", property: "tunnelAllowanceStandard" },
  { label: "隧道津贴", property: "tunnelAllowance" },
  { label: "艰苦边远地区生活补贴标准", property: "remoteAllowanceStandard" },
  { label: "艰苦边远地区生活补贴", property: "remoteAllowance" },
  { label: "回族生活补贴", property: "huiEthnicityAllowance" },
  { label: "专家人员津贴标准", property: "expertAllowanceStandard" },
  { label: "专家人员津贴", property: "expertAllowance" },
  { label: "工班长津贴标准", property: "foremanAllowanceStandard" },
  { label: "工班长津贴", property: "foremanAllowance" },
  {
    label: "台车长、盾构/TBM主司机津贴标准",
    property: "tbmDriverAllowanceStandard",
  },
  { label: "台车长、盾构/TBM主司机津贴", property: "tbmDriverAllowance" },
  {
    label: "政治指导员委派津贴标准",
    property: "politicalInstructorAllowanceStandard",
  },
  { label: "政治指导员委派津贴", property: "politicalInstructorAllowance" },
  {
    label: "兼职团组书记津贴标准",
    property: "partTimeSecretaryAllowanceStandard",
  },
  { label: "兼职团组书记津贴", property: "partTimeSecretaryAllowance" },
  { label: "其他津贴", property: "otherAllowances" },
  { label: "女工费", property: "femaleWorkerFee" },
  { label: "生产奖金", property: "productionBonus" },
  { label: "绩效工资系数", property: "performanceCoefficient" },
  { label: "绩效工资标准", property: "performanceSalaryStandard" },
  { label: "绩效工资", property: "performanceSalary" },
  { label: "激励考核奖", property: "incentiveBonus" },
  { label: "年度绩效考核奖", property: "annualPerformanceBonus" },
  { label: "一次性奖励", property: "oneTimeBonus" },
  { label: "房租补贴标准", property: "housingSubsidyStandard" },
  { label: "交通补贴标准", property: "transportationSubsidy" },
  { label: "话费补贴", property: "phoneSubsidy" },
  { label: "电脑补贴", property: "computerSubsidy" },
  { label: "防暑降温标准", property: "heatReliefStandard" },
  { label: "基本年薪标准", property: "baseSalaryStandard" },
  { label: "基本年薪", property: "baseSalary" },
  { label: "绩效年薪", property: "performanceBonusTotal" },
  { label: "特殊奖励", property: "specialAwards" },
  { label: "非在岗人员生活费", property: "inactiveLivingAllowance" },
  { label: "其他收入", property: "otherIncome" },
  { label: "应发工资", property: "grossPay" },
  { label: "个人所得税（输入）", property: "inputTax" },
  { label: "养老保险", property: "pensionInsurance" },
  { label: "医疗保险", property: "medicalInsurance" },
  { label: "失业保险", property: "unemploymentInsurance" },
  { label: "住房公积金", property: "housingFund" },
  { label: "企业年金", property: "enterpriseAnnuity" },
  { label: "大额医疗保险", property: "criticalIllnessInsurance" },
  { label: "物管费", property: "propertyFee" },
  { label: "房租", property: "rent" },
  { label: "水电费", property: "utilities" },
  { label: "备用金", property: "pettyCash" },
  { label: "其他应扣", property: "otherDeductions" },
  { label: "实发工资", property: "netPay" },
  { label: "合并扣税款项", property: "mergedTaxItems" },
  { label: "个税预扣实缴", property: "prepaidTax" },
  { label: "制度工天", property: "systemWorkingDays" },
  { label: "法定工天", property: "legalWorkingDays" },
  { label: "出勤天数", property: "attendanceDays" },
  { label: "周末加班", property: "weekendOvertime" },
  { label: "节日工天", property: "holidayWorkingDays" },
  { label: "中班工天", property: "midShiftDays" },
  { label: "夜班工天", property: "nightShiftDays" },
  { label: "年休工天", property: "annualLeaveDays" },
  { label: "进洞工天", property: "tunnelWorkingDays" },
  { label: "婚假工天", property: "marriageLeaveDays" },
  { label: "工伤工天", property: "injuryLeaveDays" },
  { label: "产假工天", property: "maternityLeaveDays" },
  { label: "看护假工天", property: "nursingLeaveDays" },
  { label: "丧假工天", property: "bereavementLeaveDays" },
  { label: "探亲工天", property: "familyVisitDays" },
  { label: "长期病假工天", property: "longTermSickLeave" },
  { label: "零星病假工天", property: "shortTermSickLeave" },
  { label: "病假时段", property: "sickLeavePeriod" },
  { label: "个税预交", property: "prepaidTaxFlag" },
];
