<template>
  <div>
    <!-- 插槽区域 -->
    <div @click="handleClick">
      <slot></slot>
    </div>
    <!-- 预览弹窗 -->

    <el-dialog
      v-model="dialogVisible"
      title=""
      draggable
      append-to-body
      fullscreen
    >
      <div class="m-[20px]">
        <iframe
          v-loading="loading"
          :src="previewUrl"
          frameborder="0"
          class="w-full iframe"
          @load="handleLoad"
        ></iframe>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, watch } from "vue";
import { encode } from "js-base64";
const props = defineProps({
  url: String,
});
const loading = ref(true);
// 文件地址
const fileUrl = ref(props.url);
// 预览地址
const previewUrl = ref(
  `/preview/onlinePreview?url=${encodeURIComponent(encode(fileUrl.value))}`
);

// 弹窗是否显示
const dialogVisible = ref(false);
// 监听url变化
watch(
  () => props.url,
  (newValue) => {
    fileUrl.value = newValue;
    previewUrl.value = `/preview/onlinePreview?url=${encodeURIComponent(
      encode(fileUrl.value)
    )}`;
  }
);
// 监听弹窗打开时，重置 loading
watch(dialogVisible, (val) => {
  if (val) {
    loading.value = true;
  }
});
// iframe 加载完成后，设置 loading 为 false
const handleLoad = () => {
  loading.value = false;
};
const handleClick = () => {
  window.open(previewUrl.value, "_blank");
};
</script>

<style scoped>
.iframe {
  height: calc(100vh - 80px);
}
</style>
