<template>
  <div class="query-form-card">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="时间" prop="yearMonth" style="margin-bottom: 0">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          placeholder="选择年月"
          style="width: 200px"
          format="YYYY-MM"
          value-format="YYYY-MM"
        />
      </el-form-item>

      <el-form-item label="单位名称" prop="projectId" style="margin-bottom: 0">
        <RemoteSelect
          v-model="queryParams.projectId"
          url="/system/dept/list"
          labelKey="deptName"
          valueKey="deptId"
          responsePath="data"
          placeholder="请选择单位名称"
          clearable
          class="w-[200px]"
          :extraParams="{ parentId: '0' }"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item style="margin-bottom: 0">
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button class="reset-btn" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
// 字典数据
const { personnel_type, construction_status, organization_attribute } =
  proxy.useDict(
    "personnel_type",
    "construction_status",
    "organization_attribute"
  );

const emit = defineEmits(["search"]);
const queryParams = ref({});
const orgQueryParams = ref({});
/** 搜索按钮操作 */
function handleQuery() {
  emit("search", queryParams.value);
}
/** 单位变更操作 */
function handleProjectChange(project) {
  if (project && project.deptId) {
    queryParams.value.deptId = undefined; // 重置部门ID
    orgQueryParams.value = { parentId: project.deptId }; // 更新部门查询参数
  } else {
    queryParams.value.projectId = undefined;
    queryParams.value.deptId = undefined;
    orgQueryParams.value = {};
  }
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
</script>
<style lang="scss" scoped>
.query-form-card {
  background-color: #fff;
  padding: 20px;
}
</style>
