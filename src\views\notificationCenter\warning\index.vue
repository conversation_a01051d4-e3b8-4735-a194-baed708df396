<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="内容" prop="content">
            <el-input
              class="w-[200px]"
              v-model="queryParams.content"
              placeholder="请输入内容"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item label="查看状态" prop="isRead">
            <el-select
              class="w-[200px]"
              v-model="queryParams.isRead"
              placeholder="请选择状态"
              clearable
            >
              <el-option
                v-for="dict in notice_read_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleReadAll"
              >一键已读</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="noticeList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="内容" align="center" prop="content" />
          <el-table-column label="消息类型" align="center" prop="warnType">
          </el-table-column>
          <el-table-column label="查看状态" align="center" prop="isRead">
            <template #default="scope">
              <dict-tag
                :options="notice_read_status"
                :value="scope.row.isRead"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="发布时间"
            align="center"
            prop="createTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.isRead === 0"
                type="text"
                @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                v-if="scope.row.isRead === 1"
                type="text"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 查看系统通知对话框 -->
    <el-dialog title="查看通知" v-model="viewOpen" width="800px" append-to-body>
      <div class="notice-view">
        <div class="notice-info">
          <span>类型：{{ viewForm.warnType }}</span>
          <span>发布时间：{{ parseTime(viewForm.createTime) }}</span>
        </div>
        <div class="notice-content" v-html="viewForm.content"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { parseTime } from '@/utils/welink';
import {
  listWarningNotice,
  delWarningNotice,
  readWarningNotice,
  readAllNotice,
} from '@/api/notificationCenter/notice';

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_notice_type, notice_read_status } = proxy.useDict(
  'sys_notice_type',
  'notice_read_status'
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 系统通知表格数据
const noticeList = ref([]);
// 是否显示查看弹出层
const viewOpen = ref(false);
// 查看表单
const viewForm = ref({});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  content: undefined,
  warnType: undefined,
  isRead: undefined,
});

/** 查询系统通知列表 */
function getList() {
  loading.value = true;
  listWarningNotice(queryParams.value).then((response) => {
    noticeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 查看按钮操作 */
function handleView(row) {
  viewForm.value = row;
  viewOpen.value = true;
  // 如果是未读状态，则标记为已读
  if (row.isRead === 0) {
    readWarningNotice(row.id).then(() => {
      getList();
    });
  }
}

/** 一键已读按钮操作 */
function handleReadAll() {
  proxy.$modal
    .confirm('确认将所有未读通知标记为已读?')
    .then(() => {
      readAllNotice().then(() => {
        getList();
        proxy.$modal.msgSuccess('操作成功');
      });
    })
    .catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const noticeIds = row.id;
  proxy.$modal
    .confirm('是否确认删除系统通知编号为"' + noticeIds + '"的数据项?')
    .then(function () {
      return delWarningNotice(noticeIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.notice-view {
  padding: 20px;
}
.notice-title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 15px;
}
.notice-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
.notice-content {
  min-height: 200px;
}
</style>
