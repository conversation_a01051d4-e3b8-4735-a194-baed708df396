<template>
  <view>
    <table border class="custom-table" pro>
      <slot></slot>
    </table>
  </view>
</template>

<script setup>
import { provide, toRefs } from "vue";
const props = defineProps({
  labelWidth: {
    type: String,
    default: () => "",
  },
  formRef: {
    type: Object,
    default: () => {},
  },
});
// 提供响应式数据，使用toRefs确保props的变化能够响应式传播
provide("formContext", {
  ...toRefs(props),
});
</script>

<style lang="scss" scoped>
.custom-table {
  border-collapse: collapse;
  border-color: #e6e6e6;
  width: 100%;
  table-layout: auto;
}
</style>
