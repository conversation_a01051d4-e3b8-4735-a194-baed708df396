<template>
  <div class="basic-info">
    <!-- 骨架框加载效果 -->
    <SkeletonLoader
      v-if="isLoading"
      layout="form"
      :show-search="true"
      :show-photo="true"
      :form-rows="16"
      :form-cols="4"
      :show-buttons="true"
    />

    <!-- 实际内容 -->
    <div v-else>
      <div class="flex justify-between mb-4">
        <PageSearch container="#searchContainer"></PageSearch>
      </div>
      <el-form
        id="searchContainer"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="0"
      >
        <div
          class="rounded-tl-md rounded-tr-md w-full font-bold pl-3 py-2 border border-b-0 border-[#e6e6e6] border-solid"
        >
          人员信息
        </div>
        <table-form label-width="110px" :formRef="formRef">
          <tr>
            <table-td label="姓名：" width="240px" required>
              <el-form-item prop="name">
                <el-input v-model="form.name" />
              </el-form-item>
            </table-td>
            <table-td
              label="员工编号："
              width="240px"
              required
              :disabled="form.id"
            >
              <el-form-item prop="staffCode">
                <el-input v-model="form.staffCode" />
              </el-form-item>
            </table-td>
            <table-td label="曾用名：" width="240px">
              <el-form-item prop="formerName">
                <el-input v-model="form.formerName" />
              </el-form-item>
            </table-td>
            <table-td label="出生日期：" width="240px" required>
              <el-form-item prop="birthDate">
                <el-date-picker
                  v-model="form.birthDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td rowspan="5" width="150px" height="100%">
              <el-form-item>
                <image-upload
                  v-model="form.photo"
                  size="100%"
                  height="180px"
                  :isShowTip="false"
                  :limit="1"
                >
                  <p>上传照片</p>
                </image-upload>
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="出生地：">
              <el-form-item prop="birthPlace">
                <el-input v-model="form.birthPlace" />
              </el-form-item>
            </table-td>
            <table-td label="民族：">
              <el-form-item prop="ethnicity">
                <el-input v-model="form.ethnicity" />
              </el-form-item>
            </table-td>
            <table-td label="性别：">
              <el-form-item prop="gender">
                <el-select
                  v-model="form.gender"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in sys_user_sex"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="身份证号：" required>
              <el-form-item prop="idNumber">
                <el-input v-model="form.idNumber" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="婚姻状况：">
              <el-form-item prop="maritalStatus">
                <el-select
                  v-model="form.maritalStatus"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in marital_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="健康状况：">
              <el-form-item prop="healthStatus">
                <el-select
                  v-model="form.healthStatus"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in health_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="国家地区：">
              <el-form-item prop="countryRegion">
                <el-input v-model="form.countryRegion" />
              </el-form-item>
            </table-td>
            <table-td label="籍贯：">
              <el-form-item prop="hometown">
                <el-input v-model="form.hometown" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="户口性质：">
              <el-form-item prop="householdType">
                <el-select
                  v-model="form.householdType"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in household_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="最高学历：" required>
              <el-form-item prop="highestEducation">
                <el-select
                  v-model="form.highestEducation"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in education_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="政治面貌：">
              <el-form-item prop="politicalStatus">
                <el-select
                  v-model="form.politicalStatus"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in political_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="户口所在地：">
              <el-form-item prop="householdLocation">
                <el-input v-model="form.householdLocation" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="入党(入团)日期：">
              <el-form-item prop="partyJoinDate">
                <el-date-picker
                  v-model="form.partyJoinDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="人员类型：" required>
              <el-form-item prop="staffType">
                <el-select
                  v-model="form.staffType"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in personnel_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="进入中铁时间：" required>
              <el-form-item prop="joinCrscDate">
                <el-date-picker
                  popper-class="custom-table-popper"
                  v-model="form.joinCrscDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
            <table-td label="年龄：">
              <el-form-item prop="age">
                <el-input-number
                  :controls="false"
                  v-model="form.age"
                  :min="0"
                  :max="150"
                  style="width: 100%"
                  disabled
                />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="工龄：">
              <el-form-item prop="workYears">
                <el-input-number
                  :controls="false"
                  v-model="form.workYears"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
            <table-td label="调整工龄：">
              <el-form-item prop="adjustedWorkYears">
                <el-input-number
                  :controls="false"
                  v-model="form.adjustedWorkYears"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
            <table-td label="联系电话：" required>
              <el-form-item prop="phone">
                <el-input v-model="form.phone" />
              </el-form-item>
            </table-td>
            <table-td label="家庭电话：" colspan="2">
              <el-form-item prop="homePhone">
                <el-input v-model="form.homePhone" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="家庭住址：">
              <el-form-item prop="homeAddress">
                <el-input v-model="form.homeAddress" />
              </el-form-item>
            </table-td>
            <table-td label="现在居住地址：">
              <el-form-item prop="currentAddress">
                <el-input v-model="form.currentAddress" />
              </el-form-item>
            </table-td>
            <table-td label="转干时间：">
              <el-form-item prop="cadreDate">
                <el-date-picker
                  v-model="form.cadreDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="所在单位：" colspan="2" required>
              <el-form-item prop="projectId">
                <RemoteSelect
                  v-model="form.projectId"
                  v-model:modelName="form.projectName"
                  url="/system/dept/list"
                  labelKey="deptName"
                  valueKey="deptId"
                  responsePath="data"
                  :extraParams="{ parentId: '0' }"
                  @change="handleProjectChange"
                  popper-class="custom-table-popper"
                ></RemoteSelect>
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="二级机构：">
              <el-form-item prop="deptId">
                <RemoteSelect
                  v-model="form.deptId"
                  v-model:modelName="form.deptName"
                  url="/system/dept/list"
                  labelKey="deptName"
                  valueKey="deptId"
                  responsePath="data"
                  :extraParams="{ parentId: 9999 }"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="提职日期：">
              <el-form-item prop="extractDate">
                <el-date-picker
                  v-model="form.extractDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="到本单位时间：">
              <el-form-item prop="joinUnitDate">
                <el-date-picker
                  v-model="form.joinUnitDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="学龄：" colspan="2">
              <el-form-item prop="schoolingYears">
                <el-input-number
                  :controls="false"
                  v-model="form.schoolingYears"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="职称聘任时间：">
              <el-form-item prop="professionalTitleDate">
                <el-date-picker
                  v-model="form.professionalTitleDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="技能鉴定工种：" required>
              <el-form-item prop="skillIdentification">
                <el-select
                  v-model="form.skillIdentification"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in identify_job_types"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="社保身份：" required>
              <el-form-item prop="socialSecurityStatus">
                <el-select
                  v-model="form.socialSecurityStatus"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in social_security_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="岗位：" colspan="2" required>
              <el-form-item prop="postId">
                <RemoteSelect
                  v-model="form.postId"
                  url="/system/post/list"
                  labelKey="postName"
                  valueKey="postId"
                  popper-class="custom-table-popper"
                ></RemoteSelect>
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="从事工作：">
              <el-form-item prop="jobContent">
                <el-select
                  v-model="form.jobContent"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in work_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="专业技术职务：" required>
              <el-form-item prop="professionalTitle">
                <el-select
                  v-model="form.professionalTitle"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in professional_title"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="工人技术等级：">
              <el-form-item prop="workerSkillLevel">
                <el-select
                  v-model="form.workerSkillLevel"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in worker_skill_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="职务职别：" colspan="2">
              <el-form-item prop="positionLevel">
                <el-select
                  v-model="form.positionLevel"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in position_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="进入来源：" required>
              <el-form-item prop="entrySource">
                <el-select
                  v-model="form.entrySource"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in entry_source"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="岗位状态：" required>
              <el-form-item prop="positionStatus">
                <el-select
                  v-model="form.positionStatus"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in position_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="现从事专业：">
              <el-form-item prop="currentProfession">
                <el-input v-model="form.currentProfession" />
              </el-form-item>
            </table-td>
            <table-td label="职称分类：" colspan="2">
              <el-form-item prop="titleCategory">
                <el-select
                  v-model="form.titleCategory"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in title_category"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="个人性格：">
              <el-form-item prop="personality">
                <el-select
                  v-model="form.personality"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in personality_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="职业性格：">
              <el-form-item prop="professionalPersonality">
                <el-select
                  v-model="form.professionalPersonality"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in professional_personality"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="业务能力评价：">
              <el-form-item prop="businessAbilityEvaluation">
                <el-select
                  v-model="form.businessAbilityEvaluation"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in business_ability_evaluation"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="工作状态评价：" colspan="2">
              <el-form-item prop="workStatusEvaluation">
                <el-select
                  v-model="form.workStatusEvaluation"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in work_status_evaluation"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="档案是否在册：">
              <el-form-item prop="archived">
                <el-select
                  v-model="form.archived"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="档案号：">
              <el-form-item prop="archiveNumber">
                <el-input v-model="form.archiveNumber" />
              </el-form-item>
            </table-td>
            <table-td label="档案年龄：">
              <el-form-item prop="archiveAge">
                <el-input-number
                  :controls="false"
                  v-model="form.archiveAge"
                  :min="0"
                  :max="150"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
            <table-td label="档案类别：" colspan="2">
              <el-form-item prop="archiveCategory">
                <el-input v-model="form.archiveCategory" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="档案所在地：">
              <el-form-item prop="archiveLocation">
                <el-input v-model="form.archiveLocation" />
              </el-form-item>
            </table-td>
            <table-td label="档案管理单位：">
              <el-form-item prop="archiveManagementUnit">
                <el-input v-model="form.archiveManagementUnit" />
              </el-form-item>
            </table-td>
            <table-td label="是否列入鸿鹄计划：">
              <el-form-item prop="honggouPlan">
                <el-select
                  v-model="form.honggouPlan"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="非在岗时间：" colspan="2">
              <el-form-item prop="offlineDate">
                <el-date-picker
                  v-model="form.offlineDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="非在岗原因：">
              <el-form-item prop="offlineReason">
                <el-input v-model="form.offlineReason" />
              </el-form-item>
            </table-td>
            <table-td label="第一学历：">
              <el-form-item prop="firstEducation">
                <el-select
                  v-model="form.firstEducation"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in education_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="第一学历毕业院校：">
              <el-form-item prop="firstEducationSchool">
                <el-input v-model="form.firstEducationSchool" />
              </el-form-item>
            </table-td>
            <table-td label="第一学历毕业专业：" colspan="2">
              <el-form-item prop="firstEducationMajor">
                <el-input v-model="form.firstEducationMajor" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="第一学历毕业时间：">
              <el-form-item prop="firstEducationGraduationDate">
                <el-date-picker
                  v-model="form.firstEducationGraduationDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="院校类别：">
              <el-form-item prop="firstEducationSchoolType">
                <el-select
                  v-model="form.firstEducationSchoolType"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in school_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="最高学历毕业学校：">
              <el-form-item prop="highestEducationSchool">
                <el-input v-model="form.highestEducationSchool" />
              </el-form-item>
            </table-td>
            <table-td label="最高学历毕业专业：" colspan="2">
              <el-form-item prop="highestEducationMajor">
                <el-input v-model="form.highestEducationMajor" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="最高学历毕业时间：">
              <el-form-item prop="highestEducationGraduationDate">
                <el-date-picker
                  v-model="form.highestEducationGraduationDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="是否为专家：">
              <el-form-item prop="isExpert">
                <el-select
                  v-model="form.isExpert"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                  clearable
                >
                  <el-option
                    v-for="dict in sys_yes_no"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </table-td>
            <table-td label="专家名称：">
              <el-form-item prop="expertTitle">
                <el-input v-model="form.expertTitle" />
              </el-form-item>
            </table-td>
            <table-td label="专家级别：" colspan="2">
              <el-form-item prop="expertLevel">
                <el-input v-model="form.expertLevel" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="专家类别：">
              <el-form-item prop="expertCategory">
                <el-input v-model="form.expertCategory" />
              </el-form-item>
            </table-td>
            <table-td label="现职时间：">
              <el-form-item prop="currentPositionDate">
                <el-date-picker
                  v-model="form.currentPositionDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                  popper-class="custom-table-popper"
                />
              </el-form-item>
            </table-td>
            <table-td label="提现/职级年限：">
              <el-form-item prop="positionPromotionYears">
                <el-input-number
                  :controls="false"
                  v-model="form.positionPromotionYears"
                  :min="0"
                  :max="100"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
            <table-td label="详细描述：" colspan="2">
              <el-form-item prop="detailedDescription">
                <el-input
                  v-model="form.detailedDescription"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="用工单位：">
              <el-form-item prop="employers">
                <el-input v-model="form.employers" />
              </el-form-item>
            </table-td>
            <table-td label="工资标准(元)：">
              <el-form-item prop="wageScale">
                <el-input-number
                  :controls="false"
                  v-model="form.wageScale"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </el-form-item>
            </table-td>
            <table-td label="自建班组名称：">
              <el-form-item prop="selfTeam">
                <el-input v-model="form.selfTeam" />
              </el-form-item>
            </table-td>
            <table-td label="班组负责人：" colspan="2">
              <el-form-item prop="teamHead">
                <el-input v-model="form.teamHead" />
              </el-form-item>
            </table-td>
          </tr>
          <tr>
            <table-td label="组建时间周期段：">
              <el-form-item prop="formationCycle">
                <el-input v-model="form.formationCycle" />
              </el-form-item>
            </table-td>
            <table-td label="班组从事工作内容：" colspan="7">
              <el-form-item prop="teamWorkContent">
                <el-input v-model="form.teamWorkContent" />
              </el-form-item>
            </table-td>
          </tr>
        </table-form>
        <div
          class="rounded-bl-md rounded-br-md w-full py-2 border border-t-0 border-[#e6e6e6] border-solid"
        >
          <div class="flex justify-center w-full">
            <el-button class="save-btn" type="primary" @click="cancel"
              >返 回</el-button
            >
            <el-button
              v-if="route.query.type !== 'check'"
              class="save-btn"
              type="primary"
              :loading="loading"
              @click="submitForm"
              >保存配置</el-button
            >
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import {
  getEmployee,
  addEmployee,
  auditEmployee,
} from '@/api/personnelFiles/incumbentEmployee';
import staffFormDict from '../staffFormDict';
const route = useRoute();
const router = useRouter();
import PageSearch from '@/components/PageSearch/index.vue';
import SkeletonLoader from '@/components/SkeletonLoader/index.vue';
const { proxy } = getCurrentInstance();

// 加载状态
const isLoading = ref(true);

// 字典数据
const {
  sys_user_sex,
  social_security_status,
  personnel_type,
  education_type,
  sys_yes_no,
  work_type,
  marital_status,
  health_status,
  household_type,
  political_status,
  identify_job_types,
  professional_title,
  worker_skill_level,
  position_level,
  entry_source,
  position_status,
  title_category,
  personality_type,
  professional_personality,
  business_ability_evaluation,
  work_status_evaluation,
  school_type,
} = proxy.useDict(
  'sys_user_sex',
  'social_security_status',
  'personnel_type',
  'education_type',
  'sys_yes_no',
  'work_type',
  'marital_status',
  'health_status',
  'household_type',
  'political_status',
  'identify_job_types',
  'professional_title',
  'worker_skill_level',
  'position_level',
  'entry_source',
  'position_status',
  'title_category',
  'personality_type',
  'professional_personality',
  'business_ability_evaluation',
  'work_status_evaluation',
  'school_type'
);

const formRef = ref();
const oldForm = ref({});
const form = ref({});
const loading = ref(false); // 添加loading状态

// 表单校验规则
const rules = ref({
  professionalTitle: [
    {
      required: true,
      message: '请选择专业技术职务',
      trigger: 'change',
    },
  ],
  projectId: [{ required: true, message: '请选择所在单位', trigger: 'change' }],
  gender: [{ required: true, message: '性别不能为空', trigger: 'change' }],
  birthDate: [
    { required: true, message: '出生日期不能为空', trigger: 'change' },
  ],
  postId: [{ required: true, message: '岗位不能为空', trigger: 'change' }],
  highestEducation: [
    { required: true, message: '最高学历不能为空', trigger: 'change' },
  ],
  name: [{ required: true, message: '姓名不能为空', trigger: 'change' }],

  staffCode: [
    { required: true, message: '员工编号不能为空', trigger: 'change' },
  ],
  idNumber: [
    { required: true, message: '身份证号不能为空', trigger: 'change' },
  ],
  phone: [{ required: true, message: '联系电话不能为空', trigger: 'change' }],
  staffType: [
    { required: true, message: '人员类型不能为空', trigger: 'change' },
  ],
  socialSecurityStatus: [
    { required: true, message: '社保身份不能为空', trigger: 'change' },
  ],
  skillIdentification: [
    { required: true, message: '技能鉴定不能为空', trigger: 'change' },
  ],
  entrySource: [
    { required: true, message: '进入来源不能为空', trigger: 'change' },
  ],
  positionStatus: [
    { required: true, message: '岗位状态不能为空', trigger: 'change' },
  ],
  deptId: [{ required: true, message: '二级机构不能为空', trigger: 'change' }],
  joinCrscDate: [
    { required: true, message: '进入中铁时间不能为空', trigger: 'change' },
  ],
});

/** 查询员工详细 */
function getInfo(id) {
  isLoading.value = true;
  getEmployee(id)
    .then((response) => {
      form.value = response.data;
      oldForm.value = JSON.parse(JSON.stringify(response.data));
    })
    .finally(() => {
      isLoading.value = false;
    });
}

/** 项目单位变更操作 */
function handleProjectChange(project) {
  if (project && project.deptId) {
    form.value.deptId = undefined; // 重置机构ID
    form.value.deptName = undefined;
  } else {
    form.value.projectId = undefined;
    form.value.deptId = undefined;
    form.value.deptName = undefined;
  }
}

// 获取更新前更新后的差异字段信息
function getDiffField(oldData, newData) {
  const diffFieldArr = [];
  for (const key in oldData) {
    if (oldData[key] !== newData[key]) {
      diffFieldArr.push(staffFormDict[key]);
    }
  }
  return diffFieldArr.join(',');
}
// 编辑前重组数据
function getEditData() {
  const diffFieldSrt = getDiffField(oldForm.value, form.value);
  const editData = {
    description: diffFieldSrt,
    flowVO: {
      templateId: '10008',
    },
    newStaff: JSON.stringify(form.value),
    oldStaff: JSON.stringify(oldForm.value),
    staffId: form.value.id,
  };
  return editData;
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;
        if (form.value.id) {
          // 如果是编辑，需要审核
          const data = getEditData();
          // 如果没有变更任何东西
          if (!data.description) {
            proxy.$modal.msgError('没有需要审核的变更信息');
            return;
          }
          await auditEmployee(data);
          proxy.$modal.msgSuccess('操作成功');
          cancel();
        } else {
          await addEmployee(form.value);
          proxy.$modal.msgSuccess('新增成功');
          cancel();
        }
      } finally {
        loading.value = false;
      }
    }
  });
}
/** 返回按钮 */
function goBack() {
  router.go(-1);
} /** 取消按钮 */
function cancel() {
  router.replace('/personnelFiles/incumbentEmployee');
}

// 计算年龄的函数
function calculateAge(birthDate) {
  if (!birthDate) return null;
  const birth = new Date(birthDate);
  const today = new Date();

  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();

  // 如果还没到生日，年龄减1
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }

  return age >= 0 ? age : null;
}

// 监听出生日期变化，自动计算年龄
watch(
  () => form.value.birthDate,
  (newBirthDate) => {
    form.value.age = calculateAge(newBirthDate);
  },
  { immediate: true }
);

onMounted(() => {
  if (route.query.id) {
    getInfo(route.query.id);
  } else {
    // 新建时也显示短暂的加载效果
    isLoading.value = true;
    setTimeout(() => {
      isLoading.value = false;
    }, 300);
  }
});
</script>

<style scoped lang="scss">
.el-row {
  margin-bottom: 20px;
}
</style>

<style scoped>
div :deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}
.save-btn {
  border-color: #e6e6e6;
  background-color: #fff;
  color: #444;
  border-radius: 6px;
}
.save-btn:hover {
  border-color: #76b4ac;
  color: #007465;
  background-color: #edf9f7;
}
</style>
