<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="项目" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择项目"
              clearable
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item label="审核状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择审核状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in audit_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="applyList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目名称" align="center" prop="projectName" />
          <el-table-column
            label="项目负责人"
            align="center"
            prop="leaderUserName"
          />
          <el-table-column
            label="办公室负责人"
            align="center"
            prop="officeLeader"
          />
          <el-table-column label="负责人联系方式" align="center" prop="phone" />
          <el-table-column label="需求人数" align="center" prop="staffNum" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag :options="audit_status" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" />
          <el-table-column
            label="操作"
            align="center"
            width="250"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="text"
                @click="handleSubmitAudit(scope.row)"
                v-if="scope.row.status === '0'"
                >提审</el-button
              >
              <el-button type="text" @click="handleInfo(scope.row)"
                >查看</el-button
              >
              <el-button
                type="text"
                @click="handleUpdate(scope.row)"
                v-if="scope.row.status === '0'"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-if="scope.row.status === '0'"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import RemoteSelect from "@/components/RemoteSelect";
import {
  listProjectStaffApply,
  delProjectStaffApply,
  submitAudit,
} from "@/api/personnelFiles/projectStaffApply";
const { proxy } = getCurrentInstance();
const router = useRouter();
// 字典数据
const { audit_status } = proxy.useDict("audit_status");

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 项目人员申请列表数据
const applyList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  status: undefined,
});

/** 查询项目人员申请列表 */
function getList() {
  loading.value = true;
  listProjectStaffApply(queryParams.value).then((response) => {
    applyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  router.push("/personnelFiles/projectStaffApply/addOrEdit");
}

/** 查看按钮操作 */
function handleInfo(row) {
  proxy.getProcessRouterPath(row, "10009", row.id);
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  router.push({
    path: "/personnelFiles/projectStaffApply/addOrEdit",
    query: { id: row.id },
  });
}

/** 提交审核按钮操作 */
function handleSubmitAudit(row) {
  const data = {
    bizId: row.id,
    templateId: "10009",
  };
  proxy.$modal
    .confirm('是否确认提交"' + row.projectName + '"的申请?')
    .then(function () {
      return submitAudit(data);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("提交成功");
    })
    .catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id;
  proxy.$modal
    .confirm('是否确认删除"' + row.projectName + '"的申请?')
    .then(function () {
      return delProjectStaffApply(id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "railway/wlProjectApply/export",
    {
      ...queryParams.value,
    },
    `项目人员申请_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
