<template>
  <div class="title-info">
    <div class="flex justify-between mb-4">
      <p class="font-bold text-xl">职称信息</p>
    </div>

    <div v-if="titleList.length === 0" class="info-content">
      <Empty text="暂无职称信息" />
    </div>
    <div v-for="(item, index) in titleList" :key="index">
      <div class="indexTitle">
        <span>职称{{ numberToChinese(index + 1) }}</span>
      </div>
      <div class="info-content">
        <div class="title-block">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span class="label">职称名称：</span>
                <span>{{ item.technicalPosition || "--" }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span class="label">技术资格获得时间：</span>
                <span>{{ item.executeDate || "--" }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span class="label">技术职务聘用时间：</span>
                <span>{{ item.executeDate || "--" }}</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span class="label">关联发文：</span>
                <el-button
                  @click="handleView(item.dispatchId)"
                  v-if="item.title"
                  link
                  type="primary"
                  >{{ item.title }}</el-button
                >
                <span v-else>--</span>
              </div>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item">
                <span class="label">附件：</span>
                <div v-if="item.wlAnnexes && item.wlAnnexes.length > 0">
                  <span v-for="(item, index) in item.wlAnnexes" :key="index">
                    <div class="elLink" @click="downloadFile(item.path)">
                      {{ index + 1 }}、{{ item.name }}
                    </div>
                  </span>
                </div>
                <span v-else>--</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item full-width">
                <span class="label">详细描述：</span>
                <span v-html="item.content || '--'"></span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getTitleInfo"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { getWlStaffInfoPost } from "@/api/personnelFiles/incumbentEmployee";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const query = ref({});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(0);

const titleList = ref([]);

// 数字转中文
function numberToChinese(num) {
  var chinese = "";
  var unit = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  var digit = ["", "十", "百", "千", "万", "亿"];

  var numStr = num.toString();
  var len = numStr.length;
  for (var i = 0; i < len; i++) {
    var currentNum = parseInt(numStr[i]);
    chinese += unit[currentNum] + digit[len - i - 1];
  }
  return chinese;
}

/** 查询职称信息 */
function getTitleInfo(employeeId) {
  getWlStaffInfoPost(employeeId, queryParams.value).then((response) => {
    if (response.code === 200) {
      titleList.value = response.rows || [];
      total.value = response.total || 0;
      /* if (titleList.value.length === 0) {
        // 测试数据，实际使用时应移除
   
        titleList.value = [{
          staffName: "张三",
          idNumber: "110101199001011234",
          gender: "0",
          executeDate: "2020-01-01",
          title: "关于XX职称的发文",
          technicalPosition: "高级工程师",
          technicalPosition: "中级工程师",
          projectName: "测试项目",
          engageWork: "js",
          createBy: "admin",
          createTime: "2020-01-01 12:00:00",
          updateBy: "admin",
          updateTime: "2020-01-02 12:00:00",
          dispatchId: "",
          content: "详细描述信息",
          remark: "备注信息",
          wlAnnexes:[
            {name:'444',path:'8888'},
            {name:'444',path:'8888'}
          ]
        }];
    total.value = 1
      } */
    }
  });
}

/** 查看关联发文 */
function handleView(id) {
  if (!id) return;
  router.push({
    path: "/documentCenter/dispatchArticle/addOrEdit",
    query: {
      id: id,
      mode: "view",
    },
  });
}

/** 下载附件 */
function downloadFile(path) {
  if (path) {
    window.open(path);
  }
}

onMounted(() => {
  query.value = route.query;
  // 获取员工ID
  const employeeId = route.query.id;
  if (employeeId) {
    getTitleInfo(employeeId);
  }
});
</script>

<style scoped lang="scss">
.title-info {
  padding: 20px;
}

.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
}

.title-block {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #d9d9d9;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

.el-row {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 140px;
    text-align: right;
    color: #606266;
    padding-right: 10px;
  }
}

.full-width {
  width: 100%;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}

.indexTitle {
  margin: 20px 0;
  span {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #2674fe;
    background-color: #e6efff;
    padding: 10px 20px;
    border-radius: 4px;
  }
}
.elLink {
  cursor: pointer;
}
.elLink:hover {
  color: #409eff;
}

@media screen and (max-width: 768px) {
  .info-item .label {
    min-width: 110px;
  }
}
</style>
