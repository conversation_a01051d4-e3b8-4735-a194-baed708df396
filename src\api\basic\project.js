import request from "@/utils/request";

// 查询项目列表
export function listProject(query) {
  return request({
    url: "/railway/wlProject/list",
    method: "get",
    params: query,
  });
}

// 查询项目详细
export function getProject(id) {
  return request({
    url: "/railway/wlProject/" + id,
    method: "get",
  });
}

// 新增项目
export function addProject(data) {
  return request({
    url: "/railway/wlProject",
    method: "post",
    data: data,
  });
}

// 编辑项目
export function updateProject(data) {
  return request({
    url: "/railway/wlProject",
    method: "put",
    data: data,
  });
}

// 删除项目
export function delProject(id) {
  return request({
    url: "/railway/wlProject/" + id,
    method: "delete",
  });
}

// 查询项目机构列表
export function listProjectOrg(query) {
  return request({
    url: "/railway/wlProjectOrg/list",
    method: "get",
    params: query,
  });
}

// 查询项目机构详细
export function getProjectOrg(id) {
  return request({
    url: "/railway/wlProjectOrg/" + id,
    method: "get",
  });
}

// 新增项目机构
export function addProjectOrg(data) {
  return request({
    url: "/railway/wlProjectOrg",
    method: "post",
    data: data,
  });
}

// 编辑项目机构
export function updateProjectOrg(data) {
  return request({
    url: "/railway/wlProjectOrg",
    method: "put",
    data: data,
  });
}

// 删除项目机构
export function delProjectOrg(id) {
  return request({
    url: "/railway/wlProjectOrg/" + id,
    method: "delete",
  });
}
