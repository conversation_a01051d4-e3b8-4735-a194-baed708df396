<template>
  <div>
    <div v-if="nodeInfo && nodeInfo.processInstanceNodeList?.length > 0">
      <p class="font-bold">{{ nodeInfo.instanceName || "审批流程" }}</p>
      <!-- 使用封装的审批流程节点组件 -->
      <process-nodes :nodeInfo="nodeInfo" />
    </div>
    <!-- 有审批权限并且状态是待审才显示 -->
    <div v-if="nodeInfo.authority === 'A' && nodeInfo.status === '1'">
      <p class="font-bold">审核</p>
      <!-- 表单 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="mt-4"
      >
        <el-form-item label="审核状态" prop="operationType">
          <el-radio-group v-model="form.operationType">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">驳回</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- <el-form-item label="附件">
        <file-upload
          v-model="form.files"
          :limit="1"
          :isShowTip="true"
          :fileSize="10"
        />
      </el-form-item> -->

        <el-form-item label="审核意见" prop="instruction">
          <el-input
            v-model="form.instruction"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="submitForm"
            >提交</el-button
          >
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import FileUpload from "@/components/FileUpload";
import ProcessNodes from "@/components/ProcessNodes";
import { auditProcess } from "@/api/process/audit";

const { proxy } = getCurrentInstance();
const router = useRouter();
const props = defineProps({
  // 节点信息
  nodeInfo: {
    type: Object,
    required: true,
    default: () => {
      return { processInstanceNodeList: [] };
    },
  },
});

const formRef = ref(null);
const emit = defineEmits(["submit"]);
const loading = ref(false);

// 表单数据
const form = reactive({
  instanceId: props.nodeInfo.instanceId,
  operationType: 1, // 默认通过
  files: [],
  instruction: "",
});

// 表单校验规则
const rules = {
  operationType: [
    { required: true, message: "请选择审核状态", trigger: "change" },
  ],
  files: [{ required: true, message: "请上传附件", trigger: "change" }],
  instruction: [
    { required: true, message: "请输入审核意见", trigger: "blur" },
    { min: 2, max: 500, message: "长度在 2 到 500 个字符", trigger: "blur" },
  ],
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      const submitData = {
        ...form,
        instanceId: props.nodeInfo.instanceId,
      };
      emit("submit", submitData);
      auditProcess(submitData)
        .then((res) => {
          proxy.$modal.msgSuccess("审核成功");
          router.go(-1);
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
};
</script>

<style scoped>
.mt-4 {
  margin-top: 16px;
}
</style>
