<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
      <div class="filter-container">
        <div style="display: flex; align-items: center; margin-right: 20px">
          <div>年龄段：</div>
          <el-select
            v-model="yearFilter"
            placeholder="请选择年龄段"
            style="width: 190px"
            clearable
            @change="handleFullYearFilter"
          >
            <el-option label="24岁以下" value="0" />
            <el-option label="24-30岁" value="1" />
            <el-option label="31-40岁" value="2" />
            <el-option label="41-50岁" value="3" />
            <el-option label="51-55岁" value="4" />
            <el-option label="56岁以上" value="5" />
          </el-select>
        </div>
        <div style="display: flex; align-items: center">
          <div>从事工作：</div>
          <el-select
            v-model="departmentFilter"
            placeholder="请选择从事工作"
            style="width: 190px"
            clearable
            @change="handleDepartmentFilter"
          >
            <el-option
              v-for="item in work_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="chart"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from "vue";
import * as echarts from "echarts/core";
import { BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
} from "echarts/components";
import { LabelLayout, UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
const { proxy } = getCurrentInstance();
const { work_type } = proxy.useDict("work_type");
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  BarChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

const emit = defineEmits(["change"]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});

const chartRef = ref(null);
const yearFilter = ref("");
const departmentFilter = ref("");
let chart = null;
const params = ref({});
const colors = [
  ["#FB7293", "#FB7293"], // 粉色系
  ["#FFA06C", "#FFA06C"], // 蓝色系
  ["#08C3D2", "#08C3D2"], // 绿色系
  ["#8378EA", "#8378EA"], // 黄色系
  ["#67E0E3", "#67E0E3"], // 橙色系
  ["#9FE6B8", "#9FE6B8"], // 紫色系
];

const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    // legend: {
    //   data: ['技术工作', '管理工作', '其他工作'],
    //   right: 10,
    //   top: 0,
    // },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "40px",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: props.chartData.categories,
        axisLabel: {
          interval: 0,
          fontSize: 12,
          color: "#333",
          margin: 12,
          rotate: 45,
        },
        axisTick: {
          alignWithLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: "#E4E7ED",
          },
        },
      },
    ],
    yAxis: [
      {
        minInterval: 1,
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#E4E7ED",
          },
        },
        axisLabel: {
          color: "#333",
        },
      },
    ],
    series: [
      {
        name: "24岁以下",
        type: "bar",
        barWidth: 5,
        barGap: 1,
        itemStyle: {
          color: colors[0][0],
        },
        emphasis: {
          itemStyle: {
            color: colors[0][1],
          },
        },
        data: props.chartData.data?.[0]?.data,
      },
      {
        name: "24-30岁",
        type: "bar",
        barWidth: 5,
        barGap: 1,
        itemStyle: {
          color: colors[1][0],
        },
        emphasis: {
          itemStyle: {
            color: colors[1][1],
          },
        },
        data: props.chartData.data?.[1]?.data,
      },
      {
        name: "31-40岁",
        type: "bar",
        barWidth: 5,
        barGap: 1,
        itemStyle: {
          color: colors[2][0],
        },
        emphasis: {
          itemStyle: {
            color: colors[2][1],
          },
        },
        data: props.chartData.data?.[2]?.data,
      },
      {
        name: "41-50岁",
        type: "bar",
        barWidth: 5,
        barGap: 1,
        itemStyle: {
          color: colors[3][0],
        },
        emphasis: {
          itemStyle: {
            color: colors[3][1],
          },
        },
        data: props.chartData.data?.[3]?.data,
      },
      {
        name: "51-55岁",
        type: "bar",
        barWidth: 5,
        barGap: 1,
        itemStyle: {
          color: colors[4][0],
        },
        emphasis: {
          itemStyle: {
            color: colors[4][1],
          },
        },
        data: props.chartData.data?.[4]?.data,
      },
      {
        name: "56岁以上",
        type: "bar",
        barWidth: 5,
        barGap: 1,
        itemStyle: {
          color: colors[5][0],
        },
        emphasis: {
          itemStyle: {
            color: colors[5][1],
          },
        },
        data: props.chartData.data?.[5]?.data,
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart && chart.resize();
  });
};
function handleFullYearFilter(value) {
  params.value.ageGroup = value;
  emit("change", params.value);
}
function handleDepartmentFilter(value) {
  params.value.jobContent = value;
  emit("change", params.value);
}
watch(
  () => props.chartData,
  () => {
    chart && chart.dispose();
    initChart();
  },
  { deep: true }
);

watch([yearFilter, departmentFilter], () => {
  // 可以在这里添加过滤逻辑
  // 根据过滤条件重新获取数据，更新图表
});

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.filter-container {
  display: flex;
  align-items: center;
}

.chart {
  width: 100%;
  height: 400px;
}

:deep(.el-select) {
  margin-left: 8px;
}

:deep(.el-input__wrapper) {
  background-color: #f5f6f8;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}
</style>
