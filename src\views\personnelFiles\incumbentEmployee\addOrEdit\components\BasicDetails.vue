<template>
  <div class="basic-info">
    <div class="flex justify-between">
      <p class="font-bold text-xl">人员信息</p>
      <PageSearch container="#searchContainer"></PageSearch>
    </div>
    <div class="info-content" id="searchContainer">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="8">
          <div class="info-item">
            <span class="label">照片：</span>
            <image-upload
              v-model="info.photo"
              size="70px"
              :isShowTip="false"
              :limit="1"
              disabled
            />
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span>{{ info.name || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">员工编号：</span>
            <span>{{ info.staffCode || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">曾用名：</span>
            <span>{{ info.formerName || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">出生日期：</span>
            <span>{{ info.birthDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">出生地：</span>
            <span>{{ info.birthPlace || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">民族：</span>
            <span>{{ info.ethnicity || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">性别：</span>
            <span>{{ getDictLabel(sys_user_sex, info.gender) || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">身份证号：</span>
            <span>{{ info.idNumber || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">婚姻状况：</span>
            <span>{{
              getDictLabel(marital_status, info.maritalStatus) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">健康状况：</span>
            <span>{{
              getDictLabel(health_status, info.healthStatus) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">国家地区：</span>
            <span>{{ info.countryRegion || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">籍贯：</span>
            <span>{{ info.hometown || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">户口性质：</span>
            <span>{{
              getDictLabel(household_type, info.householdType) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">最高学历：</span>
            <span>{{
              getDictLabel(education_type, info.highestEducation) || "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">政治面貌：</span>
            <span>{{
              getDictLabel(political_status, info.politicalStatus) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">户口所在地：</span>
            <span>{{ info.householdLocation || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">入党(入团)日期：</span>
            <span>{{ info.partyJoinDate || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">人员类型：</span>
            <span>{{
              getDictLabel(personnel_type, info.staffType) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">进入中铁时间：</span>
            <span>{{ info.joinCrscDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">年龄：</span>
            <span>{{ info.age || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">工龄：</span>
            <span>{{ info.workYears || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">调整工龄：</span>
            <span>{{ info.adjustedWorkYears || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span>{{ info.phone || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">家庭电话：</span>
            <span>{{ info.homePhone || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">家庭住址：</span>
            <span>{{ info.homeAddress || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">现在居住地址：</span>
            <span>{{ info.currentAddress || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">转干时间：</span>
            <span>{{ info.cadreDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">所在单位：</span>
            <span>{{ info.projectName || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">现从事专业：</span>
            <span>{{ info.currentProfession || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">提职日期：</span>
            <span>{{ info.extractDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">到本单位时间：</span>
            <span>{{ info.joinUnitDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">学龄：</span>
            <span>{{ info.schoolingYears || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">职称聘任时间：</span>
            <span>{{ info.professionalTitleDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">技能鉴定工种：</span>
            <span>{{
              getDictLabel(identify_job_types, info.skillIdentification) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">社保身份：</span>
            <span>{{
              getDictLabel(social_security_status, info.socialSecurityStatus) ||
              "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">岗位：</span>
            <span>{{ info.postName || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">从事工作：</span>
            <span>{{ getDictLabel(work_type, info.jobContent) || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">专业技术职务：</span>
            <span>{{
              getDictLabel(professional_title, info.professionalTitle) || "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">工人技术等级：</span>
            <span>{{
              getDictLabel(worker_skill_level, info.workerSkillLevel) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">职务职别：</span>
            <span>{{
              getDictLabel(position_level, info.positionLevel) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">进入来源：</span>
            <span>{{
              getDictLabel(entry_source, info.entrySource) || "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">岗位状态：</span>
            <span>{{
              getDictLabel(position_status, info.positionStatus) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">二级机构：</span>
            <span>{{ info.deptName || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">职称分类：</span>
            <span>{{
              getDictLabel(title_category, info.titleCategory) || "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">个人性格：</span>
            <span>{{
              getDictLabel(personality_type, info.personality) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">职业性格：</span>
            <span>{{
              getDictLabel(
                professional_personality,
                info.professionalPersonality
              ) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">业务能力评价：</span>
            <span>{{
              getDictLabel(
                business_ability_evaluation,
                info.businessAbilityEvaluation
              ) || "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">工作状态评价：</span>
            <span>{{
              getDictLabel(work_status_evaluation, info.workStatusEvaluation) ||
              "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">档案是否在册：</span>
            <span>{{ getDictLabel(sys_yes_no, info.archived) || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">档案号：</span>
            <span>{{ info.archiveNumber || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">档案年龄：</span>
            <span>{{ info.archiveAge || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">档案类别：</span>
            <span>{{ info.archiveCategory || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">档案所在地：</span>
            <span>{{ info.archiveLocation || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">档案管理单位：</span>
            <span>{{ info.archiveManagementUnit || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">是否列入鸿鹄计划：</span>
            <span>{{
              getDictLabel(sys_yes_no, info.honggouPlan) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">非在岗时间：</span>
            <span>{{ info.offlineDate || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">非在岗原因：</span>
            <span>{{ info.offlineReason || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">第一学历：</span>
            <span>{{
              getDictLabel(education_type, info.firstEducation) || "--"
            }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">第一学历毕业院校：</span>
            <span>{{ info.firstEducationSchool || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">第一学历毕业专业：</span>
            <span>{{ info.firstEducationMajor || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">第一学历毕业时间：</span>
            <span>{{ info.firstEducationGraduationDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">院校类别：</span>
            <span>{{
              getDictLabel(school_type, info.firstEducationSchoolType) || "--"
            }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">最高学历毕业学校：</span>
            <span>{{ info.highestEducationSchool || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">最高学历毕业专业：</span>
            <span>{{ info.highestEducationMajor || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">最高学历毕业时间：</span>
            <span>{{ info.highestEducationGraduationDate || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">是否为专家：</span>
            <span>{{ getDictLabel(sys_yes_no, info.isExpert) || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">专家名称：</span>
            <span>{{ info.expertTitle || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">专家级别：</span>
            <span>{{ info.expertLevel || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">专家类别：</span>
            <span>{{ info.expertCategory || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">现职时间：</span>
            <span>{{ info.currentPositionDate || "--" }}</span>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="label">提现/职级年限：</span>
            <span>{{ info.positionPromotionYears || "--" }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="24">
          <div class="info-item full-width">
            <span class="label">详细描述：</span>
            <span>{{ info.detailedDescription || "--" }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getEmployee } from "@/api/personnelFiles/incumbentEmployee";
import { useRoute, useRouter } from "vue-router";
import { getCurrentInstance } from "vue";
const route = useRoute();
const router = useRouter();
import PageSearch from "@/components/PageSearch/index.vue";
const { proxy } = getCurrentInstance();
import { getBusinessIdByInstanceId } from "@/api/process/audit";
// 字典数据
const {
  sys_user_sex,
  social_security_status,
  personnel_type,
  education_type,
  sys_yes_no,
  work_type,
  marital_status,
  health_status,
  household_type,
  political_status,

  department_type,
  identify_job_types,
  professional_title,
  worker_skill_level,
  position_level,
  entry_source,
  position_status,
  secondary_organization,
  title_category,
  personality_type,
  professional_personality,
  business_ability_evaluation,
  work_status_evaluation,
  school_type,
} = proxy.useDict(
  "sys_user_sex",
  "social_security_status",
  "personnel_type",
  "education_type",
  "sys_yes_no",
  "work_type",
  "marital_status",
  "health_status",
  "household_type",
  "political_status",
  "department_type",
  "identify_job_types",
  "professional_title",
  "worker_skill_level",
  "position_level",
  "entry_source",
  "position_status",
  "secondary_organization",
  "title_category",
  "personality_type",
  "professional_personality",
  "business_ability_evaluation",
  "work_status_evaluation",
  "school_type"
);

const info = ref({});

// 获取字典标签
function getDictLabel(dict, value) {
  const item = dict.find((d) => d.value === value);
  return item ? item.label : "";
}

/** 查询员工详细 */
function getInfo(id) {
  getEmployee(id).then((response) => {
    info.value = response.data;
  });
}

/** 返回按钮 */
function goBack() {
  router.go(-1);
}

onMounted(async () => {
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  if (instanceId) {
    const res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
  }
  if (id) {
    getInfo(id);
  }
});
</script>

<style scoped lang="scss">
.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
}

.el-row {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 140px;
    text-align: right;
    color: #606266;
    padding-right: 10px;
  }
}

.full-width {
  width: 100%;
}

@media screen and (max-width: 768px) {
  .info-item .label {
    min-width: 110px;
  }
}
</style>
