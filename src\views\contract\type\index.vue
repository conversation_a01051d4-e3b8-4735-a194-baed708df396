<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="类型名称" prop="typeName">
            <el-input
              class="w-[200px]"
              v-model="queryParams.typeName"
              placeholder="请输入类型名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="typeList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="类型名称" align="center" prop="typeName" />
          <el-table-column label="排序" align="center" prop="sort" />
          <!-- <el-table-column label="附件" align="center" prop="fileUrl">
            <template #default="scope">
              <el-link
                v-if="scope.row.fileUrl"
                @click="handleDownload(scope.row.fileUrl)"
                :underline="false"
                type="primary"
              >
                {{ getShortFileName(scope.row.fileUrl) }}
              </el-link>
              <span v-else>无</span>
            </template>
          </el-table-column> -->
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="160"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑合同类型对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="typeForm" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="form.typeName" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="附件" prop="fileUrl">
          <file-upload
            v-model="form.fileUrl"
            :file-size="60"
            :file-type="['docx', 'pdf']"
          />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { parseTime } from "@/utils/welink";
import FileUpload from "@/components/FileUpload/index.vue";
import {
  listContractType,
  getContractType,
  addContractType,
  updateContractType,
  delContractType,
  downloadContractTypeFile,
} from "@/api/contract/type";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 合同类型表格数据
const typeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  typeName: undefined,
  status: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  typeName: undefined,
  sort: 0,
  status: "0",
  fileUrl: undefined,
});

// 表单校验
const rules = ref({
  typeName: [{ required: true, message: "类型名称不能为空", trigger: "blur" }],
  sort: [{ required: true, message: "排序不能为空", trigger: "blur" }],
  fileUrl: [{ required: true, message: "请上传附件", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "blur" }],
});

/** 查询合同类型列表 */
function getList() {
  loading.value = true;
  listContractType(queryParams.value).then((response) => {
    typeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    typeName: undefined,
    sort: 0,
    status: "0",
    fileUrl: undefined,
  };
  proxy.resetForm("typeForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 获取短文件名 */
function getShortFileName(url) {
  if (!url) return "";
  // 提取文件名
  const fileName = url.split("/").pop();
  // 如果文件名太长，截取显示
  if (fileName.length > 20) {
    return (
      fileName.substring(0, 10) +
      "..." +
      fileName.substring(fileName.length - 10)
    );
  }
  return fileName;
}

/** 下载附件 */
function handleDownload(fileUrl) {
  downloadContractTypeFile(fileUrl).then((response) => {
    const blob = new Blob([response]);
    const fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();
    URL.revokeObjectURL(link.href);
  });
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加合同类型";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || row.typeId;
  getContractType(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑合同类型";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["typeForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateContractType(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addContractType(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || row.typeId;
  proxy.$modal
    .confirm('是否确认删除合同类型编号为"' + ids + '"的数据项?')
    .then(function () {
      return delContractType(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  getList();
});
</script>
