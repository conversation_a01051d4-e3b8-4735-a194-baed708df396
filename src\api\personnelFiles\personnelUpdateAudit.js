import request from "@/utils/request";

// 查询人员审核列表
export function listStaffAudit(query) {
  return request({
    url: "/railway/wlStaffUpdate/list",
    method: "get",
    params: query,
  });
}

// 查询人员审核详细
export function getStaffAudit(id) {
  return request({
    url: "/railway/wlStaffUpdate/" + id,
    method: "get",
  });
}

// 处理审核
export function handleAudit(data) {
  return request({
    url: "/railway/wlStaffUpdate/audit",
    method: "put",
    data: data,
  });
}
