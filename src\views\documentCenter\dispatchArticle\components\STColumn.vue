<template>
  <el-table-column
    label="现项目从事工种"
    prop="skillIdentification"
    width="200"
    align="center"
  >
    <template #default="slotScope">
      <dict-tag
        :options="identify_job_types"
        :value="slotScope.row.skillIdentification"
      />
    </template>
  </el-table-column>

  <el-table-column label="鉴定工种" prop="workRace" width="200" align="center">
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.workRace"
        placeholder="请选择从事工种"
      >
        <el-option
          v-for="dict in identify_job_types"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag
        v-else
        :options="identify_job_types"
        :value="slotScope.row.workRace"
      />
    </template>
  </el-table-column>
  <el-table-column label="类别" prop="workType" width="200" align="center">
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.workType"
        placeholder="请选择类别"
      >
        <el-option
          v-for="dict in title_category"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag
        v-else
        :options="title_category"
        :value="slotScope.row.workType"
      />
    </template>
  </el-table-column>

  <el-table-column label="鉴定等级" prop="workGrade" width="200" align="center">
    <template #default="slotScope">
      <el-select
        v-if="!viewMode"
        v-model="slotScope.row.workGrade"
        placeholder="请选择鉴定等级"
      >
        <el-option
          v-for="dict in worker_skill_level"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
      <dict-tag
        v-else
        :options="worker_skill_level"
        :value="slotScope.row.workGrade"
      />
    </template>
  </el-table-column>
  <el-table-column
    label="岗位系数"
    prop="postFactor"
    width="150"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.postFactor"
        placeholder="请输入岗位系数"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.postFactor }}</span>
    </template>
  </el-table-column>
  <el-table-column
    label="岗位工资标准(元)"
    prop="postSalary"
    width="180"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.postSalary"
        placeholder="请输入岗位工资标准"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.postSalary }}</span>
    </template>
  </el-table-column>
  <el-table-column
    label="技术津贴标准(元)"
    prop="postSubsidy"
    width="180"
    align="center"
  >
    <template #default="slotScope">
      <el-input-number
        v-if="!viewMode"
        style="width: 100%"
        v-model="slotScope.row.postSubsidy"
        placeholder="请输入技术津贴标准"
        :min="0"
        :controls="false"
      />
      <span v-else>{{ slotScope.row.postSubsidy }}</span>
    </template>
  </el-table-column>

  <el-table-column label="备注" prop="remark" width="200" align="center">
    <template #default="slotScope">
      <el-input
        v-if="!viewMode"
        v-model="slotScope.row.remark"
        placeholder="请输入备注"
      />
      <span v-else>{{ slotScope.row.remark }}</span>
    </template>
  </el-table-column>
</template>

<script setup>
import { getCurrentInstance } from "vue";

defineProps({
  viewMode: {
    type: Boolean,
    default: false,
  },
});

const { proxy } = getCurrentInstance();
const { title_category, worker_skill_level, identify_job_types } =
  proxy.useDict("title_category", "worker_skill_level", "identify_job_types");
</script>
