<template>
  <div class="new-instrument-container">
    <CustomTabs v-model="activeMainTab" @tab-click="handleMainTabClick">
      <TabPane label="人力资源数据分析" name="dataAnalysis">
        <ExistingIndex />
      </TabPane>
      <TabPane label="人员动态面板" name="dynamicPanel">
        <DynamicPanel />
      </TabPane>
    </CustomTabs>
  </div>
</template>

<script setup>
import { ref } from "vue";
import CustomTabs from "@/components/CustomTabs/index.vue";
import TabPane from "@/components/CustomTabs/TabPane.vue";
import ExistingIndex from "./existingIndex.vue";
import DynamicPanel from "./dynamicPanel/index.vue";

const activeMainTab = ref("dataAnalysis");

const handleMainTabClick = (pane, event) => {};
</script>

<style scoped>
.new-instrument-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}
</style>
