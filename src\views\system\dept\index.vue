<template>
  <div class="px-4">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="项目单位" name="dept"></el-tab-pane>
      <el-tab-pane label="二级机构" name="secondary"></el-tab-pane>
    </el-tabs>
    <Dept v-if="activeTab === 'dept'"></Dept>
    <Secondary v-if="activeTab === 'secondary'"></Secondary>
  </div>
</template>
<script setup>
import Dept from './dept.vue';
import Secondary from './secondary.vue';

const activeTab = ref('dept');
</script>
