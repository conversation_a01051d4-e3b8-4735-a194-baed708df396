import request from "@/utils/request";

// 查询证书类型列表
export function listType(query) {
  return request({
    url: "/railway/wlType/list",
    method: "get",
    params: { ...query, type: "ZS" },
  });
}

// 查询证书类型详细
export function getType(id) {
  return request({
    url: "/railway/wlType/" + id,
    method: "get",
  });
}

// 新增证书类型
export function addType(data) {
  return request({
    url: "/railway/wlType",
    method: "post",
    data: { ...data, type: "ZS" },
  });
}

// 编辑证书类型
export function updateType(data) {
  return request({
    url: "/railway/wlType",
    method: "put",
    data: { ...data, type: "ZS" },
  });
}

// 删除证书类型
export function delType(id) {
  return request({
    url: "/railway/wlType/" + id,
    method: "delete",
  });
}
