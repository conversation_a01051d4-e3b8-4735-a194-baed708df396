<template>
  <div class="app-container">
    <el-card>
      <div class="basic-info">
        <div class="flex justify-between">
          <p class="font-bold">项目人员申请审核</p>
        </div>

        <div class="info-content">
          <!-- 项目基本信息 -->
          <div class="info-section">
            <p class="section-title">项目基本信息</p>
            <div class="info-row">
              <div class="info-item">
                <span class="label">项目名称：</span>
                <span>{{ info.projectName || "--" }}</span>
              </div>
              <div class="info-item">
                <span class="label">项目负责人：</span>
                <span>{{ info.projectLeader || "--" }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">办公室负责人：</span>
                <span>{{ info.officeLeader || "--" }}</span>
              </div>
              <div class="info-item">
                <span class="label">联系方式：</span>
                <span>{{ info.phone || "--" }}</span>
              </div>
            </div>
          </div>

          <!-- 项目人员需求详情 -->
          <div class="info-section">
            <p class="section-title">项目人员需求详细信息</p>
            <el-table :data="info.staffList || []" border stripe>
              <el-table-column
                label="序号"
                type="index"
                width="80"
                align="center"
              />
              <el-table-column
                label="需求岗位"
                align="center"
                prop="postName"
              />
              <el-table-column
                label="岗位工资"
                align="center"
                prop="postSalary"
              />
              <el-table-column
                label="需求人数"
                align="center"
                prop="peopleNumber"
              />
              <el-table-column label="性别" align="center" prop="postSex">
                <template #default="scope">
                  <dict-tag
                    :options="sys_user_sex"
                    :value="scope.row.postSex"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="最迟进场时间"
                align="center"
                prop="entryTime"
              />
              <el-table-column
                label="职业性格"
                align="center"
                prop="careerCharacter"
              >
                <template #default="scope">
                  <dict-tag
                    :options="professional_personality"
                    :value="scope.row.careerCharacter"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="个人性格"
                align="center"
                prop="personCharacter"
              >
                <template #default="scope">
                  <dict-tag
                    :options="personality_type"
                    :value="scope.row.personCharacter"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="业务能力评价"
                align="center"
                prop="abilityEvaluate"
              >
                <template #default="scope">
                  <dict-tag
                    :options="business_ability_evaluation"
                    :value="scope.row.abilityEvaluate"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="工作状态评价"
                align="center"
                prop="statusEvaluate"
              >
                <template #default="scope">
                  <dict-tag
                    :options="work_status_evaluation"
                    :value="scope.row.statusEvaluate"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="需求原因简述"
                align="center"
                prop="resume"
              />
              <el-table-column label="备注" align="center" prop="remark">
                <template #default="scope">
                  {{ scope.row.remark || "--" }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 审核组件 -->
        <ProcessComponent :nodeInfo="nodeInfo" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getCurrentInstance } from "vue";
import ProcessComponent from "@/components/ProcessComponent";
import { getProjectStaffApply } from "@/api/personnelFiles/projectStaffApply";
import { getBusinessIdByInstanceId } from "@/api/process/audit";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

// 节点信息
const nodeInfo = ref({});
// 项目申请信息
const info = ref({});

// 字典数据
const {
  sys_user_sex,
  professional_personality,
  personality_type,
  business_ability_evaluation,
  work_status_evaluation,
} = proxy.useDict(
  "sys_user_sex",
  "professional_personality",
  "personality_type",
  "business_ability_evaluation",
  "work_status_evaluation"
);

/** 查询项目申请详细 */
function getInfo(id) {
  getProjectStaffApply(id).then((response) => {
    info.value = response.data;
  });
}

/** 返回按钮 */
function goBack() {
  router.replace("/process/processAudit");
}

onMounted(async () => {
  // 如果是实例ID则先根据实例ID获取
  const instanceId = route.query.instanceId;
  let id = route.query.id;
  if (instanceId) {
    const res = await getBusinessIdByInstanceId(instanceId);
    id = res.data?.processInstanceBiz?.bizId;
    nodeInfo.value = res.data;
  }
  if (id) {
    getInfo(id);
  }
});
</script>

<style scoped lang="scss">
.info-content {
  background-color: #f3f7fc;
  padding: 20px;
  margin-bottom: 20px;
}

.info-section {
  margin-bottom: 30px;

  .section-title {
    font-weight: bold;
    margin-bottom: 15px;
    padding-left: 10px;
    border-left: 4px solid #1890ff;
  }
}

.info-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: flex-start;

  .label {
    min-width: 120px;
    text-align: right;
    color: #606266;
    margin-right: 10px;
  }
}
</style>
