<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
      <div class="tab-group">
        <el-radio-group v-model="tabActive">
          <el-radio-button label="趋势图"></el-radio-button>
          <el-radio-button label="列表"></el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div v-if="tabActive === '趋势图'" ref="chartRef" class="chart"></div>
    <div v-else-if="tabActive === '列表'" class="table-container">
      <el-table
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{
          background: '#F5F6F8',
          color: '#333',
          borderColor: '#E4E7ED',
        }"
        :cell-style="{
          color: '#333',
        }"
        height="250"
      >
        <el-table-column prop="name" label="用户名" align="center" />
        <el-table-column
          prop="idNumber"
          label="身份证号"
          align="center"
          width="180"
        />
        <el-table-column prop="gender" label="性别" align="center">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column prop="age" label="档案年龄" align="center" />
        <el-table-column prop="retireDate" label="预计退休时间" align="center">
          <template #default="scope">
            {{ parseTime(scope.row.retireDate, "{y}-{m}-{d}") }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pageNum"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :small="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-card>
</template>

<script setup>
import {
  ref,
  onMounted,
  watch,
  defineProps,
  nextTick,
  getCurrentInstance,
} from "vue";
import * as echarts from "echarts/core";
import { LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from "echarts/components";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { getRetireListData } from "@/api/instrument";
const { proxy } = getCurrentInstance();
const { sys_user_sex } = proxy.useDict("sys_user_sex");
import { parseTime } from "@/utils/welink";

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
  LineChart,
  UniversalTransition,
  CanvasRenderer,
]);
defineExpose({
  getList,
});
const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
  queryParams: {
    type: Object,
    required: true,
  },
});

const chartRef = ref(null);
const tabActive = ref("趋势图");
let chart = null;
const total = ref(0);
// 表格数据
const tableData = ref([]);
// 分页相关
const pageNum = ref(1);
const pageSize = ref(10);

const handleSizeChange = (val) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val) => {
  pageNum.value = val;
  getList();
};

/**
 * 获取列表数据
 */
function getList() {
  getRetireListData({
    ...props.queryParams,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }).then((res) => {
    total.value = res.total;
    tableData.value = res.rows;
  });
}
const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);
  const series = props.chartData.data?.map((item) => {
    return {
      name: item.name,
      type: "line",
      stack: item.name,
      emphasis: {
        focus: "series",
      },
      symbolSize: 8,
      data: item.value,
    };
  });
  const option = {
    color: ["#96BFFF", "#FB7293", "#FFD700"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    legend: {
      data: props.chartData.data?.map((item) => item.name),
      right: 10,
      top: "20%",
      orient: "vertical",
    },
    grid: {
      left: "3%",
      right: "12%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: false,
        data: props.chartData.years,
      },
    ],
    yAxis: [
      {
        minInterval: 1,
        type: "value",
        nameTextStyle: {
          color: "#999",
          fontSize: 12,
        },
      },
    ],
    series: series,
  };

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart && chart.resize();
  });
};

watch(
  () => props.chartData,
  () => {
    chart && chart.dispose();
    initChart();
  },
  { deep: true }
);

watch(tabActive, () => {
  if (tabActive.value === "列表") {
    getList();
  } else {
    nextTick(() => {
      chart && chart.dispose();
      initChart();
    });
  }
});

onMounted(() => {
  initChart();
});
</script>

<style scoped lang="scss">
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
}

.tab-group {
  display: flex;
  align-items: center;
}

.chart {
  width: 100%;
  height: 300px;
}

.table-container {
  padding: 10px 0;
  .pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
