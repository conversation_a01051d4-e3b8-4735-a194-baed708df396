import request from "@/utils/request";

// 查询在职员工详细
export function getEmployee(employeeId) {
  return request({
    url: "/railway/wlStaffInfo/" + employeeId,
    method: "get",
  });
}
// 在职员工列表
export function listEmployee(params) {
  return request({
    url: "/railway/wlStaffInfo/list",
    method: "get",
    params,
  });
}

// 新增在职员工
export function addEmployee(data) {
  return request({
    url: "/railway/wlStaffInfo",
    method: "post",
    data: data,
  });
}

// 编辑在职员工
export function updateEmployee(data) {
  return request({
    url: "/railway/wlStaffInfo",
    method: "put",
    data: data,
  });
}
// 人员信息修改提审
export function auditEmployee(data) {
  return request({
    url: "/railway/wlStaffUpdate/submit",
    method: "post",
    data: data,
  });
}

// 删除在职员工
export function delEmployee(employeeId) {
  return request({
    url: "/railway/wlStaffInfo/" + employeeId,
    method: "delete",
  });
}
// 查询员工履历信息
export function getWlStaffInfoTransfer(employeeId, query) {
  return request({
    url: "/railway/wlStaffInfo/transfer/" + employeeId,
    method: "get",
    params: query,
  });
}
// 查询员工职称信息
export function getWlStaffInfoPost(employeeId, query) {
  return request({
    url: "/railway/wlStaffInfo/post/" + employeeId,
    method: "get",
    params: query,
  });
}
// 查询员工证书信息
export function getWlStaffInfoCertificate(query) {
  return request({
    url: "/railway/wlStaffInfo/certificate",
    method: "get",
    params: query,
  });
}
// 查询员工合同信息
export function getWlStaffInfoConcat(query) {
  return request({
    url: "/railway/wlStaffInfo/concat",
    method: "get",
    params: query,
  });
}

// 查询学历信息列表
export function listWlStaffDegree(query) {
  return request({
    url: "/railway/wlStaffDegree/list",
    method: "get",
    params: query,
  });
}

// 查询学历信息详细
export function getWlStaffDegree(id) {
  return request({
    url: "/railway/wlStaffDegree/" + id,
    method: "get",
  });
}

// 新增学历信息
export function addWlStaffDegree(data) {
  return request({
    url: "/railway/wlStaffDegree",
    method: "post",
    data: data,
  });
}

// 修改学历信息
export function updateWlStaffDegree(data) {
  return request({
    url: "/railway/wlStaffDegree",
    method: "put",
    data: data,
  });
}

// 删除学历信息
export function delWlStaffDegree(id) {
  return request({
    url: "/railway/wlStaffDegree/" + id,
    method: "delete",
  });
}
// 查询员工培训信息
export function listWlStaffInfoTraining(query) {
  return request({
    url: "/railway/wlStaffInfo/training",
    method: "get",
    params: query,
  });
}
// 新增自培记录
export function addWlStaffInfoTraining(data) {
  return request({
    url: "/railway/wlStaffInfo/trainingStaff",
    method: "post",
    data: data,
  });
}

// 修改自培记录
export function putWlStaffInfoEditTrainingStaff(data) {
  return request({
    url: "/railway/wlStaffInfo/edit/trainingStaff",
    method: "put",
    data: data,
  });
}

// 删除自培记录
export function delWlStaffTrainingStaff(selfId, staffId) {
  return request({
    url: `/railway/wlStaffInfo/remove/trainingStaff/${selfId}/${staffId}`,
    method: "delete",
  });
}
// 新增委培记录
export function addWlStaffInfoEvaluateCost(data) {
  return request({
    url: "/railway/wlStaffInfo/evaluateCost",
    method: "post",
    data: data,
  });
}
// 修改委培记录
export function putWlStaffInfoEditEvaluateCost(data) {
  return request({
    url: "/railway/wlStaffInfo/edit/evaluateCost",
    method: "put",
    data: data,
  });
}
// 删除委培记录
export function delWlStaffEvaluateCost(id) {
  return request({
    url: `/railway/wlStaffInfo/remove/evaluateCost/${id}`,
    method: "delete",
  });
}

// 员工附件参数
export function listWlAnnexGetObjectIds(staffId) {
  return request({
    url: `/railway/wlAnnex/getObjectIds/${staffId}`,
    method: "get",
  });
}
// 查询 员工附件档案
export function listWlAnnexListByStaff(query) {
  return request({
    url: "/railway/wlAnnex/listByStaff",
    method: "get",
    params: query,
  });
}
// 新增附件管理
export function addWlAnnex(data) {
  return request({
    url: "/railway/wlAnnex",
    method: "post",
    data: data,
  });
}
// 删除附件管理 [附件id，objectId，员工id]
export function delWlAnnex(ids) {
  return request({
    url: `/railway/wlAnnex/${ids}`,
    method: "delete",
  });
}
