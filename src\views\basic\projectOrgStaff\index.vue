<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="年份" prop="formYear">
            <el-date-picker
              v-model="queryParams.formYear"
              type="year"
              placeholder="请选择年份"
              value-format="YYYY"
              class="w-[200px]"
              clearable
            />
          </el-form-item>
          <el-form-item label="岗位" prop="postId">
            <RemoteSelect
              v-model="queryParams.postId"
              url="/system/post/list"
              labelKey="postName"
              valueKey="postId"
              placeholder="请选择岗位"
              class="w-[200px]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" class="custom-btn" @click="handleImport"
              >导入</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" class="custom-btn" @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="staffList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="编制年份" align="center" prop="formYear" />
          <el-table-column label="项目单位" align="center" prop="projectName" />
          <el-table-column label="项目机构" align="center" prop="deptName" />
          <el-table-column label="岗位" align="center" prop="postName" />
          <el-table-column label="一月" align="center" prop="formJan" />
          <el-table-column label="二月" align="center" prop="formFeb" />
          <el-table-column label="三月" align="center" prop="formMar" />
          <el-table-column label="四月" align="center" prop="formApr" />
          <el-table-column label="五月" align="center" prop="formMay" />
          <el-table-column label="六月" align="center" prop="formJun" />
          <el-table-column label="七月" align="center" prop="formJul" />
          <el-table-column label="八月" align="center" prop="formAug" />
          <el-table-column label="九月" align="center" prop="formSep" />
          <el-table-column label="十月" align="center" prop="formOct" />
          <el-table-column label="十一月" align="center" prop="formNov" />
          <el-table-column label="十二月" align="center" prop="formDec" />
          <el-table-column
            label="操作"
            align="center"
            width="180"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑定员设置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="staffForm" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="编制年份" prop="formYear">
              <el-date-picker
                v-model="form.formYear"
                type="year"
                placeholder="请选择年份"
                value-format="YYYY"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目单位" prop="projectId">
              <RemoteSelect
                v-model="form.projectId"
                url="/system/dept/list"
                labelKey="deptName"
                valueKey="deptId"
                :extraParams="{ parentId: '0' }"
                placeholder="请选择项目单位"
                @change="handleProjectChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目机构" prop="deptId">
              <RemoteSelect
                v-model="form.deptId"
                url="/system/dept/list"
                labelKey="deptName"
                valueKey="deptId"
                placeholder="请选择项目机构"
                :extraParams="{ parentId: 9999 }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位" prop="postId">
              <RemoteSelect
                v-model="form.postId"
                url="/system/post/list"
                labelKey="postName"
                valueKey="postId"
                placeholder="请选择岗位"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="月度编制" prop="monthly">
          <div class="month-table">
            <div class="month-header">
              <div
                v-for="(month, index) in months"
                :key="index"
                class="month-cell m-0"
              >
                {{ month }}
              </div>
            </div>
            <div class="month-inputs">
              <el-input-number
                v-for="(field, index) in monthFields"
                :key="index"
                :min="0"
                :step="1"
                step-strictly
                v-model="form[field]"
                :controls="false"
                class="month-cell"
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <ImportExcel
      v-model:visible="importVisible"
      :importUrl="'/railway/wlProjectOrgStaff/import'"
      :templateUrl="'/railway/wlProjectOrgStaff/importTemplate'"
      @success="getList"
    />
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import {
  listProjectOrgStaff,
  getProjectOrgStaff,
  addProjectOrgStaff,
  updateProjectOrgStaff,
  delProjectOrgStaff,
  exportProjectOrgStaff,
} from '@/api/basic/projectOrgStaff';
import ImportExcel from '@/components/ImportExcel/index.vue';

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 定员设置表格数据
const staffList = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 是否显示导入弹出层
const importVisible = ref(false);

// 月份数据
const months = ref([
  '1月',
  '2月',
  '3月',
  '4月',
  '5月',
  '6月',
  '7月',
  '8月',
  '9月',
  '10月',
  '11月',
  '12月',
]);
// 月份对应字段
const monthFields = ref([
  'formJan',
  'formFeb',
  'formMar',
  'formApr',
  'formMay',
  'formJun',
  'formJul',
  'formAug',
  'formSep',
  'formOct',
  'formNov',
  'formDec',
]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  formYear: new Date().getFullYear().toString(),
  postId: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  projectId: undefined,
  deptId: undefined,
  postId: undefined,
  formYear: new Date().getFullYear().toString(),
  formJan: 0,
  formFeb: 0,
  formMar: 0,
  formApr: 0,
  formMay: 0,
  formJun: 0,
  formJul: 0,
  formAug: 0,
  formSep: 0,
  formOct: 0,
  formNov: 0,
  formDec: 0,
});

// 表单校验
const rules = ref({
  formYear: [
    { required: true, message: '编制年份不能为空', trigger: 'change' },
  ],
  projectId: [
    { required: true, message: '项目单位不能为空', trigger: 'change' },
  ],
  deptId: [{ required: true, message: '项目机构不能为空', trigger: 'change' }],
  postId: [{ required: true, message: '岗位不能为空', trigger: 'change' }],
});

/** 查询定员设置列表 */
function getList() {
  loading.value = true;
  listProjectOrgStaff(queryParams.value).then((response) => {
    staffList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    projectId: undefined,
    deptId: undefined,
    postId: undefined,
    formYear: new Date().getFullYear().toString(),
    formJan: 0,
    formFeb: 0,
    formMar: 0,
    formApr: 0,
    formMay: 0,
    formJun: 0,
    formJul: 0,
    formAug: 0,
    formSep: 0,
    formOct: 0,
    formNov: 0,
    formDec: 0,
  };

  proxy.resetForm('staffForm');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加定员设置';
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getProjectOrgStaff(id).then((response) => {
    form.value = response.data;

    open.value = true;
    title.value = '编辑定员设置';
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['staffForm'].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateProjectOrgStaff(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess('编辑成功');
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addProjectOrgStaff(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删项目名称为"' + row.projectName + '"的定员设置数据项？')
    .then(function () {
      return delProjectOrgStaff(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    '/railway/wlProjectOrgStaff/export',
    {
      ...queryParams.value,
    },
    `定员设置_${new Date().getTime()}.xlsx`
  );
}

/** 导入按钮操作 */
function handleImport() {
  importVisible.value = true;
}

/** 项目单位变更操作 */
function handleProjectChange(project) {
  if (project && project.deptId) {
    form.value.deptId = undefined; // 重置机构ID
  } else {
    form.value.projectId = undefined;
    form.value.deptId = undefined;
  }
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.month-table {
  width: 100%;
  box-sizing: border-box;
}

.month-header {
  display: flex;
  width: 100%;
}

.month-inputs {
  display: flex;
  width: 100%;
}

.month-cell {
  flex: 1;
  text-align: center;
  height: 32px;
  line-height: 32px;
  margin: 2px;
}

.month-header .month-cell {
  background-color: #2f7bff;
  color: white;
  font-size: 14px;
}

.month-inputs :deep(.el-input-number .el-input__inner) {
  text-align: center;
  padding: 0;
  border-radius: 0;
}
.m-0 {
  margin: 0;
}
</style>
