<template>
  <div class="app-container">
    <!-- 导入对话框 -->
    <import-excel
      v-model:visible="importOpen"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @downloadTemplate="handleDownloadTemplate"
    />

    <div class="main-box-card">
      <el-card class="box-card">
        <el-tabs v-model="activeTab" @tab-change="tabChange">
          <el-tab-pane label="我发起的委培" name="myTraining"></el-tab-pane>
          <el-tab-pane label="审核中的委培" name="reviewing"></el-tab-pane>
          <el-tab-pane label="全部委培" name="allTraining"></el-tab-pane>
        </el-tabs>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="培训单位" prop="projectId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              :extraParams="{ parentId: '0' }"
              placeholder="请选择培训单位"
            ></RemoteSelect>
          </el-form-item>

          <el-form-item label="授课班名" prop="className">
            <el-input
              class="w-[200px]"
              v-model="queryParams.className"
              placeholder="请输入授课班名"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训开始时间" prop="beginTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.beginTime"
              type="date"
              placeholder="选择开始时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训结束时间" prop="endTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.endTime"
              type="date"
              placeholder="选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>

          <el-form-item label="培训形式" prop="trainingMode">
            <el-select
              class="w-[200px]"
              v-model="queryParams.trainingMode"
              placeholder="请选择培训形式"
              clearable
            >
              <el-option
                v-for="dict in training_form"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="集团内/外培训" prop="isInterior">
            <el-select
              class="w-[200px]"
              v-model="queryParams.isInterior"
              placeholder="请选择集团内/外培训"
              clearable
            >
              <el-option label="内" value="1" />
              <el-option label="外" value="0" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-show="activeTab !== 'reviewing'"
            label="状态"
            prop="status"
          >
            <el-select
              class="w-[200px]"
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option
                v-for="dict in training_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" type="primary" @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleImport">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="trainingDeputeList">
          <el-table-column label="培训单位" align="center" prop="projectName" />

          <el-table-column label="授课班名" align="center" prop="className" />
          <el-table-column label="培训人数" align="center" prop="trainingCount">
            <template #default="scope">
              <span>{{ scope.row.evaluateCosts.length || "0" }}</span>
            </template>
          </el-table-column>

          <el-table-column label="培训形式" align="center" prop="trainingMode">
            <template #default="scope">
              <dict-tag
                :options="training_form"
                :value="scope.row.trainingMode"
              />
            </template>
          </el-table-column>

          <el-table-column
            label="培训学校或单位"
            align="center"
            prop="trainingUnit"
          />

          <el-table-column label="培训信息来源" align="center" prop="sources" />

          <el-table-column
            label="集团内/外培训"
            align="center"
            prop="isInterior"
          >
            <template #default="scope">
              {{ scope.row.isInterior == 1 ? "内" : "外" }}
            </template>
          </el-table-column>
          <el-table-column label="开始时间" align="center" prop="beginTime">
            <template #default="scope">
              {{ parseTime(scope.row.beginTime, "{y}-{m}-{d}") }}
            </template>
          </el-table-column>
          <el-table-column label="结束时间" align="center" prop="endTime">
            <template #default="scope">
              {{ parseTime(scope.row.endTime, "{y}-{m}-{d}") }}
            </template>
          </el-table-column>
          <el-table-column label="附件" align="center" prop="wlAnnexes">
            <template #default="scope">
              <attachment-display :attachments="scope.row.wlAnnexes" />
            </template>
          </el-table-column>

          <el-table-column
            label="审核状态"
            align="center"
            prop="status"
            fixed="right"
          >
            <template #default="scope">
              <dict-tag :options="training_status" :value="scope.row.status" />
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="200"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="text"
                v-if="scope.row.status == '0'"
                @click="handleReport(scope.row)"
                >提报</el-button
              >
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button
                type="text"
                v-if="scope.row.status == '0'"
                @click="handleEdit(scope.row, 'edit')"
                >编辑</el-button
              >
              <el-button
                type="text"
                v-if="scope.row.status == '0'"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
              <el-button
                type="text"
                v-if="scope.row.status === '2'"
                @click="handleDerive(scope.row, 'look')"
                >导出</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onMounted, watch } from "vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import { ElLoading } from "element-plus";
import {
  listWlTrainingDepute,
  getWlTrainingDepute,
  addWlTrainingDepute,
  updateWlTrainingDepute,
  delWlTrainingDepute,
  postLaunchProcess,
  getWlTrainingDeputeDownload,
} from "@/api/training/consign";
import { downloadWlTrainingSelf } from "@/api/training/selfTraining";
import { getUserProfile } from "@/api/system/user";
import { useRouter } from "vue-router";
import AttachmentDisplay from "@/components/AttachmentDisplay/index.vue";
const router = useRouter();

const { proxy } = getCurrentInstance();
// 字典数据
const { training_form, training_status } = proxy.useDict(
  "training_form",

  "training_status"
);
// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 委培管理表格数据
const trainingDeputeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 当前选中的标签页
const activeTab = ref("myTraining");
const userId = ref(null);

/** 计算总学习课时 */
const totalHours = computed(() => {
  if (!trainingDeputeList.value || trainingDeputeList.value.length === 0)
    return 0;

  let total = 0;
  trainingDeputeList.value.forEach((item) => {
    // 汇总各类培训课时
    total += Number(item.beforeHour || 0); // 岗前培训
    total += Number(item.networkTraining || 0); // 岗位培训
    total += Number(item.secureHour || 0); // 安全培训
    total += Number(item.artHour || 0); // 技术培训
  });

  return total;
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: undefined,
  className: undefined,
  beginTime: undefined,
  endTime: undefined,
  trainingMode: undefined,
  isInterior: undefined,
  status: undefined,
  userId: undefined,
});

// 导入参数
const importOpen = ref(false);
const importTitle = ref("导入委培记录");
const importUrl = "/railway/wlTrainingDepute/importData";
const templateUrl = "/railway/wlTrainingDepute/importTemplate";

/** 查询委培管理列表 */
function getList() {
  loading.value = true;
  listWlTrainingDepute(queryParams.value)
    .then((response) => {
      trainingDeputeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

function tabChange(newVal) {
  activeTab.value = newVal;
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;

  // 根据不同的标签页设置不同的查询参数
  if (newVal === "myTraining") {
    queryParams.value.userId = userId.value;
    queryParams.value.status = undefined;
  } else if (newVal === "reviewing") {
    queryParams.value.userId = userId.value;
    queryParams.value.status = "1"; // 审核中状态值为1
  } else if (newVal === "allTraining") {
    queryParams.value.userId = undefined;
    queryParams.value.status = undefined;
  }
  // 查询数据
  getList();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");

  // 根据当前选中的标签页重新设置查询参数
  if (activeTab.value === "myTraining") {
    queryParams.value.userId = userId.value;
  } else if (activeTab.value === "reviewing") {
    queryParams.value.userId = userId.value;
    queryParams.value.status = "1";
  }
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  router.push({
    path: "/training/consign/details",
    query: {},
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  router.push({
    path: "/training/consign/details",
    query: {
      id: row.id,
    },
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids;
  const projectName = row.projectName;
  proxy.$modal
    .confirm(`是否确认删除单位为"${projectName}"的委培?`)
    .then(function () {
      return delWlTrainingDepute(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

// 列表导出按钮操作
let downloadLoadingInstance = null;
function handleDerive(row) {
  downloadLoadingInstance = ElLoading.service({
    text: "正在下载数据，请稍候",
    background: "rgba(0, 0, 0, 0.7)",
  });
  getWlTrainingDeputeDownload(row.id)
    .then((response) => {
      downloadLoadingInstance.close();
      var elink = document.createElement("a");
      elink.download = "委培管理审批表.xlsx";
      elink.style.display = "none";
      elink.href = window.URL.createObjectURL(response);
      elink.click();
      URL.revokeObjectURL(elink.href);
      // proxy.$modal.msgSuccess("下载成功");
    })
    .catch(() => {
      proxy.$modal.msgError("下载文件出现错误，请联系管理员！");
      downloadLoadingInstance.close();
    });

  // getWlTrainingDeputeDownload
  // proxy.download(
  //     "/railway/wlTrainingDepute/download",
  //     {
  //       id:row.id
  //     },
  //     `${row.className+'_' || ''}委培管理审批表_${new Date().getTime()}.xlsx`
  //   );
}

/** 导入按钮操作 */
function handleImport() {
  importOpen.value = true;
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "/railway/wlTrainingDepute/warn/export",
    {
      ...queryParams.value,
    },
    `委培管理记录_${new Date().getTime()}.xlsx`
  );
}

/** 提报按钮操作 */
function handleReport(row) {
  let value = {
    templateId: 10004,
    bizId: row.id,
  };
  const projectName = row.projectName;
  proxy.$modal
    .confirm(`是否确认提报单位为"${projectName}"的委培?`)
    .then(function () {
      return postLaunchProcess(value);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("提报成功");
    })
    .catch(() => {});
}
/** 查看按钮操作 */
function handleView(row) {
  proxy.getProcessRouterPath(row, "10004", row.id);
}
/** 查看按钮操作 */
function handleEdit(row, type = "edit") {
  router.push({
    path: "/training/consign/details",
    query: {
      id: row.id,
      type: type,
    },
    meta: { activeMenu: location.pathname },
  });
}

/** 下载模板操作 */
function handleDownloadTemplate() {
  console.log("下载模板");
  proxy.download(
    "railway/wlTrainingDepute/importTemplate",
    {},
    `委培管理模板_${new Date().getTime()}.xlsx`
  );
}

function handleDownload(row) {
  downloadWlTrainingSelf(row.id)
    .then((response) => {
      const blob = new Blob([response], { type: "application/zip" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = row.className + "委培附件";
      link.click();
      URL.revokeObjectURL(link.href);
      proxy.$modal.msgSuccess("下载成功");
    })
    .catch(() => {});
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  //   proxy.$modal.msgSuccess(response.msg);
  getList();
}

onMounted(async () => {
  const { data } = await getUserProfile(); // 获取当前登录用户信息
  userId.value = data.userId;
  tabChange(activeTab.value);
});
</script>

<style scoped lang="scss">
::v-deep(.el-tabs--border-card) {
  border-bottom: 0px;
  margin-bottom: 20px;
  .el-tabs__content {
    padding: 0px !important;
  }
}
.cardItem {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  div {
    min-width: 100px;
    padding: 8px 18px;
    background: #e9f1ff;
    border-radius: 4px;
    margin-right: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    text-align: center;
    margin-bottom: 10px;
  }
}
</style>
