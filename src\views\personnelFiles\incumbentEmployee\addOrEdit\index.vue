<template>
  <div class="app-container">
    <el-card class="box-card" v-if="route.query.id">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="人员信息" name="basic"> </el-tab-pane>
        <el-tab-pane label="履历信息" name="resume"> </el-tab-pane>
        <el-tab-pane label="培训信息" name="training"> </el-tab-pane>
        <el-tab-pane label="奖惩信息" name="reward"> </el-tab-pane>
        <el-tab-pane label="职称信息" name="title"> </el-tab-pane>
        <el-tab-pane label="证书信息" name="certificate"> </el-tab-pane>
        <el-tab-pane label="劳动合同信息" name="contract"> </el-tab-pane>
        <el-tab-pane label="档案附件" name="archive"> </el-tab-pane>
        <el-tab-pane label="学历信息" name="education"> </el-tab-pane>
      </el-tabs>
      <component :is="currentComponent" />
    </el-card>
    <el-card class="box-card" v-else>
      <BasicInfo></BasicInfo>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, defineAsyncComponent } from "vue";
import { useRoute } from "vue-router";

// 异步导入组件
const BasicInfo = defineAsyncComponent(() =>
  import("./components/BasicInfo.vue")
); // 新增或者编辑人员详情
const ResumeInfo = defineAsyncComponent(() =>
  import("./components/ResumeInfo.vue")
);
const TrainingInfo = defineAsyncComponent(() =>
  import("./components/TrainingInfo.vue")
);
const RewardInfo = defineAsyncComponent(() =>
  import("./components/RewardInfo.vue")
);
const TitleInfo = defineAsyncComponent(() =>
  import("./components/TitleInfo.vue")
);
const CertificateInfo = defineAsyncComponent(() =>
  import("./components/CertificateInfo.vue")
);
const ContractInfo = defineAsyncComponent(() =>
  import("./components/ContractInfo.vue")
);
const ArchiveInfo = defineAsyncComponent(() =>
  import("./components/ArchiveInfo.vue")
);
const EducationInfo = defineAsyncComponent(() =>
  import("./components/EducationInfo.vue")
);
const BasicDetails = defineAsyncComponent(() =>
  import("./components/BasicDetails.vue")
); // 人员详情

const query = ref({});
const route = useRoute();
const activeTab = ref(route.query.activeTab || "basic");

// 根据当前激活的选项卡和查询类型动态确定要渲染的组件
const currentComponent = computed(() => {
  // 人员信息选项卡特殊处理
  if (activeTab.value === "basic") {
    return query.value.type === "check" ? BasicDetails : BasicInfo;
  }

  // 其他选项卡根据名称映射到对应组件
  const componentMap = {
    basic: BasicInfo,
    resume: ResumeInfo,
    training: TrainingInfo,
    reward: RewardInfo,
    title: TitleInfo,
    certificate: CertificateInfo,
    contract: ContractInfo,
    archive: ArchiveInfo,
    education: EducationInfo,
  };

  return componentMap[activeTab.value];
});

onMounted(() => {
  query.value = route.query;
});
</script>
<style scoped lang="scss">
::v-deep(.el-tabs--border-card) {
  border-bottom: 0px;
  margin-bottom: 0px;
  .el-tabs__content {
    padding: 0px !important;
  }
}
</style>
