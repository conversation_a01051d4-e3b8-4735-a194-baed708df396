<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
    </div>
    <div ref="chartRef" class="chart"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from "vue";
import * as echarts from "echarts/core";
import { Bar<PERSON><PERSON>, LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from "echarts/components";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  UniversalTransition,
  CanvasRenderer,
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});

const chartRef = ref(null);
let chart = null;

const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  // 模拟数据
  const trainingData = [60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60];
  const teachingData = [
    135, 135, 135, 135, 135, 135, 135, 135, 135, 135, 135, 135,
  ];
  const onlineData = [30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30];
  const studyCostData = [60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60];

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },
    legend: {
      data: ["培训人次", "上报课时", "审批课时", "学习费用"],
      right: 120,
      top: 27,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: props.chartData.xAxis,
        axisLabel: {
          interval: 0,
          rotate: 45,
          color: "#333",
          fontSize: 12,
        },
        axisPointer: {
          type: "shadow",
        },
        axisTick: {
          alignWithLabel: true,
        },
        axisLine: {
          lineStyle: {
            color: "#E4E7ED",
          },
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "人数/课时",
        nameTextStyle: {
          color: "#333", // 名称颜色
          fontSize: 12, // 字体大小
          align: "right", // 文本对齐方式，此处设置为居中
        },
        min: 0,
        max: 210,
        interval: 30,
        axisLabel: {
          formatter: "{value}",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#E4E7ED",
          },
        },
      },
      {
        type: "value",
        name: "",
        nameTextStyle: {
          color: "#333", // 名称颜色
          fontSize: 12, // 字体大小
          align: "left", // 文本对齐方式，此处设置为居中
        },
        min: 0,
        max: 250,
        interval: 50,
        axisLabel: {
          formatter: "{value}.000",
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: "培训人次",
        type: "bar",
        barWidth: 16,
        itemStyle: {
          color: "#08C3D2",
          borderRadius: [8, 8, 0, 0],
        },
        data: props.chartData.staffNums,
      },
      {
        name: "上报课时",
        type: "bar",
        barWidth: 16,
        itemStyle: {
          color: "#9FE6B8",
          borderRadius: [8, 8, 0, 0],
        },
        data: props.chartData.reportTimes,
      },
      {
        name: "审批课时",
        type: "bar",
        barWidth: 16,
        itemStyle: {
          color: "#FEDB65",
          borderRadius: [8, 8, 0, 0],
        },
        data: props.chartData.approveTimes,
      },
      {
        name: "学习费用",
        type: "bar",
        barWidth: 16,
        yAxisIndex: 1,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: "#FB7293",
          borderRadius: [8, 8, 0, 0],
        },
        data: props.chartData.costs,
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart && chart.resize();
  });
};

watch(
  () => props.chartData,
  () => {
    chart && chart.dispose();
    initChart();
  },
  { deep: true }
);

onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.chart {
  width: 100%;
  height: 400px;
}
.card-header {
  min-height: 32px;
}
</style>
