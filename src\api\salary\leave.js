import request from '@/utils/request';

// 查询假单列表
export function listLeave(query) {
  return request({
    url: '/railway/wlLeave/list',
    method: 'get',
    params: query,
  });
}

// 查询假单详细
export function getLeave(id) {
  return request({
    url: '/railway/wlLeave/' + id,
    method: 'get',
  });
}

// 新增假单
export function addLeave(data) {
  return request({
    url: '/railway/wlLeave',
    method: 'post',
    data: data,
  });
}

// 修改假单
export function updateLeave(data) {
  return request({
    url: '/railway/wlLeave',
    method: 'put',
    data: data,
  });
}

// 删除假单
export function delLeave(id) {
  return request({
    url: '/railway/wlLeave/' + id,
    method: 'delete',
  });
}

// 导出假单
export function exportLeave(query) {
  return request({
    url: '/railway/wlLeave/export',
    method: 'get',
    params: query,
  });
}
