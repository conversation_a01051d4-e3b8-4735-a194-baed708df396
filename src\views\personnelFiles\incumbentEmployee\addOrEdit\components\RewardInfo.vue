<template>
  <div class="resume-info">
    <div class="flex justify-between mb-4 items-center">
      <p class="font-bold text-xl">奖惩信息</p>
      <el-button type="primary" @click="exportResumeInfo"
        >奖惩信息导出</el-button
      >
    </div>

    <div v-if="resumeList.length === 0" class="info-content">
      <Empty text="暂无奖惩信息" />
    </div>
    <div v-for="(item, index) in resumeList" :key="index">
      <div class="indexTitle">
        <span>奖惩{{ numberToChinese(index + 1) }}</span>
      </div>
      <div class="info-content">
        <div class="resume-block">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span class="label">奖惩类型：</span>
                <span>{{
                  getDictLabel(reward_punishment_type, item?.type) || "--"
                }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <div class="info-item">
                <span class="label">执行时间：</span>
                <span>{{ item.executeDate || "--" }}</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item">
                <span class="label">附件：</span>
                <div v-if="item.wlAnnexes && item.wlAnnexes.length > 0">
                  <span v-for="(item, index) in item.wlAnnexes" :key="index">
                    <el-button
                      type="text"
                      class="elLink"
                      @click="downloadFile(item.path)"
                    >
                      {{ item.name }}
                    </el-button>
                  </span>
                </div>
                <span v-else>--</span>
              </div>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24">
              <div class="info-item full-width">
                <span class="label">奖惩描述：</span>
                <span>{{ item.remark || "--" }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getResumeInfo"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";

import { listRewardPunishment } from "@/api/personnelFiles/rewardPunishment";
import { useRoute, useRouter } from "vue-router";
import { downloadFile } from "@/utils/welink";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const query = ref({});

// 字典数据
const { reward_punishment_type } = proxy.useDict("reward_punishment_type");

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const total = ref(0);

// 获取字典标签
function getDictLabel(dict, value) {
  if (!value) return;
  const item = dict.find((d) => d.value === value);
  return item ? item.label : "";
}

const resumeList = ref([
  // {type:'0',executeDate:'2025-6',remark:'奖惩描述'}
]);

function numberToChinese(num) {
  var chinese = "";
  var unit = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  var digit = ["", "十", "百", "千", "万", "亿"];

  var numStr = num.toString();
  var len = numStr.length;
  for (var i = 0; i < len; i++) {
    var currentNum = parseInt(numStr[i]);
    chinese += unit[currentNum] + digit[len - i - 1];
  }
  return chinese;
}

/** 查询奖惩信息 */
function getResumeInfo(employeeId) {
  listRewardPunishment({ ...queryParams.value, staffId: employeeId }).then(
    (response) => {
      if (response.code === 200) {
        resumeList.value = response.rows || [];
        total.value = response.total || 0;
      }
    }
  );
}
function handleView(id) {
  // 跳转到 发文详情界面
  if (!id) return;
  router.push({
    path: "",
    query: {
      id: id,
    },
  });
}

/** 导出奖惩信息 */
function exportResumeInfo() {
  const employeeId = route.query.id;
  if (employeeId) {
    proxy.download(
      `railway/wlBonusPenalty/export`,
      {
        staffId: employeeId,
      },
      `个人奖惩信息_${new Date().getTime()}.xlsx`
    );
  }
}

onMounted(() => {
  query.value = route.query;
  // 获取员工ID
  const employeeId = route.query.id;
  if (employeeId) {
    getResumeInfo(employeeId);
  }
});
</script>

<style scoped lang="scss">
.resume-info {
  padding: 20px;
}

.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
}

.resume-block {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #d9d9d9;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

.el-row {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 140px;
    text-align: right;
    color: #606266;
    padding-right: 10px;
  }
}

.full-width {
  width: 100%;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}
.indexTitle {
  // display: block;
  margin: 20px 0;
  span {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #2674fe;
    background-color: #e6efff;
    padding: 10px 20px;
    border-radius: 4px;
  }
}
.elLink {
  cursor: pointer;
}
.elLink:hover {
  color: #409eff;
}
@media screen and (max-width: 768px) {
  .info-item .label {
    min-width: 110px;
  }
}
</style>
