<template>
  <div class="dynamic-panel-container">
    <CustomTabs v-model="activeSubTab" @tab-click="handleSubTabClick">
      <TabPane label="动态图表" name="dynamicChart">
        <DynamicChart />
      </TabPane>
      <TabPane label="汇总信息" name="summaryInfo">
        <SummaryInfo />
      </TabPane>
      <TabPane label="人员信息" name="personnelInfo">
        <PersonnelInfo />
      </TabPane>
      <TabPane label="辅助性人员信息" name="auxiliaryPersonnel">
        <AuxiliaryPersonnel />
      </TabPane>
      <TabPane label="自建班组" name="selfBuiltTeam">
        <SelfBuiltTeam />
      </TabPane>
    </CustomTabs>
  </div>
</template>

<script setup>
import { ref } from "vue";
import CustomTabs from "@/components/CustomTabs/index.vue";
import TabPane from "@/components/CustomTabs/TabPane.vue";
import DynamicChart from "./components/DynamicChart.vue";
import SummaryInfo from "./components/SummaryInfo.vue";
import PersonnelInfo from "./components/PersonnelInfo.vue";
import AuxiliaryPersonnel from "./components/AuxiliaryPersonnel.vue";
import SelfBuiltTeam from "./components/SelfBuiltTeam.vue";

const activeSubTab = ref("dynamicChart");

const handleSubTabClick = (pane, event) => {
  console.log("子标签切换:", pane.label, event);
};
</script>

<style scoped>
.dynamic-panel-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
</style>
