import request from "@/utils/request";

// 查询退休人员列表
export function listRetiredEmployee(params) {
  // 添加退休状态参数
  const queryParams = {
    ...params,
  };
  return request({
    url: "/railway/wlStaffInfo/list",
    method: "get",
    params: queryParams,
  });
}

// 获取退休人员详细信息
export function getRetiredEmployee(id) {
  return request({
    url: "/railway/wlStaffInfo/" + id,
    method: "get",
  });
}

// 导出退休人员基本信息
export function exportRetiredEmployeeBasic(params) {
  // 添加退休状态参数
  const queryParams = {
    ...params,
    status: "R",
  };
  return request({
    url: "/railway/wlStaffInfo/exportBasic",
    method: "get",
    params: queryParams,
  });
}

// 导出退休人员履历
export function exportRetiredEmployeeResume(params) {
  // 添加退休状态参数
  const queryParams = {
    ...params,
    status: "R",
  };
  return request({
    url: "/railway/wlStaffInfo/exportResume",
    method: "get",
    params: queryParams,
  });
}

// 导出退休人员简历
export function exportRetiredEmployeeCV(params) {
  // 添加退休状态参数
  const queryParams = {
    ...params,
    status: "R",
  };
  return request({
    url: "/railway/wlStaffInfo/exportCV",
    method: "get",
    params: queryParams,
  });
}
