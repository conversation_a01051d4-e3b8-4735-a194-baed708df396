<template>
  <div class="notice-card">
    <div class="flex justify-between items-center">
      <span class="section-title">{{ title }}</span>
      <div class="view-more" @click="$emit('more')">
        <span>查看更多</span>
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
    </div>
    <div class="notice-box" v-if="noticeList && noticeList.length > 0">
      <div
        class="notice-item"
        v-for="item in noticeList"
        :key="item.id"
        @click="$emit('click', item)"
        :class="{ unread: item.status !== '2' }"
      >
        <div class="notice-content">
          <div class="notice-header">
            <span class="notice-title">通知:</span>
            <span
              class="read-status"
              :class="{ 'unread-dot': item.status !== '2' }"
            >
              {{ item.status !== "2" ? "未读" : "已读" }}
            </span>
          </div>
          <div class="notice-info">
            <div
              class="notice-text"
              :class="{ 'font-bold': item.status !== '2' }"
            >
              {{ item.todoSubject }}
            </div>
            <button class="watch-btn">查看</button>
          </div>
        </div>
      </div>
    </div>
    <div class="notice-box h-full justify-center" v-else>
      <Empty />
    </div>
  </div>
</template>

<script setup>
import { ArrowRight } from "@element-plus/icons-vue";
import Empty from "@/components/Empty/index.vue";

defineEmits(["click", "more"]);

defineProps({
  title: {
    type: String,
    default: "通知消息",
  },
  noticeList: {
    type: Array,
    default: () => [],
  },
});
</script>

<style lang="scss" scoped>
.notice-card {
  padding: 16px;
  height: 455px;
  overflow: auto;
  background-color: white;
}

.notice-box {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  max-height: calc(100% - 60px);
  overflow-y: auto;
}

.notice-item {
  position: relative;
  padding-left: 12px;
  padding-right: 8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-radius: 6px;
  margin-bottom: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.unread::before {
    background-color: #ff4744;
  }

  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 8px;
    width: 4px;
    height: calc(100% - 16px);
    background-color: #2674fe;
    border-radius: 2px;
  }
}

.notice-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.read-status {
  font-size: 12px;
  color: #909399;

  &.unread-dot {
    color: #ff4744;
    font-weight: bold;
    position: relative;
    padding-left: 16px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ff4744;
    }
  }
}

.notice-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 4px;
}

.notice-text {
  width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #606266;

  &.font-bold {
    color: #303133;
    font-weight: bold;
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.notice-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.view-more {
  display: flex;
  align-items: center;
  color: #2674fe;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #ebf7fd;
  }

  i {
    margin-left: 4px;
    font-size: 12px;
  }
}

.watch-btn {
  min-width: 80px;
  height: 32px;
  border-radius: 16px;
  color: #0570c0;
  border: none;
  padding: 0 12px;
  font-size: 12px;
  cursor: pointer;
  background: #fff;
  border-radius: 18px;
  border: 1px solid #0570c0;
}

@media (max-width: 768px) {
  .watch-btn {
    min-width: 60px;
    height: 28px;
    font-size: 12px;
  }

  .notice-text {
    width: 80%;
  }
}
</style>
