<template>
  <div class="skeleton-loader">
    <!-- 顶部搜索区域骨架 -->
    <div v-if="showSearch" class="skeleton-search mb-4">
      <div class="flex justify-between items-center">
        <div class="skeleton-item skeleton-search-bar"></div>
        <div class="skeleton-item skeleton-button"></div>
      </div>
    </div>

    <!-- 表单布局骨架 -->
    <div v-if="layout === 'form'" class="skeleton-form">
      <!-- 表单标题 -->
      <div class="skeleton-form-title mb-4"></div>

      <!-- 表单内容 -->
      <div class="skeleton-form-content">
        <div
          v-for="row in formRows"
          :key="row"
          class="skeleton-form-row"
          :class="{ 'with-photo': row === 1 && showPhoto }"
        >
          <div class="skeleton-form-fields">
            <div
              v-for="col in getColsForRow(row)"
              :key="col"
              class="skeleton-form-field"
            >
              <div class="skeleton-field-label"></div>
              <div class="skeleton-field-input"></div>
            </div>
          </div>
          <!-- 照片上传区域（仅第一行显示） -->
          <div v-if="row === 1 && showPhoto" class="skeleton-photo">
            <div class="skeleton-photo-upload"></div>
          </div>
        </div>
      </div>

      <!-- 表单底部按钮 -->
      <div v-if="showButtons" class="skeleton-form-buttons">
        <div class="skeleton-button-group">
          <div class="skeleton-button"></div>
          <div class="skeleton-button"></div>
        </div>
      </div>
    </div>

    <!-- 表格布局骨架 -->
    <div v-else-if="layout === 'table'" class="skeleton-table">
      <!-- 表格工具栏 -->
      <div v-if="showToolbar" class="skeleton-toolbar mb-4">
        <div class="flex justify-between items-center">
          <div class="flex gap-2">
            <div class="skeleton-button"></div>
            <div class="skeleton-button"></div>
            <div class="skeleton-button"></div>
          </div>
          <div class="skeleton-button"></div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="skeleton-table-content">
        <!-- 表格头 -->
        <div class="skeleton-table-header">
          <div
            v-for="col in tableCols"
            :key="col"
            class="skeleton-table-th"
          ></div>
        </div>
        <!-- 表格行 -->
        <div v-for="row in tableRows" :key="row" class="skeleton-table-row">
          <div
            v-for="col in tableCols"
            :key="col"
            class="skeleton-table-td"
          ></div>
        </div>
      </div>

      <!-- 分页器 -->
      <div v-if="showPagination" class="skeleton-pagination mt-4">
        <div class="skeleton-pagination-content"></div>
      </div>
    </div>

    <!-- 列表布局骨架 -->
    <div v-else-if="layout === 'list'" class="skeleton-list">
      <div v-for="item in listItems" :key="item" class="skeleton-list-item">
        <div class="skeleton-list-avatar"></div>
        <div class="skeleton-list-content">
          <div class="skeleton-list-title"></div>
          <div class="skeleton-list-desc"></div>
          <div class="skeleton-list-meta"></div>
        </div>
        <div class="skeleton-list-actions">
          <div class="skeleton-button-small"></div>
          <div class="skeleton-button-small"></div>
        </div>
      </div>
    </div>

    <!-- 详情页布局骨架 -->
    <div v-else-if="layout === 'detail'" class="skeleton-detail">
      <!-- 详情标题 -->
      <div class="skeleton-detail-title mb-6"></div>

      <!-- 详情内容块 -->
      <div
        v-for="block in detailBlocks"
        :key="block"
        class="skeleton-detail-block mb-6"
      >
        <div class="skeleton-detail-block-title mb-4"></div>
        <div class="skeleton-detail-block-content">
          <div v-for="row in 3" :key="row" class="skeleton-detail-row">
            <div v-for="col in 3" :key="col" class="skeleton-detail-field">
              <div class="skeleton-field-label"></div>
              <div class="skeleton-field-value"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  // 布局类型：form-表单, table-表格, list-列表, detail-详情页
  layout: {
    type: String,
    default: "form",
    validator: (value) => ["form", "table", "list", "detail"].includes(value),
  },
  // 是否显示搜索区域
  showSearch: {
    type: Boolean,
    default: true,
  },
  // 是否显示工具栏（表格布局）
  showToolbar: {
    type: Boolean,
    default: true,
  },
  // 是否显示分页器
  showPagination: {
    type: Boolean,
    default: true,
  },
  // 是否显示底部按钮
  showButtons: {
    type: Boolean,
    default: true,
  },
  // 是否显示照片上传区域
  showPhoto: {
    type: Boolean,
    default: false,
  },
  // 表单行数
  formRows: {
    type: Number,
    default: 8,
  },
  // 每行列数（可以是数组，为每行指定不同列数）
  formCols: {
    type: [Number, Array],
    default: 4,
  },
  // 表格行数
  tableRows: {
    type: Number,
    default: 10,
  },
  // 表格列数
  tableCols: {
    type: Number,
    default: 6,
  },
  // 列表项数
  listItems: {
    type: Number,
    default: 8,
  },
  // 详情页内容块数
  detailBlocks: {
    type: Number,
    default: 3,
  },
  // 动画持续时间（毫秒）
  animationDuration: {
    type: Number,
    default: 1500,
  },
});

// 计算每行的列数
const getColsForRow = (rowIndex) => {
  if (Array.isArray(props.formCols)) {
    return props.formCols[rowIndex - 1] || props.formCols[0] || 4;
  }
  return props.formCols;
};
</script>

<style scoped lang="scss">
.skeleton-loader {
  .skeleton-item {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading v-bind(animationDuration + "ms") infinite;
    border-radius: 4px;
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  // 搜索区域骨架
  .skeleton-search {
    .skeleton-search-bar {
      width: 300px;
      height: 32px;
    }
    .skeleton-button {
      width: 80px;
      height: 32px;
    }
  }

  // 表单布局骨架
  .skeleton-form {
    .skeleton-form-title {
      @extend .skeleton-item;
      width: 200px;
      height: 20px;
    }

    .skeleton-form-content {
      border: 1px solid #e6e6e6;
      border-radius: 6px;
    }

    .skeleton-form-row {
      display: flex;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &.with-photo {
        .skeleton-form-fields {
          flex: 1;
        }
      }

      .skeleton-form-fields {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
        width: 100%;
      }

      .skeleton-form-field {
        .skeleton-field-label {
          @extend .skeleton-item;
          width: 80px;
          height: 16px;
          margin-bottom: 8px;
        }
        .skeleton-field-input {
          @extend .skeleton-item;
          width: 100%;
          height: 32px;
        }
      }

      .skeleton-photo {
        width: 150px;
        margin-left: 20px;

        .skeleton-photo-upload {
          @extend .skeleton-item;
          width: 100%;
          height: 180px;
        }
      }
    }

    .skeleton-form-buttons {
      padding: 16px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #f0f0f0;

      .skeleton-button-group {
        display: flex;
        gap: 12px;

        .skeleton-button {
          @extend .skeleton-item;
          width: 100px;
          height: 32px;
        }
      }
    }
  }

  // 表格布局骨架
  .skeleton-table {
    .skeleton-toolbar {
      .skeleton-button {
        @extend .skeleton-item;
        width: 80px;
        height: 32px;
      }
    }

    .skeleton-table-content {
      border: 1px solid #e6e6e6;
      border-radius: 6px;
      overflow: hidden;

      .skeleton-table-header {
        display: grid;
        grid-template-columns: repeat(v-bind(tableCols), 1fr);
        background: #fafafa;
        border-bottom: 1px solid #e6e6e6;

        .skeleton-table-th {
          @extend .skeleton-item;
          height: 20px;
          margin: 12px 16px;
        }
      }

      .skeleton-table-row {
        display: grid;
        grid-template-columns: repeat(v-bind(tableCols), 1fr);
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .skeleton-table-td {
          @extend .skeleton-item;
          height: 16px;
          margin: 16px;
        }
      }
    }

    .skeleton-pagination {
      display: flex;
      justify-content: center;

      .skeleton-pagination-content {
        @extend .skeleton-item;
        width: 300px;
        height: 32px;
      }
    }
  }

  // 列表布局骨架
  .skeleton-list {
    .skeleton-list-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .skeleton-list-avatar {
        @extend .skeleton-item;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin-right: 16px;
      }

      .skeleton-list-content {
        flex: 1;

        .skeleton-list-title {
          @extend .skeleton-item;
          width: 200px;
          height: 20px;
          margin-bottom: 8px;
        }

        .skeleton-list-desc {
          @extend .skeleton-item;
          width: 300px;
          height: 16px;
          margin-bottom: 8px;
        }

        .skeleton-list-meta {
          @extend .skeleton-item;
          width: 150px;
          height: 14px;
        }
      }

      .skeleton-list-actions {
        display: flex;
        gap: 8px;

        .skeleton-button-small {
          @extend .skeleton-item;
          width: 60px;
          height: 28px;
        }
      }
    }
  }

  // 详情页布局骨架
  .skeleton-detail {
    .skeleton-detail-title {
      @extend .skeleton-item;
      width: 300px;
      height: 28px;
    }

    .skeleton-detail-block {
      .skeleton-detail-block-title {
        @extend .skeleton-item;
        width: 150px;
        height: 20px;
      }

      .skeleton-detail-block-content {
        border: 1px solid #e6e6e6;
        border-radius: 6px;
        padding: 16px;

        .skeleton-detail-row {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .skeleton-detail-field {
            .skeleton-field-label {
              @extend .skeleton-item;
              width: 80px;
              height: 16px;
              margin-bottom: 8px;
            }

            .skeleton-field-value {
              @extend .skeleton-item;
              width: 100%;
              height: 20px;
            }
          }
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .skeleton-loader {
    .skeleton-item {
      background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
      background-size: 200% 100%;
    }
  }
}
</style>
