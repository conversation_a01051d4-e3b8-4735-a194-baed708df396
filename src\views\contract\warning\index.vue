<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="合同编号" prop="contractCode">
            <el-input
              class="w-[200px]"
              v-model="queryParams.contractCode"
              placeholder="请输入合同编号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              class="w-[200px]"
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="合同类型" prop="contractType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.contractType"
              placeholder="请选择合同类型"
              clearable
            >
              <el-option
                v-for="dict in contract_type"
                :label="dict.label"
                :value="dict.value"
                :key="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="合同种类" prop="contractTypeId">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.contractTypeId"
              url="railway/wlType/list"
              labelKey="typeName"
              valueKey="id"
              :extraParams="{
                type: 'HT',
              }"
              placeholder="请选择合同种类"
              @change="handleContractTypeChange"
            />
          </el-form-item>
          <el-form-item label="合同状态" prop="expirationStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.expirationStatus"
              placeholder="请选择合同状态"
              clearable
            >
              <el-option
                v-for="dict in contract_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="合同年限" prop="duration">
            <el-select
              class="w-[200px]"
              v-model="queryParams.duration"
              placeholder="请选择合同年限"
              clearable
            >
              <el-option
                v-for="dict in contract_duration"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="终止时间" prop="endTime">
            <el-date-picker
              class="w-[200px]"
              v-model="queryParams.endTime"
              type="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="选择终止时间"
              clearable
            />
          </el-form-item>
          <el-form-item label="预警状态" prop="warningStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.warningStatus"
              placeholder="请选择预警状态"
              clearable
            >
              <el-option
                v-for="dict in certificate_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="预警级别" prop="warnLevel">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.warnLevel"
              url="/railway/wlEarlyWarning/list"
              labelKey="ruleName"
              valueKey="id"
              placeholder="请选择预警级别"
              clearable
              :extraParams="{ ruleType: 'EXPIRATION_OF_CONTRACT' }"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>

          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="warningList">
          <el-table-column
            label="序号"
            type="index"
            width="50"
            align="center"
          />
          <el-table-column
            label="合同编号"
            align="center"
            prop="contractCode"
          />
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="学历" align="center" prop="highestEducation">
            <template #default="scope">
              <dict-tag
                :options="education_type"
                :value="scope.row.highestEducation"
              />
            </template>
          </el-table-column>
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="出生日期"
            align="center"
            prop="birthDate"
            width="100"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.birthDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>

          <el-table-column label="合同种类" align="center" prop="typeName">
          </el-table-column>
          <el-table-column label="合同类型" align="center" prop="contractType">
            <template #default="scope">
              <dict-tag
                :options="contract_type"
                :value="scope.row.contractType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="开始时间"
            align="center"
            prop="beginTime"
            width="100"
          >
            <template #default="scope">
              <span v-if="scope.row.beginTime">{{
                parseTime(scope.row.beginTime, "{y}-{m}-{d}")
              }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            label="结束时间"
            align="center"
            prop="endTime"
            width="100"
          >
            <template #default="scope">
              <span v-if="scope.row.endTime">{{
                parseTime(scope.row.endTime, "{y}-{m}-{d}")
              }}</span>

              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="合同状态" align="center" prop="status" />
          <el-table-column
            label="合同变更、终止、解除情况"
            align="center"
            width="180"
            prop="benefit"
          />
          <el-table-column label="预警级别" align="center" prop="warnLevel">
          </el-table-column>
          <el-table-column
            label="合同预警状态"
            align="center"
            width="120"
            prop="status"
          >
          </el-table-column>
          <el-table-column label="剩余天数" align="center" prop="daysInterval">
            <template #default="scope">
              <el-tag :type="getTagType(scope.row.daysInterval)">{{
                scope.row.daysInterval
              }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            width="160"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)"
                >查看</el-button
              >
              <el-button type="text" @click="handleRenew(scope.row)"
                >续签</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import { parseTime } from "@/utils/welink";
import { listContractWarning } from "@/api/contract/warning";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const {
  sys_user_sex,
  education_type,
  personnel_type,
  contract_type,
  contract_category,
  contract_status,
  contract_duration,
  certificate_status,
  warning_level,
} = proxy.useDict(
  "sys_user_sex",
  "education_type",
  "personnel_type",
  "contract_type",
  "contract_category",
  "contract_status",
  "contract_duration",
  "certificate_status",
  "warning_level"
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 合同预警表格数据
const warningList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  contractCode: undefined,
  name: undefined,
  endTime: undefined,
  contractType: undefined,
  contractTypeId: undefined,
  warningStatus: undefined,
  duration: undefined,
});

/** 查询合同预警列表 */
function getList() {
  loading.value = true;
  listContractWarning(queryParams.value).then((response) => {
    warningList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "railway/wlContract/warn/export",
    {
      ...queryParams.value,
    },
    `合同预警${new Date().getTime()}.xlsx`
  );
}

/** 查看按钮操作 */
function handleView(row) {
  router.push({
    path: "/contract/index/addOrEdit",
    query: { id: row.id, view: true },
    meta: { activeMenu: location.pathname },
  });
}

/** 续签按钮操作 */
function handleRenew(row) {
  // TODO: 实现续签功能
  router.push({
    path: "/contract/index/addOrEdit",
    query: { id: row.id, type: "renew" },
    meta: { activeMenu: location.pathname },
  });
}

/** 获取剩余天数标签类型 */
function getTagType(days) {
  if (days <= 30) {
    return "danger";
  } else if (days <= 90) {
    return "warning";
  } else {
    return "info";
  }
}

/** 下载模板操作 */
function handleDownloadTemplate() {
  proxy.download(
    "system/wlContract/warning/importTemplate",
    {},
    `contract_warning_template_${new Date().getTime()}.xlsx`
  );
}

/** 导入成功回调 */
function handleImportSuccess(response) {
  proxy.$modal.msgSuccess(response.msg);
  getList();
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.download-link {
  color: #409eff;
  cursor: pointer;
  margin-right: 8px;
  text-decoration: underline;
}
</style>
