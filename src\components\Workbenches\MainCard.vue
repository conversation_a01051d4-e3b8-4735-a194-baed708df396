<template>
  <div class="p-4 bg-white mb-4">
    <div
      class="card p-2 flex justify-center items-center"
      @click="$emit('click', cardData)"
    >
      <!-- <img :src="cardData.image" class="card-img w-full h-[200px]" /> -->
      <div class="card-footer w-full">
        <div class="card-info mt-[10px]">
          <span class="card-title">{{ cardData.title }}</span>
          <span class="card-badge"> 待处理：{{ cardData.count || 0 }} </span>
        </div>
        <img :src="cardData.icon" class="card-icon" />
      </div>
    </div>
  </div>
</template>

<script setup>
defineEmits(['click']);

defineProps({
  cardData: {
    type: Object,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.card {
  background-color: white;
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .card-img {
    object-fit: cover;
  }

  .card-info {
    display: flex;
    flex-direction: column;
  }

  .card-icon {
    width: 32px;
    height: 32px;
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.card-badge {
  color: #0570c0;
  font-size: 12px;
  margin-top: 8px;
}
</style>
