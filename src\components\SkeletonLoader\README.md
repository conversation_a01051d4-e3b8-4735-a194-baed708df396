# SkeletonLoader 骨架框加载组件

一个通用的骨架框加载组件，支持多种布局模式，可以在数据加载时提供良好的用户体验。

## 功能特性

- 🎨 支持多种布局模式：表单、表格、列表、详情页
- ⚙️ 高度可配置，支持自定义行数、列数、是否显示各个区域
- 🎯 专门适配表单布局，支持照片上传区域
- 📱 响应式设计，适配不同屏幕尺寸
- 🌙 支持深色主题
- ✨ 流畅的动画效果

## 基本用法

```vue
<template>
  <div>
    <!-- 基础表单骨架 -->
    <SkeletonLoader
      v-if="loading"
      layout="form"
      :form-rows="8"
      :form-cols="4"
      :show-photo="true"
    />
    
    <!-- 实际内容 -->
    <div v-else>
      <!-- 你的表单内容 -->
    </div>
  </div>
</template>

<script setup>
import SkeletonLoader from '@/components/SkeletonLoader/index.vue'
import { ref } from 'vue'

const loading = ref(true)

// 模拟数据加载
setTimeout(() => {
  loading.value = false
}, 2000)
</script>
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| layout | String | 'form' | 布局类型：'form', 'table', 'list', 'detail' |
| showSearch | Boolean | true | 是否显示搜索区域 |
| showToolbar | Boolean | true | 是否显示工具栏（表格布局） |
| showPagination | Boolean | true | 是否显示分页器 |
| showButtons | Boolean | true | 是否显示底部按钮 |
| showPhoto | Boolean | false | 是否显示照片上传区域（表单布局） |
| formRows | Number | 8 | 表单行数 |
| formCols | Number/Array | 4 | 每行列数，可以是数组为每行指定不同列数 |
| tableRows | Number | 10 | 表格行数 |
| tableCols | Number | 6 | 表格列数 |
| listItems | Number | 8 | 列表项数 |
| detailBlocks | Number | 3 | 详情页内容块数 |
| animationDuration | Number | 1500 | 动画持续时间（毫秒） |

## 使用场景

### 1. 表单页面

```vue
<!-- 适用于员工信息、项目信息等表单页面 -->
<SkeletonLoader
  layout="form"
  :show-search="true"
  :show-photo="true"
  :form-rows="16"
  :form-cols="4"
  :show-buttons="true"
/>
```

### 2. 表格页面

```vue
<!-- 适用于数据列表页面 -->
<SkeletonLoader
  layout="table"
  :show-toolbar="true"
  :show-pagination="true"
  :table-rows="10"
  :table-cols="6"
/>
```

### 3. 列表页面

```vue
<!-- 适用于消息列表、通知列表等 -->
<SkeletonLoader
  layout="list"
  :list-items="8"
  :show-search="false"
/>
```

### 4. 详情页面

```vue
<!-- 适用于详情查看页面 -->
<SkeletonLoader
  layout="detail"
  :detail-blocks="3"
  :show-search="false"
  :show-buttons="false"
/>
```

### 5. 自定义表单列数

```vue
<!-- 每行有不同的列数 -->
<SkeletonLoader
  layout="form"
  :form-rows="5"
  :form-cols="[3, 4, 2, 4, 1]"
/>
```

## 样式自定义

组件使用了CSS变量，可以通过覆盖样式来自定义外观：

```scss
.skeleton-loader {
  // 自定义动画持续时间
  --skeleton-animation-duration: 2000ms;
  
  // 自定义颜色
  .skeleton-item {
    background: linear-gradient(90deg, #your-color-1, #your-color-2, #your-color-1);
  }
}
```

## 最佳实践

1. **合理设置参数**：根据实际页面内容设置合适的行数和列数
2. **保持一致性**：在同一个应用中保持骨架框的风格一致
3. **适当的加载时间**：避免骨架框显示时间过短或过长
4. **响应式适配**：确保骨架框在不同设备上显示正常

## 在项目中注册

如果需要全局使用，可以在 `main.js` 中注册：

```js
import SkeletonLoader from '@/components/SkeletonLoader/index.vue'

app.component('SkeletonLoader', SkeletonLoader)
```

## 与现有项目集成

这个组件设计为即插即用，只需要：

1. 在需要加载效果的页面导入组件
2. 添加一个 `loading` 状态
3. 使用 `v-if`/`v-else` 切换显示状态
4. 根据页面类型配置相应的参数

就可以快速为你的页面添加专业的加载效果！ 