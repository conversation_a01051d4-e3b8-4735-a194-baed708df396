<template>
  <div>
    <!-- 导出按钮 -->
    <el-button class="custom-btn" @click="handleOpenDialog">
      {{ name }}
    </el-button>

    <!-- 导出设置弹窗 -->
    <el-dialog
      :title="name"
      v-model="dialogVisible"
      width="800px"
      append-to-body
    >
      <div class="export-container">
        <!-- 导出字段选择区域 -->
        <el-form
          ref="exportForm"
          label-position="top"
          :model="formData"
          :rules="rules"
          class="field-select"
        >
          <el-form-item
            label="是否自定义导出"
            prop="isCustomExport"
            label-position="left"
            label-width="130"
          >
            <el-radio-group v-model="formData.isCustomExport">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="formData.isCustomExport">
            <slot name="field" :field="formData"></slot>
            <el-form-item label="导出字段" prop="selectedFields">
              <el-select
                v-model="formData.selectedFields"
                multiple
                :collapse-tags="true"
                :max-collapse-tags="20"
                placeholder="请选择导出字段"
                style="width: 100%"
              >
                <template #header>
                  <el-checkbox
                    v-model="checkAll"
                    :indeterminate="indeterminate"
                    @change="handleCheckAll"
                  >
                    {{ checkAll ? "全不选" : "全选" }}
                  </el-checkbox>
                </template>

                <el-option
                  v-for="item in filteredFieldOptions"
                  :key="item.property"
                  :label="item.label"
                  :value="item.property"
                />
              </el-select>
            </el-form-item>

            <!-- 预览表格 -->
            <div
              class="preview-table"
              v-if="formData.selectedFields.length > 0"
            >
              <div class="preview-title">预览表格：</div>
              <div class="preview-div-table">
                <div class="preview-div-header">
                  <div
                    v-for="field in selectedFieldDetails"
                    :key="field.property"
                    class="preview-div-cell header-cell"
                  >
                    {{ field.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="flex justify-center">
          <el-button @click="dialogVisible = false" class="cancel-btn"
            >取消</el-button
          >
          <el-button type="primary" @click="handleExport" :loading="exporting"
            >导出</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  defineProps,
  defineEmits,
  getCurrentInstance,
  watch,
  nextTick,
} from "vue";

const { proxy } = getCurrentInstance();

const props = defineProps({
  name: {
    type: String,
    default: () => "导出设置",
  },
  // 导出URL（必传）
  url: {
    type: String,
    required: true,
  },
  // 导出文件名（默认为当前时间）
  fileName: {
    type: String,
    default: () => `导出数据_${new Date().getTime()}.xlsx`,
  },
  // 可选导出字段列表
  fieldOptions: {
    type: Array,
    default: () => [],
  },
  // 需要过滤的label数组
  filterLabels: {
    type: Array,
    default: () => [],
  },
  // 额外的请求参数
  params: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["export-success", "export-error"]);
const checkAll = ref(false);
const indeterminate = ref(false);
// 弹窗显示状态
const dialogVisible = ref(false);
// 已选择的字段
const selectedFields = ref([]);
// 导出加载状态
const exporting = ref(false);

// 当前使用的参数
const currentParams = ref({ ...props.params });

// 新增表单相关ref和rules
const exportForm = ref(null);
const formData = ref({
  selectedFields: selectedFields.value,
  isCustomExport: false,
});
const rules = ref({
  selectedFields: [
    { required: true, message: "请选择导出字段", trigger: "change" },
  ],
  isCustomExport: [
    { required: true, message: "请选择是否自定义导出", trigger: "change" },
  ],
});
// 保持formData和selectedFields同步
watch(selectedFields, (val) => {
  formData.value.selectedFields = val;
});
watch(
  () => formData.value.selectedFields,
  (val) => {
    selectedFields.value = val;
  }
);

// 监听params变化
watch(
  () => props.params,
  (newParams) => {
    currentParams.value = { ...newParams };
  },
  { deep: true }
);

// 监听fieldOptions变化，更新默认选中字段
watch(
  () => props.fieldOptions,
  (newFields) => {
    if (selectedFields.value.length === 0 && newFields.length > 0) {
      // 使用过滤后的字段列表
      const filtered = newFields.filter(
        (item) => !props.filterLabels.includes(item.label) && item.label
      );
      selectedFields.value = filtered.map((item) => item.property);
    }
  },
  { immediate: true }
);

// 已选择字段的详细信息
const selectedFieldDetails = computed(() => {
  return filteredFieldOptions.value.filter((item) =>
    selectedFields.value.includes(item.property)
  );
});

// 过滤后的字段选项
const filteredFieldOptions = computed(() => {
  if (!props.filterLabels.length) {
    return props.fieldOptions;
  }
  return props.fieldOptions.filter(
    (item) => !props.filterLabels.includes(item.label) && item.label
  );
});

// 未选择的字段
const unselectedFields = computed(() => {
  return filteredFieldOptions.value
    .filter((item) => !selectedFields.value.includes(item.property))
    .map((item) => item.property);
});
const handleCheckAll = (val) => {
  indeterminate.value = false;
  if (val) {
    formData.value.selectedFields = filteredFieldOptions.value.map(
      (_) => _.property
    );
  } else {
    formData.value.selectedFields = [];
  }
};
// 打开导出弹窗
const handleOpenDialog = () => {
  dialogVisible.value = true;
  if (selectedFields.value.length === 0) {
    selectedFields.value = filteredFieldOptions.value.map(
      (item) => item.property
    );
  }
  nextTick(() => {
    exportForm.value && exportForm.value.clearValidate();
  });
};

// 执行导出
const handleExport = () => {
  if (formData.value.isCustomExport) {
    exportForm.value.validate((valid) => {
      if (!valid) return;
      doExport();
    });
  } else {
    doExport();
  }
};

// 抽取导出逻辑
const doExport = () => {
  exporting.value = true;
  const data = JSON.parse(JSON.stringify(formData.value));
  delete data.selectedFields;
  try {
    const exportParams = {
      ...currentParams.value,
      ...data,
    };
    if (formData.value.isCustomExport) {
      exportParams.excludeFields = unselectedFields.value.join(",");
    }
    console.log("exportParams", exportParams);

    proxy.download(props.url, exportParams, props.fileName);
    emit("export-success");
    dialogVisible.value = false;
  } catch (error) {
    console.error("导出失败:", error);
    emit("export-error", error);
  } finally {
    exporting.value = false;
  }
};
</script>

<style scoped>
.export-container {
  padding: 10px;
}

.custom-option {
  margin-bottom: 20px;
}

.field-select {
  margin-top: 20px;
}

.select-label {
  margin-bottom: 20px;
}

.preview-table {
  margin-top: 20px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.preview-div-table {
  width: 100%;
  border: 1px solid #ebeef5;
  overflow: auto;
}

.preview-div-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.preview-div-row {
  display: flex;
}

.preview-div-cell {
  flex: 1;
  padding: 12px;
  border-right: 1px solid #ebeef5;

  white-space: nowrap;
}

.preview-div-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
