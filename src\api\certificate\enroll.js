import request from "@/utils/request";

// 查询注册/岗位证书信息列表
export function listCertificateEnroll(query) {
  return request({
    url: "/railway/wlCertificateEnroll/list",
    method: "get",
    params: query,
  });
}

// 查询注册/岗位证书信息详细
export function getCertificateEnroll(id) {
  return request({
    url: "/railway/wlCertificateEnroll/" + id,
    method: "get",
  });
}

// 新增注册/岗位证书信息
export function addCertificateEnroll(data) {
  return request({
    url: "/railway/wlCertificateEnroll",
    method: "post",
    data: data,
  });
}

// 修改注册/岗位证书信息
export function updateCertificateEnroll(data) {
  return request({
    url: "/railway/wlCertificateEnroll",
    method: "put",
    data: data,
  });
}

// 删除注册/岗位证书信息
export function delCertificateEnroll(id) {
  return request({
    url: "/railway/wlCertificateEnroll/" + id,
    method: "delete",
  });
}

// 证书延期
export function extendCertificateEnroll(data) {
  return request({
    url: "/railway/wlCertificateEnroll/extend",
    method: "post",
    data: data,
  });
}
// 查询退出注册岗位证书台账
export function listExitRegisterCertificate(query) {
  return request({
    url: "/railway/wlCertificateEnroll/retire/list",
    method: "get",
    params: query,
  });
}
