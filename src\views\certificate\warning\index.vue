<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-tabs class="px-4" v-model="activeTab">
        <el-tab-pane label="特种作业证书" name="specialOperation">
        </el-tab-pane>
        <el-tab-pane label="注册/岗位证书" name="registrationPost">
        </el-tab-pane>
      </el-tabs>
      <special-operation-certificate v-if="activeTab === 'specialOperation'" />
      <registration-post-certificate v-if="activeTab === 'registrationPost'" />
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import SpecialOperationCertificate from "./components/SpecialOperationCertificate.vue";
import RegistrationPostCertificate from "./components/RegistrationPostCertificate.vue";

const activeTab = ref("specialOperation");
</script>

<style lang="scss" scoped>
:deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: 0px !important;
  }
}
.el-tabs--border-card {
  border: none !important;
}

::v-deep(.el-tabs--border-card) {
  border-bottom: 0px;
  margin-bottom: 0px;
  .el-tabs__content {
    padding: 0px !important;
  }
}
.main-box-card {
  background-color: #fff !important;
}
</style>
