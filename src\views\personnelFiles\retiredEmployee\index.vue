<template>
  <div class="app-container">
    <!-- 履历导出对话框 -->
    <el-dialog
      v-model="resumeExportOpen"
      title="履历导出"
      width="500px"
      append-to-body
    >
      <el-form
        ref="resumeExportFormRef"
        :model="resumeExportForm"
        :rules="resumeExportRules"
        label-width="100px"
      >
        <el-form-item label="开始时间" prop="beginTime">
          <el-date-picker
            v-model="resumeExportForm.beginTime"
            type="date"
            placeholder="选择开始时间"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="resumeExportForm.endTime"
            type="date"
            placeholder="选择结束时间"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="单位名称">
          <RemoteSelect
            v-model="resumeExportForm.projectId"
            url="/system/dept/list"
            labelKey="deptName"
            valueKey="deptId"
            :extraParams="{ parentId: '0' }"
            placeholder="请选择单位"
          />
        </el-form-item>
        <el-form-item label="职务">
          <el-select
            v-model="resumeExportForm.professionalTitle"
            placeholder="请选择职务"
            style="width: 100%"
          >
            <el-option
              v-for="dict in professional_title"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职务级别" prop="positionLevel">
          <el-select
            v-model="resumeExportForm.positionLevel"
            placeholder="请选择职务级别"
            style="width: 100%"
          >
            <el-option
              v-for="dict in position_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="resumeExportOpen = false" class="cancel-btn"
            >取 消</el-button
          >
          <el-button type="primary" @click="exportResumeData">导 出</el-button>
        </div>
      </template>
    </el-dialog>

    <div class="main-box-card">
      <el-card class="box-card">
        <!-- tab筛选 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="mb-4">
          <el-tab-pane label="已退休" name="R"></el-tab-pane>
          <el-tab-pane label="待退休" name="B"></el-tab-pane>
        </el-tabs>

        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              class="w-[200px]"
              v-model="queryParams.keyword"
              placeholder="请输入关键字"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="社保身份" prop="socialSecurityStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.socialSecurityStatus"
              placeholder="请选择社保身份"
              clearable
            >
              <el-option
                v-for="dict in social_security_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="从事工作" prop="jobContent">
            <el-select
              class="w-[200px]"
              v-model="queryParams.jobContent"
              placeholder="请选择从事工作"
              clearable
            >
              <el-option
                v-for="dict in work_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="职称" prop="titleCategory">
            <el-select
              class="w-[200px]"
              v-model="queryParams.titleCategory"
              placeholder="请选择职称"
              clearable
            >
              <el-option
                v-for="dict in title_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="人员类型" prop="personnelType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.personnelType"
              placeholder="请选择人员类型"
              clearable
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExportBasic"
              >基本信息导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExportResume"
              >履历导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExportAudit"
              >干审表导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="retiredList">
          <el-table-column label="姓名" align="center" prop="name" />

          <el-table-column
            label="社保身份"
            align="center"
            prop="socialSecurityStatus"
          >
            <template #default="scope">
              <dict-tag
                :options="social_security_status"
                :value="scope.row.socialSecurityStatus"
              />
            </template>
          </el-table-column>

          <el-table-column label="档案年龄" align="center" prop="archiveAge" />

          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="所在单位" align="center" prop="projectName" />
          <el-table-column label="岗位" align="center" prop="postName" />

          <el-table-column label="二级机构" align="center" prop="deptName" />
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="学历"
            align="center"
            prop="highestEducation"
          />
          <el-table-column label="职称" align="center" prop="titleCategory">
            <template #default="scope">
              <dict-tag
                :options="title_category"
                :value="scope.row.titleCategory"
              />
            </template>
          </el-table-column>
          <el-table-column label="从事工作" align="center" prop="jobContent">
            <template #default="scope">
              <dict-tag :options="work_type" :value="scope.row.jobContent" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="120"
          >
            <template #default="scope">
              <el-button type="text" @click="handleInfo(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { listRetiredEmployee } from '@/api/personnelFiles/retiredEmployee';
import RemoteSelect from '@/components/RemoteSelect/index.vue';
const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const {
  sys_user_sex,
  social_security_status,
  personnel_type,
  title_category,
  work_type,
  professional_title,
  position_level,
} = proxy.useDict(
  'sys_user_sex',
  'social_security_status',
  'personnel_type',
  'title_category',
  'work_type',
  'professional_title',
  'position_level'
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 退休人员表格数据
const retiredList = ref([]);
// 当前激活的tab
const activeTab = ref('R');

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  socialSecurityStatus: undefined,
  staffType: undefined,
  status: 'R', // 默认查询已退休人员
});

// 履历导出参数
const resumeExportOpen = ref(false);
const resumeExportForm = ref({
  beginTime: '',
  endTime: '',
  projectId: undefined,
  professionalTitle: undefined,
  positionLevel: undefined,
});
// 验证结束时间必须大于开始时间
function validateEndTime(rule, value, callback) {
  if (resumeExportForm.value.beginTime && value) {
    if (new Date(value) <= new Date(resumeExportForm.value.beginTime)) {
      callback(new Error('结束时间必须大于开始时间'));
    } else {
      callback();
    }
  } else {
    callback();
  }
}
const resumeExportRules = ref({
  beginTime: [
    { required: true, message: '开始时间不能为空', trigger: 'change' },
  ],
  endTime: [
    { required: true, message: '结束时间不能为空', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' },
  ],
});

/** 查询退休人员列表 */
function getList() {
  loading.value = true;
  listRetiredEmployee(queryParams.value).then((res) => {
    retiredList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

/** tab切换操作 */
function handleTabClick(tab) {
  queryParams.value.status = tab.props.name;
  queryParams.value.pageNum = 1;
  getList();
}

/** 查看按钮操作 */
function handleInfo(row) {
  router.push({
    path: '/personnelFiles/incumbentEmployee/addOrEdit',
    query: { id: row.id, type: 'check' },
    meta: { activeMenu: location.pathname },
  });
}

/** 基本信息导出按钮操作 */
function handleExportBasic() {
  proxy.download(
    'railway/wlStaffInfo/export',
    {
      ...queryParams.value,
    },
    `${
      activeTab.value === 'R' ? '已退休' : '待退休'
    }人员基本信息_${new Date().getTime()}.xlsx`
  );
}

/** 履历导出按钮操作 */
function handleExportResume() {
  // 重置表单数据
  resumeExportForm.value = {
    beginTime: '',
    endTime: '',
    projectId: undefined,
    professionalTitle: undefined,
    positionLevel: undefined,
  };
  resumeExportOpen.value = true;
}

/** 履历导出数据 */
function exportResumeData() {
  proxy.$refs['resumeExportFormRef'].validate((valid) => {
    if (valid) {
      exportResumeDataHandle();
    }
  });
}

function exportResumeDataHandle() {
  proxy.download(
    'railway/wlStaffInfo/curriculumVitaeExport',
    {
      ...queryParams.value,
      ...resumeExportForm.value,
    },
    `${
      activeTab.value === 'R' ? '已退休' : '待退休'
    }人员履历导出_${new Date().getTime()}.zip`
  );
  resumeExportOpen.value = false;
}

/** 干审表导出按钮操作 */
function handleExportAudit() {
  proxy.download(
    'railway/wlStaffInfo/dryReviewExport',
    {
      ...queryParams.value,
    },
    `${activeTab.value === 'R' ? '已退休' : '待退休'}人员干审核表导出.zip`
  );
}

/** 简历导出按钮操作 */
function handleExportCV() {
  proxy.download(
    'railway/wlStaffInfo/exportCV',
    {
      ...queryParams.value,
    },
    `${
      activeTab.value === 'R' ? '已退休' : '待退休'
    }人员简历_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
