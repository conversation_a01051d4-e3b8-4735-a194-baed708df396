import request from '@/utils/request';

// 查询培训计划项目列表
export function listProject(query) {
  return request({
    url: '/railway/wlTrainingPlan/project/list',
    method: 'get',
    params: query,
  });
}

// 查询培训计划列表
export function listPlan(query) {
  return request({
    url: '/railway/wlTrainingPlan/list',
    method: 'get',
    params: query,
  });
}

// 查询培训计划详细
export function getPlan(id) {
  return request({
    url: '/railway/wlTrainingPlan/' + id,
    method: 'get',
  });
}

// 新增培训计划
export function addPlan(data) {
  return request({
    url: '/railway/wlTrainingPlan',
    method: 'post',
    data: data,
  });
}

// 修改培训计划
export function updatePlan(data) {
  return request({
    url: '/railway/wlTrainingPlan',
    method: 'put',
    data: data,
  });
}

// 删除培训计划
export function delPlan(id) {
  return request({
    url: '/railway/wlTrainingPlan/' + id,
    method: 'delete',
  });
}

// 导出培训计划
export function exportPlan(query) {
  return request({
    url: '/railway/wlTrainingPlan/export',
    method: 'get',
    params: query,
  });
}

// 上传培训计划材料
export function uploadPlanMaterial(data) {
  return request({
    url: '/railway/wlTrainingPlan/upload',
    method: 'post',
    data: data,
  });
}

// 下载培训计划模板
export function downloadTemplate() {
  return request({
    url: '/railway/wlTrainingPlan/template',
    method: 'get',
  });
}

// 导入培训计划数据
export function importPlan(data) {
  return request({
    url: '/railway/wlTrainingPlan/import',
    method: 'post',
    data: data,
  });
}
