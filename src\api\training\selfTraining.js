import request from '@/utils/request'

// 查询自培管理列表
export function listWlTrainingSelf(query) {
    return request({
        url: '/railway/wlTrainingSelf/list',
        method: 'get',
        params: query
    })
}

// 查询自培管理详细
export function getWlTrainingSelf(id) {
    return request({
        url: '/railway/wlTrainingSelf/' + id,
        method: 'get'
    })
}

// 新增自培管理
export function addWlTrainingSelf(data) {
    return request({
        url: '/railway/wlTrainingSelf',
        method: 'post',
        data: data
    })
}

// 修改自培管理
export function updateWlTrainingSelf(data) {
    return request({
        url: '/railway/wlTrainingSelf',
        method: 'put',
        data: data
    })
}

// 删除自培管理
export function delWlTrainingSelf(id) {
    return request({
        url: '/railway/wlTrainingSelf/' + id,
        method: 'delete'
    })
}

// 发起自培管理流程
export function subLaunchProcess(data) {
    return request({
        url: '/railway/wlTrainingSelf/launchProcess',
        method: 'post',
        data: data
    })
}
// 下载附件
export function downloadWlTrainingSelf(id) {
    return request({
        url: '/railway/wlTrainingSelf/download/' + id,
        method: 'get',
        responseType: "blob"
    })
}
