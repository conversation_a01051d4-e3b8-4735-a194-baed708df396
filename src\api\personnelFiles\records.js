import request from "@/utils/request";

// 查询人员变动记录列表
export function listWlStaffChanges(query) {
  return request({
    url: "/railway/wlStaffChanges/list",
    method: "get",
    params: query,
  });
}

// 查询人员变动记录详细
export function getWlStaffChanges(id) {
  return request({
    url: "/railway/wlStaffChanges/" + id,
    method: "get",
  });
}

// 新增人员变动记录
export function addWlStaffChanges(data) {
  return request({
    url: "/railway/wlStaffChanges",
    method: "post",
    data: data,
  });
}

// 编辑人员变动记录
export function updateWlStaffChanges(data) {
  return request({
    url: "/railway/wlStaffChanges",
    method: "put",
    data: data,
  });
}

// 删除人员变动记录
export function delWlStaffChanges(id) {
  return request({
    url: "/railway/wlStaffChanges/" + id,
    method: "delete",
  });
}

// 提交审核人员变动记录
export function submitWlStaffChanges(data) {
  return request({
    url: "/railway/wlStaffChanges/submit",
    method: "post",
    data,
  });
}
