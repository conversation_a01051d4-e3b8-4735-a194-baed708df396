<template>
  <div class="small-card horizontal-card p-8" @click="$emit('click', cardData)">
    <img :src="cardData.image" class="small-card-img" />
    <div class="small-card-content">
      <span class="card-title">{{ cardData.title }}</span>
      <div class="card-footer">
        <span class="card-badge"> 待处理：{{ cardData.count || 0 }} </span>
      </div>
    </div>
  </div>
</template>

<script setup>
defineEmits(["click"]);

defineProps({
  cardData: {
    type: Object,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.small-card {
  background-color: white;
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 28px 20px;
  gap: 12px;
  flex: 1;

  .small-card-img {
    width: 60px;
    object-fit: contain;
  }

  .small-card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    gap: 8px;
  }
}

.card-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.card-badge {
  color: #0570c0;
  font-size: 12px;
  margin-top: 8px;
}
</style>
