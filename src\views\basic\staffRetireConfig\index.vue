<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="配置状态"
              clearable
              class="w-[200px]"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleAdd">新增</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="staffRetireConfigList">
          <el-table-column type="index" label="序号" width="50" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="社保身份" align="center" prop="identity">
            <template #default="scope">
              <dict-tag
                :options="social_security_status"
                :value="scope.row.identity"
              />
            </template>
          </el-table-column>
          <el-table-column label="条件" align="center" prop="compute">
            <template #default="scope">
              <dict-tag
                :options="calculation_rules"
                :value="scope.row.compute"
              />
            </template>
          </el-table-column>
          <el-table-column label="退休年龄" align="center" prop="age" />

          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="150"
            fixed="right"
          >
            <template #default="scope">
              <el-button type="text" @click="handleUpdate(scope.row)"
                >编辑</el-button
              >
              <!-- <el-button
                type="text"
                @click="handleDelete(scope.row)"
                
                >删除</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或修改退休年龄配置对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="staffRetireConfigForm"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="社保身份" prop="identity">
          <el-select v-model="form.identity" placeholder="请选择身份">
            <el-option
              v-for="dict in social_security_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="退休年龄" prop="age">
          <el-input-number
            class="w-full"
            :controls="false"
            v-model="form.age"
            :precision="0"
            :step="1"
            :min="0"
            :max="100"
            placeholder="请输入退休年龄"
          />
        </el-form-item>
        <el-form-item label="计算规则" prop="compute">
          <el-select v-model="form.compute" placeholder="请选择计算规则">
            <el-option
              v-for="dict in calculation_rules"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import {
  listStaffRetireConfig,
  getStaffRetireConfig,
  addStaffRetireConfig,
  updateStaffRetireConfig,
  delStaffRetireConfig,
} from "@/api/basic/staffRetireConfig";

const { proxy } = getCurrentInstance();

// 字典数据
const {
  sys_user_sex,
  sys_normal_disable,
  social_security_status,
  calculation_rules,
} = proxy.useDict(
  "sys_normal_disable",
  "social_security_status",
  "calculation_rules",
  "sys_user_sex"
);

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 退休年龄配置表格数据
const staffRetireConfigList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  gender: undefined,
  identity: undefined,
  status: undefined,
});

// 表单参数
const form = ref({
  id: undefined,
  gender: undefined,
  identity: undefined,
  age: 60,
  compute: undefined,
  status: "0",
});

// 表单校验
const rules = ref({
  compute: [
    {
      required: true,
      message: "请输入计算规则",
      trigger: "change",
    },
  ],
  gender: [{ required: true, message: "性别不能为空", trigger: "change" }],
  identity: [{ required: true, message: "身份不能为空", trigger: "change" }],
  age: [{ required: true, message: "退休年龄不能为空", trigger: "blur" }],
  status: [{ required: true, message: "状态不能为空", trigger: "blur" }],
});

/** 查询退休年龄配置列表 */
function getList() {
  loading.value = true;
  listStaffRetireConfig(queryParams.value).then((response) => {
    staffRetireConfigList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    gender: undefined,
    identity: undefined,
    age: 60,
    compute: undefined,
    status: "0",
  };
  proxy.resetForm("staffRetireConfigForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加退休年龄配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getStaffRetireConfig(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改退休年龄配置";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["staffRetireConfigForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateStaffRetireConfig(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addStaffRetireConfig(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id;
  proxy.$modal
    .confirm('是否确认删除退休年龄配置编号为"' + id + '"的数据项？')
    .then(function () {
      return delStaffRetireConfig(id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
div /deep/ .el-input-number .el-input__inner {
  text-align: left !important;
}
</style>
