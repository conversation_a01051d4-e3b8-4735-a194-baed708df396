{"name": "tabe", "version": "3.8.8", "description": "三处人力资源智能化信息系统", "author": "微联", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:test": "vite build --mode test", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/jz01/energy-two-front.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "autofit.js": "^3.2.8", "axios": "0.28.1", "clipboard": "2.0.11", "echarts": "5.5.1", "element-plus": "2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-base64": "^3.7.7", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "2.1.7", "splitpanes": "3.1.5", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "sass": "1.77.5", "tailwindcss": "^3.4.15", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}