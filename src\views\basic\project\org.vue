<template>
  <div class="app-container">
    <!-- 项目基础信息 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>项目基础信息</span>
        </div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="项目名称">{{
          projectInfo.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{
          projectInfo.leaderUserName
        }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{
          projectInfo.phone
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_normal_disable" :value="projectInfo.status" />
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 机构信息 -->
    <el-card class="box-card mt-4">
      <template #header>
        <div class="card-header">
          <span>机构信息</span>
        </div>
      </template>

      <el-row :gutter="10" class="mb-4">
        <el-col :span="1.5">
          <el-button type="primary" class="custom-btn" @click="handleAddOrg"
            >新增机构</el-button
          >
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="orgList">
        <el-table-column type="index" label="序号" width="50" align="center">
          <template #default="scope">
            <span>{{ getIndex(scope.$index) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="机构名称" align="center" prop="deptName" />
        <el-table-column
          label="流动津贴"
          align="center"
          prop="mobileAllowance"
        />
        <el-table-column
          label="隧道津贴"
          align="center"
          prop="tunnelAllowance"
        />
        <el-table-column
          label="绩效工资基数"
          align="center"
          prop="performanceBase"
        />
        <el-table-column label="操作" align="center" width="160" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleUpdateOrg(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" @click="handleDeleteOrg(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getOrgList"
      />
    </el-card>

    <!-- 添加或编辑机构对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="orgForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="机构名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入机构名称" />
        </el-form-item>
        <el-form-item label="流动津贴" prop="mobileAllowance">
          <el-input-number
            v-model="form.mobileAllowance"
            :precision="2"
            :step="100"
            :min="0"
            :controls="false"
            placeholder="请输入流动津贴"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="隧道津贴" prop="tunnelAllowance">
          <el-input-number
            v-model="form.tunnelAllowance"
            :precision="2"
            :step="100"
            :min="0"
            :controls="false"
            placeholder="请输入隧道津贴"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="绩效工资基数" prop="performanceBase">
          <el-input-number
            v-model="form.performanceBase"
            :precision="2"
            :step="100"
            :min="0"
            :controls="false"
            placeholder="请输入绩效工资基数"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getProject } from "@/api/basic/project";
import {
  listProjectOrg,
  getProjectOrg,
  addProjectOrg,
  updateProjectOrg,
  delProjectOrg,
} from "@/api/basic/project";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

// 字典数据
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 遮罩层
const loading = ref(false);
// 按钮加载状态
const buttonLoading = ref(false);
// 总条数
const total = ref(0);
// 机构表格数据
const orgList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 项目信息
const projectInfo = ref({});
// 项目ID
const projectId = route.query.projectId;

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectId: projectId,
});

// 表单参数
const form = ref({
  id: undefined,
  projectId: projectId,
  deptName: undefined,
  mobileAllowance: 0,
  tunnelAllowance: 0,
  performanceBase: 0,
});

// 表单校验
const rules = ref({
  deptName: [{ required: true, message: "机构名称不能为空", trigger: "blur" }],
  mobileAllowance: [
    { required: true, message: "流动津贴不能为空", trigger: "blur" },
  ],
  tunnelAllowance: [
    { required: true, message: "隧道津贴不能为空", trigger: "blur" },
  ],
  performanceBase: [
    { required: true, message: "绩效工资基数不能为空", trigger: "blur" },
  ],
});

/** 返回按钮操作 */
function goBack() {
  router.go(-1);
}

/** 获取项目信息 */
function getProjectInfo() {
  loading.value = true;
  getProject(projectId).then((response) => {
    projectInfo.value = response.data;
    loading.value = false;
  });
}

/** 查询机构列表 */
function getOrgList() {
  loading.value = true;
  listProjectOrg(queryParams.value).then((response) => {
    orgList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    projectId: projectId,
    deptName: undefined,
    mobileAllowance: 0,
    tunnelAllowance: 0,
    performanceBase: 0,
  };
  proxy.resetForm("orgForm");
}

/** 获取序号 */
function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

/** 新增按钮操作 */
function handleAddOrg() {
  reset();
  open.value = true;
  title.value = "添加机构";
}

/** 编辑按钮操作 */
function handleUpdateOrg(row) {
  reset();
  const id = row.id;
  getProjectOrg(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "编辑机构";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["orgForm"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateProjectOrg(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("编辑成功");
            open.value = false;
            getOrgList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      } else {
        addProjectOrg(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getOrgList();
            buttonLoading.value = false;
          })
          .catch(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDeleteOrg(row) {
  const ids = row.id;
  proxy.$modal
    .confirm('是否确认删除机构名称为"' + row.deptName + '"的数据项?')
    .then(function () {
      return delProjectOrg(ids);
    })
    .then(() => {
      getOrgList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

onMounted(() => {
  getProjectInfo();
  getOrgList();
});
</script>

<style scoped>
div :deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
