<template>
  <div class="self-built-team-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="年月" prop="yearMonth">
        <el-date-picker
          v-model="queryParams.yearMonth"
          type="month"
          placeholder="请选择年月"
          value-format="YYYY-MM"
          class="w-[200px]"
          clearable
        />
      </el-form-item>
      <el-form-item label="建设状态" prop="constructionStatus">
        <el-select
          v-model="queryParams.constructionStatus"
          placeholder="请选择建设状态"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in construction_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="机构属性" prop="orgAttribute">
        <el-select
          v-model="queryParams.orgAttribute"
          placeholder="请选择机构属性"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in organization_attribute"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员状态" prop="positionStatus">
        <el-select
          v-model="queryParams.positionStatus"
          placeholder="请选择人员状态"
          class="w-[200px]"
          clearable
        >
          <el-option
            v-for="dict in position_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery" class="reset-btn">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计数据展示 -->
    <div class="statistics-container">
      <div class="statistics-grid">
        <div class="stat-item">
          <div class="stat-title">总人数</div>
          <div class="stat-content">
            <span class="stat-value">{{ total }}</span>
          </div>
        </div>
      </div>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" class="custom-btn" @click="handleExport">
          导出自建班组
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="teamList" border>
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          <span>{{ getIndex(scope.$index) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="所在单位"
        align="center"
        prop="projectName"
        min-width="120"
      />
      <el-table-column label="姓名" align="center" prop="name" width="100" />
      <el-table-column
        label="岗位"
        align="center"
        prop="postName"
        min-width="100"
      />
      <el-table-column label="性别" align="center" prop="gender" width="80">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center" prop="age" width="80" />
      <el-table-column
        label="岗位状态"
        align="center"
        prop="positionStatus"
        min-width="100"
      >
        <template #default="scope">
          <dict-tag
            :options="position_status"
            :value="scope.row.positionStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="用工单位（挂号劳务公司名称）"
        align="center"
        prop="employers"
        min-width="180"
      />
      <el-table-column
        label="自建班组名称"
        align="center"
        prop="selfTeam"
        min-width="120"
      />
      <el-table-column
        label="班组从事工作面内容"
        align="center"
        prop="teamWorkContent"
        min-width="150"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue';
import { listPersonnelInfo } from '@/api/instrument/dynamicPanel';
const { proxy } = getCurrentInstance();
const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const teamList = ref([]);

const {
  sys_user_sex,
  construction_status,
  organization_attribute,
  position_status,
  work_type,
} = proxy.useDict(
  'sys_user_sex',
  'construction_status',
  'organization_attribute',
  'position_status',
  'work_type'
);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  yearMonth: undefined,
  constructionStatus: undefined,
  orgAttribute: undefined,
  positionStatus: undefined,
  staffType: 2,
});

function getList() {
  loading.value = true;
  listPersonnelInfo(queryParams.value).then((response) => {
    teamList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm('queryForm');
  handleQuery();
}

function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

function handleExport() {
  proxy.download(
    '/dynamics/export',
    { ...queryParams.value },
    `自建班组_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.self-built-team-container {
  padding: 0;
}

.statistics-container {
  padding: 16px 0;
}

.statistics-grid {
  display: flex;
  justify-content: flex-start;
}

.stat-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  width: 200px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.stat-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-weight: 700;
  color: #2f7bff;
  font-size: 20px;
}
</style>
