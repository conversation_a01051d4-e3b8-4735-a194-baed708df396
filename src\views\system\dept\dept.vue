<template>
  <div>
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="名称" prop="deptName">
            <el-input
              v-model="queryParams.deptName"
              placeholder="请输入名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="状态"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button class="reset-btn" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              class="custom-btn"
              @click="handleAdd"
              v-hasPermi="['system:dept:add']"
              >新增</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="deptList"
          row-key="deptId"
          :default-expand-all="isExpandAll"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        >
          <el-table-column
            prop="deptName"
            label="名称"
            width="400"
          ></el-table-column>
          <el-table-column label="机构属性" align="center" prop="orgAttribute">
            <template #default="scope">
              <dict-tag
                :options="organization_attribute"
                :value="scope.row.orgAttribute"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="建设状态"
            align="center"
            prop="constructionStatus"
          >
            <template #default="scope">
              <dict-tag
                :options="construction_status"
                :value="scope.row.constructionStatus"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="开工日期"
            align="center"
            prop="startDate"
            width="120"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="竣工日期"
            align="center"
            prop="completionDate"
            width="120"
          >
            <template #default="scope">
              <span>{{
                parseTime(scope.row.completionDate, '{y}-{m}-{d}')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="工程类型" align="center" prop="projectType">
            <template #default="scope">
              <dict-tag
                :options="project_type"
                :value="scope.row.projectType"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="orderNum"
            label="排序"
            width="200"
          ></el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="200"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:dept:edit']"
                >编辑</el-button
              >
              <!-- <el-button
                v-if="scope.row.parentId == 0"
                link
                type="primary"
                icon="Plus"
                @click="handleAdd(scope.row)"
                v-hasPermi="['system:dept:add']"
                >新增</el-button
              > -->
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:dept:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 添加或编辑部门对话框 -->
    <el-dialog :title="title" v-model="open" width="750px" append-to-body>
      <el-form ref="deptRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机构属性" prop="orgAttribute">
              <el-select
                v-model="form.orgAttribute"
                placeholder="请选择机构属性"
                style="width: 100%"
                @change="handleOrgAttributeChange"
              >
                <el-option
                  v-for="dict in organization_attribute"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 当机构属性值等于3时显示建设相关字段 -->
          <template v-if="form.orgAttribute === '3'">
            <el-col :span="12">
              <el-form-item label="建设状态" prop="constructionStatus">
                <el-select
                  v-model="form.constructionStatus"
                  placeholder="请选择建设状态"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in construction_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开工日期" prop="startDate">
                <el-date-picker
                  v-model="form.startDate"
                  type="date"
                  placeholder="请选择开工日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="竣工日期" prop="completionDate">
                <el-date-picker
                  v-model="form.completionDate"
                  type="date"
                  placeholder="请选择竣工日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="工程类型" prop="projectType">
                <el-select
                  v-model="form.projectType"
                  placeholder="请选择工程类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in project_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </template>

          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number
                class="w-full"
                v-model="form.orderNum"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <RemoteSelect
                v-model="form.leader"
                url="/system/user/list"
                labelKey="nickName"
                valueKey="userId"
                placeholder="请选择负责人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入邮箱"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :value="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dept">
import {
  listDept,
  getDept,
  delDept,
  addDept,
  updateDept,
  listDeptExcludeChild,
} from '@/api/system/dept';

const { proxy } = getCurrentInstance();
const {
  sys_normal_disable,
  organization_attribute,
  construction_status,
  project_type,
} = proxy.useDict(
  'sys_normal_disable',
  'organization_attribute',
  'construction_status',
  'project_type'
);

const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref('');
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);
const total = ref(0);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptName: undefined,
    status: undefined,
    parentId: 0,
  },
  rules: {
    orgAttribute: [
      { required: true, message: '机构属性不能为空', trigger: 'change' },
    ],
    constructionStatus: [
      { required: true, message: '建设状态不能为空', trigger: 'change' },
    ],
    startDate: [
      { required: true, message: '开工日期不能为空', trigger: 'change' },
    ],
    completionDate: [
      { required: true, message: '竣工日期不能为空', trigger: 'change' },
    ],
    leader: [{ required: true, message: '负责人不能为空', trigger: 'blur' }],
    parentId: [{ required: true, message: '上级不能为空', trigger: 'blur' }],
    deptName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
    projectType: [
      { required: true, message: '请选择工程类型', trigger: 'blur' },
    ],
    orderNum: [
      { required: true, message: '显示排序不能为空', trigger: 'blur' },
    ],
    email: [
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    phone: [
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur',
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
  loading.value = true;
  listDept(queryParams.value).then((response) => {
    deptList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    deptName: undefined,
    orgAttribute: undefined,
    constructionStatus: undefined,
    startDate: undefined,
    completionDate: undefined,
    orderNum: 0,
    leader: undefined,
    phone: undefined,
    email: undefined,
    status: '0',
  };
  proxy.resetForm('deptRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

/** 机构属性变更处理 */
function handleOrgAttributeChange(value) {
  // 当机构属性不等于3时，清空建设相关字段
  if (value !== '3') {
    form.value.constructionStatus = undefined;
    form.value.startDate = undefined;
    form.value.completionDate = undefined;
  }
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  listDept().then((response) => {
    deptOptions.value = proxy.handleTree(response.data, 'deptId');
  });
  if (row != undefined) {
    form.value.parentId = row.deptId;
  }
  open.value = true;
  title.value = '添加项目';
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  listDeptExcludeChild(row.deptId).then((response) => {
    deptOptions.value = proxy.handleTree(response.data, 'deptId');
  });
  getDept(row.deptId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = `编辑${row.parentId == 0 ? '项目' : '机构'}`;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['deptRef'].validate((valid) => {
    if (valid) {
      form.value.parentId = form.value.parentId || '0';
      if (form.value.deptId != undefined) {
        updateDept(form.value).then((response) => {
          proxy.$modal.msgSuccess('编辑成功');
          open.value = false;
          getList();
        });
      } else {
        addDept(form.value).then((response) => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除名称为"' + row.deptName + '"的数据项?')
    .then(function () {
      return delDept(row.deptId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
}

getList();
</script>
