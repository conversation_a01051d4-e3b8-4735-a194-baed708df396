import request from "@/utils/request";
// 查询左侧树形电表信息
export function listAllDevice(query) {
  return request({
    url: "/electrical/device/listAll",
    method: "get",
    params: query,
  });
}

// 查询电设备列表
export function listDevice(query) {
  return request({
    url: "/electrical/device/list",
    method: "get",
    params: query,
  });
}

// 查询电设备详细
export function getDevice(deviceId) {
  return request({
    url: "/electrical/device/" + deviceId,
    method: "get",
  });
}

// 新增电设备
export function addDevice(data) {
  return request({
    url: "/electrical/device",
    method: "post",
    data: data,
  });
}

// 编辑电设备
export function updateDevice(data) {
  return request({
    url: "/electrical/device",
    method: "put",
    data: data,
  });
}

// 删除电设备
export function delDevice(deviceId) {
  return request({
    url: "/electrical/device/" + deviceId,
    method: "delete",
  });
}
