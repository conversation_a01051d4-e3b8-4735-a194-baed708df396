import request from '@/utils/request'

// 查询兼职教师授课信息列表
export function listWlTeacher(query) {
    return request({
        url: '/railway/wlTeacher/list',
        method: 'get',
        params: query
    })
}

// 查询兼职教师授课信息详细
export function getWlTeacher(id) {
    return request({
        url: '/railway/wlTeacher/' + id,
        method: 'get'
    })
}

// 新增兼职教师授课信息
export function addWlTeacher(data) {
    return request({
        url: '/railway/wlTeacher',
        method: 'post',
        data: data
    })
}

// 修改兼职教师授课信息
export function updateWlTeacher(data) {
    return request({
        url: '/railway/wlTeacher',
        method: 'put',
        data: data
    })
}

// 删除兼职教师授课信息
export function delWlTeacher(id) {
    return request({
        url: '/railway/wlTeacher/' + id,
        method: 'delete'
    })
}