<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              class="w-[200px]"
              v-model="queryParams.keyword"
              placeholder="请输入关键字"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="社保身份" prop="socialSecurityStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.socialSecurityStatus"
              placeholder="请选择社保身份"
              clearable
            >
              <el-option
                v-for="dict in social_security_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="从事工作" prop="jobContent">
            <el-select
              class="w-[200px]"
              v-model="queryParams.jobContent"
              placeholder="请选择从事工作"
              clearable
            >
              <el-option
                v-for="dict in work_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="职称" prop="titleCategory">
            <el-select
              class="w-[200px]"
              v-model="queryParams.titleCategory"
              placeholder="请选择职称"
              clearable
            >
              <el-option
                v-for="dict in title_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="人员类型" prop="personnelType">
            <el-select
              class="w-[200px]"
              v-model="queryParams.personnelType"
              placeholder="请选择人员类型"
              clearable
            >
              <el-option
                v-for="dict in personnel_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="预警状态" prop="warningStatus">
            <el-select
              class="w-[200px]"
              v-model="queryParams.warningStatus"
              placeholder="请选择预警状态"
              clearable
            >
              <el-option
                v-for="dict in certificate_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="预警级别" prop="warnLevel">
            <RemoteSelect
              class="w-[200px]"
              v-model="queryParams.warnLevel"
              url="/railway/wlEarlyWarning/list"
              labelKey="ruleName"
              valueKey="id"
              placeholder="请选择预警级别"
              clearable
              :extraParams="{ ruleType: 'RETIREE' }"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button class="custom-btn" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="warningList">
          <el-table-column label="姓名" align="center" prop="name" />

          <el-table-column
            label="社保身份"
            align="center"
            prop="socialSecurityStatus"
          >
            <template #default="scope">
              <dict-tag
                :options="social_security_status"
                :value="scope.row.socialSecurityStatus"
              />
            </template>
          </el-table-column>

          <el-table-column label="档案年龄" align="center" prop="archiveAge" />

          <el-table-column label="性别" align="center" prop="gender">
            <template #default="scope">
              <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
            </template>
          </el-table-column>
          <el-table-column label="所在单位" align="center" prop="projectName" />
          <el-table-column label="岗位" align="center" prop="postName" />

          <el-table-column label="二级机构" align="center" prop="deptName" />
          <el-table-column label="人员类型" align="center" prop="staffType">
            <template #default="scope">
              <dict-tag
                :options="personnel_type"
                :value="scope.row.staffType"
              />
            </template>
          </el-table-column>
          <el-table-column label="学历" align="center" prop="highestEducation">
            <template #default="scope">
              <dict-tag
                :options="education_type"
                :value="scope.row.highestEducation"
              />
            </template>
          </el-table-column>
          <el-table-column label="职称" align="center" prop="titleCategory">
            <template #default="scope">
              <dict-tag
                :options="title_category"
                :value="scope.row.titleCategory"
              />
            </template>
          </el-table-column>
          <el-table-column label="从事工作" align="center" prop="jobContent">
            <template #default="scope">
              <dict-tag :options="work_type" :value="scope.row.jobContent" />
            </template>
          </el-table-column>

          <el-table-column label="预警级别" align="center" prop="warnLevel">
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            fixed="right"
            width="180"
          >
            <template #default="scope">
              <el-button type="text" @click="handleInfo(scope.row)"
                >查看</el-button
              >
              <!-- <el-button type="text" @click="handleRetire(scope.row)"
                >退休</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  listRetirementWarning,
  handleRetirement,
  exportRetirementWarning,
} from "@/api/personnelFiles/retirementWarning";
import { updateEmployee } from "@/api/personnelFiles/incumbentEmployee";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 字典数据
const {
  education_type,
  sys_user_sex,
  certificate_status,
  warning_level,
  personnel_type,
  work_type,
  title_category,
  social_security_status,
} = proxy.useDict(
  "education_type",
  "sys_user_sex",
  "certificate_status",
  "warning_level",
  "personnel_type",
  "work_type",
  "title_category",
  "social_security_status"
);

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 退休预警人员表格数据
const warningList = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined,
  warningStatus: undefined,
  warnLevel: undefined,
});

/** 查询退休预警人员列表 */
function getList() {
  loading.value = true;
  listRetirementWarning(queryParams.value).then((res) => {
    warningList.value = res.rows;
    total.value = res.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 查看按钮操作 */
function handleInfo(row) {
  router.push({
    path: "/personnelFiles/incumbentEmployee/addOrEdit",
    query: { id: row.id, type: "check" },
    meta: { activeMenu: location.pathname },
  });
}

/** 退休按钮操作 */
function handleRetire(row) {
  const employeeId = row.id;
  proxy.$modal
    .confirm('是否确认将"' + row.name + '"办理退休?')
    .then(function () {
      return updateEmployee({
        employeeId: employeeId,
        status: "R",
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("办理退休成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "railway/wlStaffInfo/retireExport",
    {
      ...queryParams.value,
    },
    `退休预警人员_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>
