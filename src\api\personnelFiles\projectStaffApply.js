import request from "@/utils/request";

// 查询项目人员申请列表
export function listProjectStaffApply(params) {
  return request({
    url: "/railway/wlProjectApply/list",
    method: "get",
    params,
  });
}

// 查询项目人员申请详细
export function getProjectStaffApply(id) {
  return request({
    url: "/railway/wlProjectApply/" + id,
    method: "get",
  });
}

// 新增项目人员申请
export function addProjectStaffApply(data) {
  return request({
    url: "/railway/wlProjectApply",
    method: "post",
    data: data,
  });
}

// 修改项目人员申请
export function updateProjectStaffApply(data) {
  return request({
    url: "/railway/wlProjectApply",
    method: "put",
    data: data,
  });
}

// 删除项目人员申请
export function delProjectStaffApply(id) {
  return request({
    url: "/railway/wlProjectApply/" + id,
    method: "delete",
  });
}

// 提交审核
export function submitAudit(data) {
  return request({
    url: "/railway/wlProjectApply/submit",
    method: "post",
    data,
  });
}
