<template>
  <el-card class="box-card" style="height: 100%">
    <div class="card-header">
      <span>{{ chartData.title }}</span>
      <div class="tab-group">
        <div style="display: flex; align-items: center; margin-right: 20px">
          <div>集团内外培训：</div>
          <el-select
            v-model="timeRange"
            placeholder="请选择集团内外培训"
            style="width: 190px"
            @change="handleChange"
            @clear="handleChange"
            clearable
          >
            <el-option label="内" value="1" />
            <el-option label="外" value="0" />
          </el-select>
        </div>
        <div style="display: flex; align-items: center">
          <div>时间日期：</div>
          <el-date-picker
            clearable
            v-model="valueTime"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 250px"
            @change="handleTimeChange"
          />
        </div>
      </div>
    </div>
    <div ref="chartRef" class="chart"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from "vue";
import * as echarts from "echarts/core";
import { BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from "echarts/components";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
  BarChart,
  UniversalTransition,
  CanvasRenderer,
]);

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
});
const params = ref({});
const emit = defineEmits(["change"]);
const chartRef = ref(null);
const timeRange = ref("");
const valueTime = ref("");
let chart = null;

const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        return (
          params[0].name +
          "<br/>" +
          params[0].seriesName +
          ": " +
          params[0].value
        );
      },
    },
    grid: {
      left: "10%",
      right: "10%",
      bottom: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: props.chartData.xAxis,
      axisLabel: {
        interval: 0,
        // rotate: 30,
        fontSize: 12,
      },
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      minInterval: 1,
      type: "value",
      // name: '费用',
      nameTextStyle: {
        color: "#999",
        fontSize: 12,
      },
      axisLabel: {
        formatter: "{value}",
      },
    },
    series: [
      {
        name: "费用",
        type: "bar",
        barWidth: 22,
        label: {
          show: true,
          position: "top", // 展示在柱子的上方
        },
        data: props.chartData.data.map((value) => ({
          value,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#956FFF" },
              { offset: 1, color: "#6F7DFF" },
            ]),
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#7F59FF" },
                { offset: 1, color: "#5467FF" },
              ]),
            },
          },
        })),
      },
    ],
  };

  chart.setOption(option);

  window.addEventListener("resize", () => {
    chart && chart.resize();
  });
};

watch(
  () => props.chartData,
  () => {
    chart && chart.dispose();
    initChart();
  },
  { deep: true }
);

watch(timeRange, (newVal) => {
  // 然后更新图表
  if (chart) {
    initChart();
  }
});
function handleChange(val) {
  params.value.isInterior = val;
  emit("change", params.value);
}
function handleTimeChange(val) {
  params.value.beginDate = val?.[0];
  params.value.endDate = val?.[1];
  emit("change", params.value);
}
onMounted(() => {
  initChart();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: bold;
}

.tab-group {
  display: flex;
  align-items: center;
}

.chart {
  width: 100%;
  height: 350px;
}
</style>
