<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-form
          v-show="showSearch"
          ref="queryForm"
          :model="queryParams"
          :inline="true"
        >
          <el-form-item label="流程名称" prop="templateName">
            <el-input
              v-model="queryParams.templateName"
              placeholder="请输入流程名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索 </el-button>
            <el-button @click="resetQuery"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <el-card class="box-card box-card-no-radius">
        <el-table
          v-loading="loading"
          :data="templateList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="index"
            label="序号"
            width="55"
            align="center"
          />
          <el-table-column
            label="模板名称"
            align="center"
            prop="templateName"
          />
          <el-table-column label="节点数量" align="center" prop="nodeNum" />
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="handleUpdate(scope.row)"
              >
                配置
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div
          v-if="open"
          class="table-wrapper"
          style="
            padding: 41px 34px 24px 60px;
            border-radius: 4px;
            background: transparent;
            border: 1px solid #d8d8d8;
          "
        >
          <div
            style="
              width: 160px;
              height: 35px;
              border-radius: 4px;
              background: #e6f7ff;
              text-align: center;
            "
          >
            <span
              style="
                font-size: 16px;
                text-align: left;
                color: #005aa6;
                line-height: 35px;
              "
              >{{ form.templateName }}</span
            >
          </div>
          <div class="flex flex-col">
            <el-steps class="m-5 overflow-auto pl-[100px]">
              <el-step
                v-for="(item, index) in form.nodeList"
                :key="index"
                class="min-w-[300px]"
              >
                <template #title>
                  <div class="step-title">
                    <div style="min-width: 40px; text-align: center">
                      <span
                        v-if="!item.editing"
                        style="
                          font-weight: Regular;
                          font-size: 16px;
                          text-align: left;
                          color: #005aa6;
                        "
                        >{{ item.nodeName }}
                      </span>
                      <el-input
                        v-else
                        ref="editing"
                        v-model="item.nodeName"
                        v-focus
                        @blur="overEdit(index)"
                      />
                    </div>
                    <div style="margin-left: 8px" class="node-actions">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="添加节点"
                        placement="top"
                      >
                        <el-button
                          class="action-btn"
                          icon="Plus"
                          type="text"
                          @click="addNode(index)"
                        >
                        </el-button>
                      </el-tooltip>
                      <el-dropdown
                        class="step-icon"
                        trigger="click"
                        @visible-change="dropClick"
                      >
                        <el-button
                          v-show="!item.editing"
                          type="text"
                          class="action-btn"
                          icon="Edit"
                        >
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item>
                              <div @click="editNode(index)">编辑节点</div>
                            </el-dropdown-item>
                            <el-dropdown-item v-if="form.nodeList?.length > 1">
                              <div @click="removeNode(index)">删除节点</div>
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </div>
                </template>
                <template #description>
                  <el-form
                    ref="nodeForm"
                    label-position="left"
                    style="
                      font-size: 14px;
                      width: 278px;
                      margin: 20px 0 0 -100px;
                    "
                    label-width="78px"
                    :model="item"
                    :rules="rules"
                  >
                    <el-form-item label="审批人:">
                      <el-select
                        v-model="item.userL"
                        multiple
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入关键词搜索审批人"
                        :remote-method="remoteSearchUsers"
                        :loading="userLoading"
                      >
                        <el-option
                          v-for="ite in searchUserList"
                          :key="ite.userId"
                          :label="ite.nickName"
                          :value="`${ite.userId}`"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="审核方式:">
                      <el-radio-group v-model="item.approvalType">
                        <el-radio label="1">或签</el-radio>
                        <el-radio label="2">会签</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-form>
                </template>
              </el-step>
            </el-steps>
            <div
              style="
                display: flex;
                flex-flow: row nowrap;
                justify-content: center;
              "
            >
              <el-button type="default" @click="cancelForm">取消</el-button>
              <el-button type="primary" @click="submitForm">发布</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import {
  listTemplate,
  getTemplate,
  updateTemplate,
} from '@/api/process/template';
import { listUser } from '@/api/system/user';
import { ElMessage } from 'element-plus';
const { proxy } = getCurrentInstance();
// 节流函数
const throttle = (fn, delay) => {
  let timer = null;
  let lastTime = 0;
  return function (...args) {
    const nowTime = Date.now();
    if (nowTime - lastTime > delay || !lastTime) {
      fn.apply(this, args);
      lastTime = nowTime;
    } else if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, args);
        lastTime = Date.now();
        timer = null;
      }, delay - (nowTime - lastTime));
    }
  };
};

// 响应式状态
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const templateList = ref([]);
const userList = ref([]);
const searchUserList = ref([]);
const userLoading = ref(false);
const title = ref('');
const open = ref(false);
const queryForm = ref(null);
const isOpenDrop = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  templateName: null,
  templateType: null,
  status: null,
});

// 表单参数
const form = ref({
  templateName: '',
  nodeList: [],
});

// 表单校验规则
const rules = {
  templateName: [
    { required: true, message: '模板名称不能为空', trigger: 'blur' },
  ],
  templateType: [
    { required: true, message: '模板类型不能为空', trigger: 'change' },
  ],
  status: [
    {
      required: true,
      message: '状态不能为空',
      trigger: 'blur',
    },
  ],
  delFlag: [
    {
      required: true,
      message: '删除标志不能为空',
      trigger: 'blur',
    },
  ],
  createBy: [{ required: true, message: '创建者不能为空', trigger: 'blur' }],
  createTime: [
    { required: true, message: '创建时间不能为空', trigger: 'blur' },
  ],
};

// 生命周期 created 替换为 onMounted
onMounted(() => {
  getList();
  listUser({ status: '0', pageNum: 1, pageSize: 30 }).then((response) => {
    userList.value = response.rows;
    searchUserList.value = response.rows;
  });
});

// 方法转换为函数
const overEdit = (index) => {
  form.value.nodeList[index] = {
    ...form.value.nodeList[index],
    editing: false,
  };
};

// 获取列表数据
const getList = () => {
  listTemplate(queryParams).then((response) => {
    templateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

// 表单重置
const resetForm = () => {
  form.value.templateName = '';
  form.value.nodeList = [];
};

// 取消按钮
const cancelForm = () => {
  open.value = false;
  resetForm();
};

// 表单提交
const submitForm = () => {
  // 发布前检验必填
  const boo = form.value.nodeList.some((item) => {
    if (!item.nodeName) {
      proxy.$modal.msgError('节点名称不能为空');
      return item;
    }
    if (!item.userL.length) {
      proxy.$modal.msgError('请选择审批人');
      return item;
    }
  });
  if (boo) return;
  // 处理提交前的数据转换
  const submitData = {
    ...form.value,
    nodeList: form.value.nodeList.map((node) => ({
      ...node,
      userIds: node.userL.join(','),
    })),
  };

  // 这里需要处理表单提交逻辑
  updateTemplate(submitData).then((response) => {
    ElMessage.success('操作成功');
    open.value = false;
    getList();
  });
};

// 表单查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 表单重置
const resetQuery = () => {
  if (queryForm.value) {
    queryForm.value.resetFields();
  }
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.templateId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 新增按钮操作
const handleAdd = () => {
  resetForm();
  open.value = true;
  title.value = '添加流程模板';
};

// 编辑按钮操作
const handleUpdate = (row) => {
  resetForm();
  const templateId = row.templateId || 0;
  getTemplate(templateId).then((response) => {
    const data = response.data;
    // 转换数据结构
    form.value.templateName = data.templateName;
    form.value.templateId = data.templateId;
    form.value.nodeList = data.nodeList?.map((node) => ({
      ...node,
      editing: false,
      userL: (node.userIds && node.userIds.split(',')) || [],
    }));
    if (!form.value.nodeList || form.value.nodeList?.length < 1) {
      form.value.nodeList = [
        {
          nodeName: '节点' + (form.value.nodeList?.length + 1),
          nodeNumber: 1,
          userL: [],
          approvalType: '1',
          editing: false,
        },
      ];
    }
    open.value = true;
    title.value = '编辑流程模板';
  });
};

// 下拉菜单可见性变化事件
const dropClick = (visible) => {
  isOpenDrop.value = visible;
};

// 添加节点
const addNode = (index) => {
  const newNode = {
    nodeName: '节点' + (form.value.nodeList.length + 1),
    userL: [],
    approvalType: '1',
    editing: false,
  };

  // 如果有模板ID，添加到新节点
  if (form.value.templateId) {
    newNode.templateId = form.value.templateId;
    newNode.nodeNumber = index + 2;
  }

  form.value.nodeList.splice(index + 1, 0, newNode);
};

// 编辑节点
const editNode = (index) => {
  form.value.nodeList[index] = {
    ...form.value.nodeList[index],
    editing: true,
  };
  nextTick(() => {
    // 这里可能需要定位到编辑的input元素
  });
};

// 删除节点
const removeNode = (index) => {
  if (form.value.nodeList.length > 1) {
    form.value.nodeList.splice(index, 1);
  }
};

// 远程搜索用户
const remoteSearchUsers = throttle((query) => {
  if (query) {
    userLoading.value = true;
    listUser({ username: query, status: '0' })
      .then((response) => {
        searchUserList.value = response.data;
        userLoading.value = false;
      })
      .catch(() => {
        userLoading.value = false;
      });
  } else {
    searchUserList.value = userList.value;
  }
}, 500);
</script>

<style lang="scss" scoped>
.step-title {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  position: relative;
  height: 40px;

  .node-actions {
    display: flex;
    align-items: center;

    .action-btn {
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .step-icon {
    margin: 0 0 0 4px;

    &.active {
      display: block;
    }
  }

  &:hover {
    color: black;
  }
}

.icon-text {
  position: absolute;
  font-size: 18px;
  color: white;
  width: 50px;
  height: 50px;
  left: -12px;
  top: -17px;
  line-height: 50px;
  text-align: center;
}
</style>
