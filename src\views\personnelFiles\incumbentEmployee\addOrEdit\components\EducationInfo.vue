<template>
  <div class="education-info">
    <div class="flex justify-between mb-4">
      <div class="font-bold text-xl">学历信息</div>
      <div>
        <!-- <el-button type="primary" @click="handleExport">学历信息导出</el-button> -->
      </div>
    </div>
    <div v-if="!isEdit">
      <div v-if="educationList.length === 0" class="info-content">
        <Empty text="暂无学历信息" />
      </div>
      <div v-else v-for="(item, index) in educationList" :key="index">
        <div class="indexTitle">
          <span>学历{{ numberToChinese(index + 1) }}</span>
        </div>
        <div class="info-content">
          <div class="education-block">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">开始时间：</span>
                  <span>{{ item.beginDate || "--" }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">结束时间：</span>
                  <span>{{ item.endTime || "--" }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">所读学校：</span>
                  <span>{{ item.school || "--" }}</span>
                </div>
              </el-col>

              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">学历：</span>
                  <span>{{
                    getDictLabel(education_type, item.degree) || "--"
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <div class="info-item">
                  <span class="label">所学专业：</span>
                  <span>{{ item.major || "--" }}</span>
                </div>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24">
                <div class="info-item">
                  <span class="label">详细描述：</span>
                  <span>{{ item.remark || "--" }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑表单 -->
    <div v-if="isEdit">
      <div v-for="(item, index) in educationList" :key="index">
        <div class="indexTitle">
          <span>学历{{ numberToChinese(index + 1) }}</span>
          <el-button type="primary" link @click="handleAdd">新增</el-button>
          <el-button
            v-if="educationList.length > 1"
            type="danger"
            link
            @click="handleDelete(item, index)"
            >删除</el-button
          >
          <el-button type="success" link @click="handleSave(item, index)"
            >保存</el-button
          >
        </div>
        <el-form
          :ref="(el) => (formRefs[index] = el)"
          :model="item"
          :rules="rules"
          label-width="100px"
          class="education-form"
        >
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="开始时间" prop="beginDate">
                <el-date-picker
                  v-model="item.beginDate"
                  type="month"
                  placeholder="请选择开始时间"
                  style="width: 100%"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker
                  v-model="item.endTime"
                  type="month"
                  placeholder="请选择结束时间"
                  style="width: 100%"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="所读学校" prop="school">
                <el-input v-model="item.school" placeholder="请输入所读学校" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="学历" prop="degree">
                <el-select
                  v-model="item.degree"
                  placeholder="请选择学历"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in education_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="所学专业" prop="major">
                <el-input v-model="item.major" placeholder="请输入所学专业" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24">
              <el-form-item label="详细描述" prop="remark">
                <el-input
                  v-model="item.remark"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入详细描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import {
  listWlStaffDegree,
  addWlStaffDegree,
  updateWlStaffDegree,
  delWlStaffDegree,
} from "@/api/personnelFiles/incumbentEmployee";
import { useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";

const { proxy } = getCurrentInstance();
const route = useRoute();

const educationList = ref([]);
const isEdit = route.query.type === "check" ? false : true;
const formRefs = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  staffId: route.query.id,
});

// 表单校验规则
const rules = ref({
  beginDate: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  degree: [{ required: true, message: "请选择学历", trigger: "change" }],
  school: [{ required: true, message: "请输入所读学校", trigger: "blur" }],
  major: [{ required: true, message: "请输入所学专业", trigger: "blur" }],
});

// 字典数据
const { education_type } = proxy.useDict("education_type");

// 数字转中文
function numberToChinese(num) {
  const unit = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  const digit = ["", "十", "百", "千", "万", "亿"];

  const numStr = num.toString();
  const len = numStr.length;
  let chinese = "";

  for (let i = 0; i < len; i++) {
    const currentNum = parseInt(numStr[i]);
    chinese += unit[currentNum] + digit[len - i - 1];
  }
  return chinese;
}
// 获取字典标签
function getDictLabel(dict, value) {
  const item = dict.find((d) => d.value === value);
  return item ? item.label : "";
}

/** 获取学历列表 */
function getList() {
  listWlStaffDegree(queryParams.value).then((response) => {
    if (response.code === 200) {
      educationList.value = response.data || [];
      if (educationList.value.length === 0 && isEdit) {
        // 如果没有数据，添加一个空对象作为默认表单
        handleAdd();
      }
    }
  });
}

/** 新增按钮操作 */
function handleAdd() {
  const newEducation = {
    beginDate: null,
    endTime: null,
    degree: null,
    major: null,
    school: null,
    remark: null,
    staffId: route.query.id,
  };
  educationList.value.push(newEducation);
}

/** 删除按钮操作 */
function handleDelete(item, index) {
  if (item.id) {
    // 如果有id，说明是已保存的数据，需要调用接口删除
    ElMessageBox.confirm("是否确认删除该学历信息？", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        delWlStaffDegree(item.id).then((response) => {
          if (response.code === 200) {
            ElMessage.success("删除成功");
            educationList.value.splice(index, 1);
            // 如果删除后没有数据，添加一个空表单
            if (educationList.value.length === 0) {
              handleAdd();
            }
          }
        });
      })
      .catch(() => {});
  } else {
    // 如果没有id，直接从列表中删除
    educationList.value.splice(index, 1);
    // 如果删除后没有数据，添加一个空表单
    if (educationList.value.length === 0) {
      handleAdd();
    }
  }
}

/** 保存按钮操作 */
function handleSave(item, index) {
  const formRef = formRefs.value[index];
  formRef?.validate((valid) => {
    if (valid) {
      // 检验结束时间必须大于开始时间
      if (new Date(item.beginDate) >= new Date(item.endTime)) {
        ElMessage.error("结束时间必须大于开始时间");
        return;
      }
      const submitFunction = item.id ? updateWlStaffDegree : addWlStaffDegree;
      submitFunction(item).then((response) => {
        if (response.code === 200) {
          ElMessage.success(item.id ? "修改成功" : "新增成功");
          // 更新当前项的id（如果是新增的话）
          if (!item.id && response.data) {
            item.id = response.data.id;
          }
        }
      });
    }
  });
}

/** 导出按钮操作 */
function handleExport() {
  if (route.query.id) {
    proxy.download(
      "/railway/wlStaffDegree/export",
      {
        ...queryParams.value,
      },
      `学历信息_${new Date().getTime()}.xlsx`
    );
  }
}

onMounted(() => {
  if (route.query.id) {
    getList();
  }
});
</script>

<style scoped lang="scss">
.education-info {
  padding: 20px;
}

.info-content {
  background-color: #f3f7fc;
  padding: 30px 5%;
  margin-bottom: 20px;
}

.education-block {
  position: relative;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  .label {
    min-width: 100px;
    text-align: right;
    color: #606266;
    padding-right: 10px;
  }
}

.operation-buttons {
  position: absolute;
  top: 0;
  right: 0;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}

.indexTitle {
  margin: 20px 0;
  span {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #2674fe;
    background-color: #e6efff;
    padding: 10px 20px;
    border-radius: 4px;
    margin-right: 20px;
  }
}

.education-form {
  // background-color: #fff;
  padding: 20px;
  // border-radius: 4px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-buttons {
  text-align: center;
  margin-top: 20px;
}
</style>
