import request from "@/utils/request";

// 查询流程模板列表
export function listTemplate(query) {
  return request({
    url: "/flow/template/list",
    method: "get",
    params: query,
  });
}

// 查询流程模板详细
export function getTemplate(templateId) {
  return request({
    url: "/flow/template/" + templateId,
    method: "get",
  });
}

// 新增流程模板
export function addTemplate(data) {
  return request({
    url: "/flow/template",
    method: "post",
    data: data,
  });
}

// 编辑流程模板
export function updateTemplate(data) {
  return request({
    url: "/flow/template",
    method: "put",
    data: data,
  });
}

// 删除流程模板
export function delTemplate(templateId) {
  return request({
    url: "/flow/template/" + templateId,
    method: "delete",
  });
}

// 查询我的待审流程列表
export function listMyPendingProcess(query) {
  return request({
    url: "/flow/instance/my/pending/list",
    method: "get",
    params: query,
  });
}

// 审核流程
export function auditProcess(data) {
  return request({
    url: "/flow/instance/audit",
    method: "post",
    data: data,
  });
}
