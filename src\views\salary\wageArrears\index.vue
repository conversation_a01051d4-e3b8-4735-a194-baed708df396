<template>
  <div class="app-container">
    <div class="main-box-card">
      <el-card class="box-card">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane key="all" label="全部" name="all" />
          <el-tab-pane
            v-for="item in personnel_type"
            :key="item.value"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>

        <!-- 搜索表单 -->
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
        >
          <el-form-item label="时间" prop="yearMonth">
            <el-date-picker
              v-model="monthrangeDate"
              type="monthrange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              class="w-[200px]"
              clearable
            />
          </el-form-item>
          <el-form-item label="单位名称" prop="projectId">
            <RemoteSelect
              v-model="queryParams.projectId"
              url="/system/dept/list"
              labelKey="deptName"
              valueKey="deptId"
              responsePath="data"
              placeholder="请选择单位名称"
              clearable
              class="w-[200px]"
              :extraParams="{ parentId: '0' }"
            />
          </el-form-item>
          <el-form-item label="建设状态" prop="constructionStatus">
            <el-select
              v-model="queryParams.constructionStatus"
              placeholder="请选择建设状态"
              class="w-[200px]"
              clearable
            >
              <el-option
                v-for="dict in construction_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="机构属性" prop="orgAttribute">
            <el-select
              v-model="queryParams.orgAttribute"
              placeholder="请选择机构属性"
              class="w-[200px]"
              clearable
            >
              <el-option
                v-for="dict in organization_attribute"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery" class="reset-btn">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      <el-card class="box-card box-card-no-radius">
        <!-- 统计数据展示 -->
        <div class="statistics-container">
          <div class="statistics-grid">
            <div class="stat-item">
              <div class="stat-title">欠薪人数</div>
              <div class="stat-content">
                <span class="stat-value">{{ statisticsData.staffNumber }}</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-title">拖欠金额</div>
              <div class="stat-content">
                <span class="stat-value">{{ statisticsData.totalCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" class="custom-btn" @click="handleExport">
              导出
            </el-button>
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="personnelList" border>
          <el-table-column type="index" label="序号" width="60" align="center">
            <template #default="scope">
              <span>{{ getIndex(scope.$index) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="所在单位"
            align="center"
            prop="projectName"
            min-width="120"
          />
          <el-table-column
            label="建设状态"
            align="center"
            prop="constructionStatus"
          >
            <template #default="scope">
              <dict-tag
                :options="construction_status"
                :value="scope.row.constructionStatus"
              />
            </template>
          </el-table-column>
          <el-table-column label="欠薪人数" align="center" prop="staffNumber" />
          <el-table-column label="欠薪月数" align="center" prop="monthNumber" />
          <el-table-column
            label="拖欠具体月份"
            align="center"
            prop="yearMonth"
            show-overflow-tooltip
          />
          <el-table-column label="拖欠实发金额" align="center" prop="total" />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { getArrearsList } from "@/api/salary/projectOrgSalary";

const { proxy } = getCurrentInstance();

const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const personnelList = ref([]);
const monthrangeDate = ref([]);
const activeTab = ref("all");

const { construction_status, organization_attribute, personnel_type } =
  proxy.useDict(
    "construction_status",
    "organization_attribute",
    "personnel_type"
  );

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  yearMonth: undefined,
  constructionStatus: undefined,
  orgAttribute: undefined,
  positionStatus: undefined,
});

const statisticsData = ref({
  projectCount: 5,
  totalCount: 5,
  workerCount: 5,
  tianchengCount: 0,
});

function handleTabClick(tab, event) {
  activeTab.value = tab.props.name;

  queryParams.value.pageNum = 1;
  getList();
}

function getList() {
  loading.value = true;
  const [beginMonth, endMonth] = monthrangeDate.value;
  queryParams.value.type =
    activeTab.value === "all" ? undefined : activeTab.value;
  getArrearsList({
    ...queryParams.value,
    beginMonth,
    endMonth,
  }).then((response) => {
    personnelList.value = response.data;
    statisticsData.value.staffNumber = response.staffNumber;
    statisticsData.value.totalCount = response.total;
    loading.value = false;
  });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryForm");
  monthrangeDate.value = [];
  handleQuery();
}

function getIndex(index) {
  return (
    (queryParams.value.pageNum - 1) * queryParams.value.pageSize + index + 1
  );
}

function handleExport() {
  proxy.download(
    "/railway/wlProjectOrgSalary/export/arrears",
    { ...queryParams.value },
    `拖欠工资统计_${new Date().getTime()}.xlsx`
  );
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.statistics-container {
  padding: 16px 0;
}

.statistics-grid {
  display: flex;
  gap: 12px;
}

.stat-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  width: 400px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.stat-content {
  font-size: 12px;
  color: #333;
}

.stat-value {
  font-weight: 700;
  color: #2f7bff;
  font-size: 20px;
  margin-right: 4px;
}

.stat-label {
  color: #999;
  font-size: 12px;
}
</style>
