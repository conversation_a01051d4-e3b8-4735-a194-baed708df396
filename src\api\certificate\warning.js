import request from '@/utils/request';

// 特种作业证书预警接口

/**
 * 查询特种作业证书预警列表
 * @param {Object} query 查询参数
 * @returns {Promise}
 */
export function listSpecialWarning(query) {
  return request({
    url: '/railway/wlCertificateSpecial/warn/list',
    method: 'get',
    params: query,
  });
}

/**
 * 查询特种作业证书预警详细
 * @param {number|string} id 预警ID
 * @returns {Promise}
 */
export function getSpecialWarning(id) {
  return request({
    url: '/railway/wlCertificateSpecialWarning/' + id,
    method: 'get',
  });
}

/**
 * 新增特种作业证书预警
 * @param {Object} data 预警数据
 * @returns {Promise}
 */
export function addSpecialWarning(data) {
  return request({
    url: '/railway/wlCertificateSpecialWarning',
    method: 'post',
    data: data,
  });
}

/**
 * 修改特种作业证书预警
 * @param {Object} data 预警数据
 * @returns {Promise}
 */
export function updateSpecialWarning(data) {
  return request({
    url: '/railway/wlCertificateSpecialWarning',
    method: 'put',
    data: data,
  });
}

/**
 * 删除特种作业证书预警
 * @param {number|string} id 预警ID
 * @returns {Promise}
 */
export function delSpecialWarning(id) {
  return request({
    url: '/railway/wlCertificateSpecialWarning/' + id,
    method: 'delete',
  });
}

// 注册/岗位证书预警接口

/**
 * 查询注册/岗位证书预警列表
 * @param {Object} query 查询参数
 * @returns {Promise}
 */
export function listEnrollWarning(query) {
  return request({
    url: '/railway/wlCertificateEnroll/warn/list',
    method: 'get',
    params: query,
  });
}

/**
 * 查询注册/岗位证书预警详细
 * @param {number|string} id 预警ID
 * @returns {Promise}
 */
export function getEnrollWarning(id) {
  return request({
    url: '/railway/wlCertificateEnrollWarning/' + id,
    method: 'get',
  });
}

/**
 * 新增注册/岗位证书预警
 * @param {Object} data 预警数据
 * @returns {Promise}
 */
export function addEnrollWarning(data) {
  return request({
    url: '/railway/wlCertificateEnrollWarning',
    method: 'post',
    data: data,
  });
}

/**
 * 修改注册/岗位证书预警
 * @param {Object} data 预警数据
 * @returns {Promise}
 */
export function updateEnrollWarning(data) {
  return request({
    url: '/railway/wlCertificateEnrollWarning',
    method: 'put',
    data: data,
  });
}

/**
 * 删除注册/岗位证书预警
 * @param {number|string} id 预警ID
 * @returns {Promise}
 */
export function delEnrollWarning(id) {
  return request({
    url: '/railway/wlCertificateEnrollWarning/' + id,
    method: 'delete',
  });
}

/**
 * 证书延期
 * @param {Object} data 延期数据
 * @returns {Promise}
 */
export function extendEnrollWarning(data) {
  return request({
    url: '/railway/wlCertificateEnrollWarning/extend',
    method: 'post',
    data: data,
  });
}

// 获取列表数据
export function getList(url, params) {
  return request({
    url: url,
    method: 'get',
    params,
  });
}
