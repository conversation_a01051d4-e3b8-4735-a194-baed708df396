import request from "@/utils/request";

// 查询项目机构薪酬列表
export function listProjectOrgSalary(query) {
  return request({
    url: "/railway/wlProjectOrgSalary/list",
    method: "get",
    params: query,
  });
}

// 查询薪酬详情列表
export function listSalaryDetails(query) {
  return request({
    url: "/railway/salaryDetails/list",
    method: "get",
    params: query,
  });
}

// 查询薪酬预警列表
export function listProjectOrgSalaryWarn(query) {
  return request({
    url: "/railway/wlProjectOrgSalary/warn",
    method: "get",
    params: query,
  });
}

// 处理薪酬
export function processSalary(data) {
  return request({
    url: "/railway/wlProjectOrgSalary/dispose",
    method: "post",
    data,
  });
}

// 上传工资凭证
export function saveVoucher(data) {
  return request({
    url: "/railway/wlProjectOrgSalary/saveVoucher",
    method: "post",
    data,
  });
}

// 重新拖欠工资统计
export function getArrearsList(params) {
  return request({
    url: "/railway/wlProjectOrgSalary/arrears",
    method: "get",
    params,
  });
}
