import request from "@/utils/request";

// 年龄结构数据
export function getAgeStructure(params) {
  return request({
    url: "/dashboard/ageStructure",
    method: "get",
    params,
  });
}

// 学历数据
export function getDegreeData(params) {
  return request({
    url: "/dashboard/degree",
    method: "get",
    params,
  });
}

// 委培评价数据
export function getDeputeAnalyseData(params) {
  return request({
    url: "/dashboard/depute/analyse",
    method: "get",
    params,
  });
}

// 委培费用排名数据
export function getDeputeCostData(params) {
  return request({
    url: "/dashboard/depute/cost",
    method: "get",
    params,
  });
}

// 职务级别分布数据
export function getJobStructureData(params) {
  return request({
    url: "/dashboard/jobStructure",
    method: "get",
    params,
  });
}

// 劳动生产率统计排名数据
export function getProductivityData(params) {
  return request({
    url: "/dashboard/productivity",
    method: "get",
    params,
  });
}

// 退休人员预测与分析曲线数据
export function getRetireLineData(params) {
  return request({
    url: "/dashboard/retireLine",
    method: "get",
    params,
  });
}

// 退休人员预测与分析列表数据
export function getRetireListData(params) {
  return request({
    url: "/dashboard/retireList",
    method: "get",
    params,
  });
}

// 自培信息统计与分析数据
export function getSelfAnalyseData(params) {
  return request({
    url: "/dashboard/self/analyse",
    method: "get",
    params,
  });
}

// 技术职称及工作情况数据
export function getSkillsData(params) {
  return request({
    url: "/dashboard/skillsData",
    method: "get",
    params,
  });
}
